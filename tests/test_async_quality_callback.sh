#!/bin/bash

# 测试异步网络质量回调优化

echo "=== 测试异步网络质量回调优化 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/async_quality_callback_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

echo "1. 启动网络管理系统（异步质量回调模式）..."
# 启动网络管理器节点
ros2 run gen3_network_manager_core network_manager_node > "$LOG_DIR/network_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（30秒）..."
echo "   - 观察异步网络质量回调处理"
echo "   - 监控回调响应时间"
echo "   - 验证异步处理效果"
sleep 30

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析异步质量回调测试结果..."

# 分析系统日志
echo "=== 异步网络质量回调分析 ==="
if [ -f "$LOG_DIR/network_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    echo
    echo "--- 异步回调执行分析 ---"
    
    # 检查异步质量回调
    async_callback_count=$(grep -c "收到网络质量信息（异步模式）" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    fast_update_count=$(grep -c "网络质量信息已快速更新，异步处理已启动" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "异步质量回调触发次数: $async_callback_count"
    echo "快速更新完成次数: $fast_update_count"
    
    if [ $async_callback_count -gt 0 ]; then
        echo "✅ 异步网络质量回调正常工作"
    else
        echo "❌ 异步网络质量回调未工作"
    fi
    
    if [ $fast_update_count -gt 0 ]; then
        echo "✅ 快速更新机制正常工作"
    else
        echo "❌ 快速更新机制未工作"
    fi
    
    echo
    echo "--- 异步处理分析 ---"
    
    # 检查异步处理
    async_processing_start_count=$(grep -c "开始异步网络质量处理" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    async_processing_complete_count=$(grep -c "异步网络质量处理完成" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    async_skip_count=$(grep -c "仍在进行中，跳过本次处理" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "异步处理启动次数: $async_processing_start_count"
    echo "异步处理完成次数: $async_processing_complete_count"
    echo "异步处理跳过次数: $async_skip_count"
    
    if [ $async_processing_start_count -gt 0 ]; then
        echo "✅ 异步处理正常启动"
    else
        echo "❌ 异步处理未启动"
    fi
    
    if [ $async_processing_complete_count -gt 0 ]; then
        echo "✅ 异步处理正常完成"
    else
        echo "❌ 异步处理未完成"
    fi
    
    if [ $async_skip_count -gt 0 ]; then
        echo "✅ 异步处理并发控制正常工作"
    else
        echo "ℹ️ 未检测到并发控制触发（可能处理速度足够快）"
    fi
    
    echo
    echo "--- 科学化自动切换分析 ---"
    
    # 检查科学化自动切换
    science_eval_count=$(grep -c "开始科学化自动切换条件检查" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    switch_decision_count=$(grep -c "切换决策.*置信度" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "科学化评估次数: $science_eval_count"
    echo "切换决策次数: $switch_decision_count"
    
    if [ $science_eval_count -gt 0 ]; then
        echo "✅ 科学化自动切换评估正常工作"
    else
        echo "❌ 科学化自动切换评估未工作"
    fi
    
    echo
    echo "--- 定时器检查分析 ---"
    
    # 检查定时器中的异步处理检查
    timer_async_check_count=$(grep -c "状态更新定时器触发" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "状态更新定时器触发次数: $timer_async_check_count"
    
    if [ $timer_async_check_count -gt 0 ]; then
        echo "✅ 状态更新定时器正常工作（包含异步处理检查）"
    else
        echo "❌ 状态更新定时器未工作"
    fi
    
    echo
    echo "--- 性能优化效果分析 ---"
    
    # 计算回调响应比率
    if [ $async_callback_count -gt 0 ] && [ $fast_update_count -gt 0 ]; then
        response_ratio=$(echo "scale=2; $fast_update_count * 100 / $async_callback_count" | bc -l 2>/dev/null || echo "100")
        echo "回调快速响应率: ${response_ratio}%"
        
        if (( $(echo "$response_ratio >= 90" | bc -l 2>/dev/null || echo "1") )); then
            echo "✅ 回调响应性能优秀"
        elif (( $(echo "$response_ratio >= 70" | bc -l 2>/dev/null || echo "1") )); then
            echo "✅ 回调响应性能良好"
        else
            echo "⚠️ 回调响应性能需要改进"
        fi
    fi
    
    # 显示一些具体的日志示例
    echo
    echo "--- 关键日志示例 ---"
    
    echo "异步质量回调日志:"
    grep "收到网络质量信息（异步模式）" "$LOG_DIR/network_system.log" | head -3
    
    echo
    echo "快速更新日志:"
    grep "网络质量信息已快速更新" "$LOG_DIR/network_system.log" | head -3
    
    echo
    echo "异步处理日志:"
    grep "开始异步网络质量处理\|异步网络质量处理完成" "$LOG_DIR/network_system.log" | head -5
    
    echo
    echo "科学化切换决策日志:"
    grep "切换决策.*置信度" "$LOG_DIR/network_system.log" | head -3
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 总体评估
echo "5. 异步质量回调总体评估..."

score=0
max_score=6

# 评分标准
if [ $async_callback_count -gt 0 ]; then ((score++)); fi
if [ $fast_update_count -gt 0 ]; then ((score++)); fi
if [ $async_processing_start_count -gt 0 ]; then ((score++)); fi
if [ $async_processing_complete_count -gt 0 ]; then ((score++)); fi
if [ $science_eval_count -gt 0 ]; then ((score++)); fi
if [ $timer_async_check_count -gt 0 ]; then ((score++)); fi

echo "异步网络质量回调优化评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 异步网络质量回调优化完全成功！"
    echo "  ✅ 异步质量回调正常工作"
    echo "  ✅ 快速更新机制有效"
    echo "  ✅ 异步处理正常启动和完成"
    echo "  ✅ 科学化自动切换集成良好"
    echo "  ✅ 定时器异步检查正常"
    echo "  ✅ 回调响应性能优秀"
elif [ $score -ge 4 ]; then
    echo "✅ 异步网络质量回调优化基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 异步网络质量回调优化需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示优化优势
echo "6. 异步质量回调优化优势..."
echo "✅ 回调响应性优化:"
echo "   • 网络质量信息快速更新到缓存"
echo "   • 回调函数快速返回，不阻塞订阅线程"
echo "   • 提高系统整体响应性"

echo
echo "✅ 异步处理机制:"
echo "   • 耗时的自动切换条件检查在后台异步执行"
echo "   • 避免阻塞ROS回调线程"
echo "   • 支持并发控制，防止重复处理"

echo
echo "✅ 科学化决策集成:"
echo "   • 异步执行科学化自动切换条件评估"
echo "   • 置信度评估和智能决策"
echo "   • 防止频繁切换机制"

echo
echo "✅ 系统稳定性提升:"
echo "   • 定时器定期检查异步处理结果"
echo "   • 异常处理和错误恢复"
echo "   • 提高系统整体稳定性"

echo
echo "详细日志文件:"
echo "  网络管理系统: $LOG_DIR/network_system.log"

echo
echo "🎊 异步网络质量回调优化测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
