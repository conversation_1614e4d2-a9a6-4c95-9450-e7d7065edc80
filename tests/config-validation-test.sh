#!/bin/bash

# 测试配置文件加载的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 测试配置文件加载 ===${NC}"

# 检查配置文件是否存在
CONFIG_FILES=(
    "src/gen3_network_manager_core/config/network_config.yaml"
    "src/gen3_network_manager_core/config/development_config.yaml"
    "src/gen3_network_manager_core/config/production_config.yaml"
)

echo -e "${BLUE}1. 检查配置文件是否存在${NC}"
for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo -e "${GREEN}✓${NC} $config_file 存在"
    else
        echo -e "${RED}✗${NC} $config_file 不存在"
    fi
done

# 检查配置文件语法
echo -e "\n${BLUE}2. 检查配置文件语法${NC}"
for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo -e "${YELLOW}检查${NC} $config_file"
        if python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} YAML语法正确"
        else
            echo -e "${RED}✗${NC} YAML语法错误"
        fi
    fi
done

# 显示配置文件内容摘要
echo -e "\n${BLUE}3. 配置文件参数摘要${NC}"
for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo -e "\n${YELLOW}=== $(basename $config_file) ===${NC}"
        
        # 提取关键参数
        echo "定时器间隔配置:"
        grep -E "(network_check_interval|quality_check_interval|status_update_interval)" "$config_file" | sed 's/^/  /'
        
        echo "网络功能配置:"
        grep -E "(enable_auto_switch|preferred_network_type|min_signal_strength)" "$config_file" | sed 's/^/  /'
        
        echo "网络接口配置:"
        grep -E "(wifi_interface|ethernet_interface|5g_interface)" "$config_file" | sed 's/^/  /'
    fi
done

# 构建项目
echo -e "\n${BLUE}4. 构建项目以测试配置加载${NC}"
if [ -f "build.sh" ]; then
    echo -e "${YELLOW}执行构建...${NC}"
    ./build.sh
    BUILD_RESULT=$?
    
    if [ $BUILD_RESULT -eq 0 ]; then
        echo -e "${GREEN}✓${NC} 构建成功"
        
        # 检查安装目录中的配置文件
        echo -e "\n${BLUE}5. 检查安装目录中的配置文件${NC}"
        INSTALL_DIRS=(
            "install/aarch64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config"
            "install/x86_64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config"
        )
        
        for install_dir in "${INSTALL_DIRS[@]}"; do
            if [ -d "$install_dir" ]; then
                echo -e "${GREEN}✓${NC} 找到安装目录: $install_dir"
                ls -la "$install_dir"
            else
                echo -e "${YELLOW}!${NC} 安装目录不存在: $install_dir"
            fi
        done
        
    else
        echo -e "${RED}✗${NC} 构建失败"
    fi
else
    echo -e "${RED}✗${NC} 找不到 build.sh 脚本"
fi

echo -e "\n${BLUE}=== 测试完成 ===${NC}"
