#!/bin/bash

# 测试低延迟优化效果

echo "=== ROS2 低延迟优化测试 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# 构建项目
echo "1. 构建项目..."
cd /mine/note/Code/ROS/Network99
./build.sh

if [ $? -ne 0 ]; then
    echo "❌ 项目构建失败"
    exit 1
fi

echo "✅ 项目构建成功"

# Source 工作空间
source install/x86_64/Debug/setup.bash

echo
echo "2. 测试低延迟配置..."

# 创建测试日志目录
LOG_DIR="/tmp/low_latency_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"

# 启动网络管理器（后台）
echo "启动网络管理器..."
ros2 run gen3_network_manager_core network_manager_node > "$LOG_DIR/network_manager.log" 2>&1 &
MANAGER_PID=$!

# 等待网络管理器启动
sleep 3

# 启动网络切换器（后台）
echo "启动网络切换器..."
ros2 run gen3_network_manager_core network_switch_node > "$LOG_DIR/network_switch.log" 2>&1 &
SWITCH_PID=$!

# 等待网络切换器启动
sleep 3

echo "等待系统稳定运行（10秒）..."
sleep 10

# 停止所有进程
echo "停止测试进程..."
kill $MANAGER_PID $SWITCH_PID 2>/dev/null
wait $MANAGER_PID $SWITCH_PID 2>/dev/null

echo
echo "3. 分析测试结果..."

# 检查网络管理器日志
if [ -f "$LOG_DIR/network_manager.log" ]; then
    echo "=== 网络管理器日志分析 ==="
    
    # 检查超低延迟QoS配置
    if grep -q "超低延迟QoS配置" "$LOG_DIR/network_manager.log"; then
        echo "✅ 网络管理器已应用超低延迟QoS配置"
    else
        echo "❌ 网络管理器未应用超低延迟QoS配置"
    fi
    
    # 检查发布次数
    pub_count=$(grep -c "发布\|publish\|PUB" "$LOG_DIR/network_manager.log" 2>/dev/null || echo "0")
    echo "网络管理器发布次数: $pub_count"
    
    # 检查初始化定时器
    if grep -q "一次性初始化定时器触发" "$LOG_DIR/network_manager.log"; then
        echo "✅ 一次性初始化定时器正常触发"
    else
        echo "❌ 一次性初始化定时器未触发"
    fi
else
    echo "❌ 网络管理器日志文件不存在"
fi

echo

# 检查网络切换器日志
if [ -f "$LOG_DIR/network_switch.log" ]; then
    echo "=== 网络切换器日志分析 ==="
    
    # 检查超低延迟QoS配置
    if grep -q "超低延迟QoS配置" "$LOG_DIR/network_switch.log"; then
        echo "✅ 网络切换器已应用超低延迟QoS配置"
    else
        echo "❌ 网络切换器未应用超低延迟QoS配置"
    fi
    
    # 检查订阅消息
    sub_count=$(grep -c "收到网络状态更新\|SUB" "$LOG_DIR/network_switch.log" 2>/dev/null || echo "0")
    echo "网络切换器接收消息次数: $sub_count"
    
    # 检查初始化定时器
    if grep -q "一次性初始化定时器触发" "$LOG_DIR/network_switch.log"; then
        echo "✅ 一次性初始化定时器正常触发"
    else
        echo "❌ 一次性初始化定时器未触发"
    fi
else
    echo "❌ 网络切换器日志文件不存在"
fi

echo
echo "4. 延迟优化效果评估..."

# 计算消息传递成功率
if [ $pub_count -gt 0 ] && [ $sub_count -gt 0 ]; then
    if command -v bc >/dev/null 2>&1; then
        success_rate=$(echo "scale=1; $sub_count * 100 / $pub_count" | bc -l)
        echo "消息传递成功率: ${success_rate}%"
        
        if [ $(echo "$success_rate >= 80" | bc -l) -eq 1 ]; then
            echo "✅ 消息传递成功率优秀"
        elif [ $(echo "$success_rate >= 60" | bc -l) -eq 1 ]; then
            echo "⚠️ 消息传递成功率良好"
        else
            echo "❌ 消息传递成功率需要改进"
        fi
    else
        echo "消息传递: 发布 $pub_count 次，接收 $sub_count 次"
    fi
else
    echo "❌ 消息传递测试失败"
fi

echo
echo "5. 低延迟优化配置验证..."

# 检查QoS配置
qos_optimizations=0

if grep -q "超低延迟QoS" "$LOG_DIR"/*.log 2>/dev/null; then
    echo "✅ 超低延迟QoS配置已应用"
    ((qos_optimizations++))
fi

if grep -q "BestEffort\|Volatile" "$LOG_DIR"/*.log 2>/dev/null; then
    echo "✅ 低延迟QoS策略已启用"
    ((qos_optimizations++))
fi

# 检查回调优化
if grep -q "减少日志输出以降低延迟" "$LOG_DIR"/*.log 2>/dev/null; then
    echo "✅ 回调函数延迟优化已应用"
    ((qos_optimizations++))
fi

echo
echo "6. 总体评估..."

total_score=0
max_score=5

# 评分标准
if [ -f "$LOG_DIR/network_manager.log" ]; then ((total_score++)); fi
if [ -f "$LOG_DIR/network_switch.log" ]; then ((total_score++)); fi
if [ $pub_count -gt 0 ]; then ((total_score++)); fi
if [ $sub_count -gt 0 ]; then ((total_score++)); fi
if [ $qos_optimizations -gt 0 ]; then ((total_score++)); fi

echo "总体评分: $total_score/$max_score"

if [ $total_score -eq $max_score ]; then
    echo "🎉 低延迟优化测试完全成功！"
    echo "  • 所有组件正常启动"
    echo "  • 消息传递正常工作"
    echo "  • 低延迟配置已应用"
elif [ $total_score -ge 3 ]; then
    echo "✅ 低延迟优化测试基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调配置"
else
    echo "❌ 低延迟优化测试需要改进"
    echo "  • 请检查配置和日志"
fi

echo
echo "7. 延迟优化建议..."

echo "已应用的优化:"
echo "  • 超低延迟QoS配置 (队列深度=1, BestEffort, Volatile)"
echo "  • 减少回调函数处理时间"
echo "  • 优化日志输出级别"

echo
echo "进一步优化建议:"
echo "  • 使用实时调度策略 (需要root权限)"
echo "  • 设置CPU亲和性"
echo "  • 锁定内存页面"
echo "  • 使用专用执行器"

echo
echo "详细日志文件:"
echo "  网络管理器: $LOG_DIR/network_manager.log"
echo "  网络切换器: $LOG_DIR/network_switch.log"

echo
echo "🎊 低延迟优化测试完成！"

# 清理进程
pkill -f "network_manager_node\|network_switch_node" 2>/dev/null
