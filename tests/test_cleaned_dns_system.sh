#!/bin/bash

# 测试清理后的DNS管理系统

echo "=== 测试清理后的DNS管理系统 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/cleaned_dns_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 创建清理后的DNS测试参数文件
cat > "$LOG_DIR/cleaned_dns_params.yaml" << EOF
network_manager_node:
  ros__parameters:
    # DNS管理配置
    dns_check_interval: 12.0
    dns_test_timeout_ms: 4000
    dns_max_consecutive_failures: 2
    dns_priority_update_threshold: 0.8
    
    # 中国网络环境DNS服务器配置
    dns_servers:
      china_primary: ["114.114.114.114", "223.5.5.5"]
      china_secondary: ["119.29.29.29", "180.76.76.76"]
      china_backup: ["1.2.4.8", "210.2.4.8"]
      international: ["8.8.8.8", "1.1.1.1", "208.67.222.222"]
    
    # 中国网络检测配置
    china_network:
      enable_detection: true
      detection_interval: 300.0
      prefer_domestic_dns: true
      domestic_test_sites: ["www.baidu.com", "www.qq.com", "www.163.com"]
      international_test_sites: ["www.google.com", "github.com", "www.cloudflare.com"]
    
    # 其他网络管理配置
    network_check_interval: 8.0
    connectivity_check_interval: 15.0
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "usb0"
EOF

echo "1. 启动清理后的DNS管理系统..."
# 启动网络管理器节点
ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$LOG_DIR/cleaned_dns_params.yaml" > "$LOG_DIR/cleaned_dns_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（45秒）..."
echo "   - 验证旧的DNS检测逻辑已删除"
echo "   - 确认新的智能DNS检查正常工作"
echo "   - 测试中国网络环境优化功能"
echo "   - 验证DNS解析测试的环境适应性"
sleep 45

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析清理后的DNS管理系统..."

# 分析系统日志
echo "=== 清理后的DNS管理系统分析 ==="
if [ -f "$LOG_DIR/cleaned_dns_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    echo
    echo "--- 旧功能清理验证 ---"
    
    # 检查是否还有旧的DNS检测逻辑
    old_check_dns_config=$(grep -c "检查DNS配置" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    old_dns_resolution=$(grep -c "DNS解析不正常" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    
    echo "旧的check_dns_config调用次数: $old_check_dns_config"
    echo "旧的DNS解析检查次数: $old_dns_resolution"
    
    if [ $old_check_dns_config -eq 0 ]; then
        echo "✅ 旧的check_dns_config函数已成功删除"
    else
        echo "❌ 仍有旧的check_dns_config函数调用"
    fi
    
    echo
    echo "--- 新功能正常工作验证 ---"
    
    # 检查智能DNS检查
    smart_dns_check_count=$(grep -c "开始智能DNS检查\|智能DNS检查完成" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    intelligent_check_count=$(grep -c "intelligent_dns_check" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    
    echo "智能DNS检查执行次数: $smart_dns_check_count"
    echo "intelligent_dns_check调用次数: $intelligent_check_count"
    
    if [ $smart_dns_check_count -gt 0 ]; then
        echo "✅ 新的智能DNS检查正常工作"
    else
        echo "❌ 新的智能DNS检查未工作"
    fi
    
    echo
    echo "--- 中国网络环境优化验证 ---"
    
    # 检查中国网络环境检测
    china_env_detection=$(grep -c "网络环境检测完成" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    china_dns_config=$(grep -c "中国优化DNS服务器配置" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    
    echo "中国网络环境检测次数: $china_env_detection"
    echo "中国DNS配置加载次数: $china_dns_config"
    
    if [ $china_env_detection -gt 0 ]; then
        echo "✅ 中国网络环境检测正常工作"
    else
        echo "❌ 中国网络环境检测未工作"
    fi
    
    echo
    echo "--- DNS解析测试环境适应性验证 ---"
    
    # 检查DNS解析测试的环境适应性
    dns_resolution_test=$(grep -c "DNS解析测试结果.*环境:" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    domain_resolution_success=$(grep -c "成功解析域名" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    
    echo "环境适应性DNS解析测试次数: $dns_resolution_test"
    echo "域名解析成功次数: $domain_resolution_success"
    
    if [ $dns_resolution_test -gt 0 ]; then
        echo "✅ DNS解析测试环境适应性正常"
    else
        echo "ℹ️ DNS解析测试可能在DEBUG级别"
    fi
    
    echo
    echo "--- 系统整体功能验证 ---"
    
    # 检查DNS服务器测试
    dns_server_test_count=$(grep -c "测试DNS服务器.*结果" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    dns_switch_attempt=$(grep -c "切换到最佳DNS服务器\|尝试切换到DNS服务器" "$LOG_DIR/cleaned_dns_system.log" 2>/dev/null || echo "0")
    
    echo "DNS服务器测试次数: $dns_server_test_count"
    echo "DNS切换尝试次数: $dns_switch_attempt"
    
    if [ $dns_server_test_count -gt 0 ]; then
        echo "✅ DNS服务器测试功能正常"
    else
        echo "ℹ️ DNS服务器测试可能在DEBUG级别"
    fi
    
    echo
    echo "--- 关键日志示例 ---"
    
    echo "智能DNS检查日志:"
    grep "智能DNS检查" "$LOG_DIR/cleaned_dns_system.log" | head -3
    
    echo
    echo "中国网络环境检测日志:"
    grep "网络环境检测完成\|在中国境内\|检测地区" "$LOG_DIR/cleaned_dns_system.log" | head -3
    
    echo
    echo "DNS解析测试日志:"
    grep "DNS解析测试结果\|成功解析域名" "$LOG_DIR/cleaned_dns_system.log" | head -3
    
    echo
    echo "DNS服务器配置日志:"
    grep "DNS服务器:.*地区:" "$LOG_DIR/cleaned_dns_system.log" | head -3
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 总体评估
echo "5. 清理后的DNS管理系统总体评估..."

score=0
max_score=6

# 评分标准
if [ $old_check_dns_config -eq 0 ]; then ((score++)); fi
if [ $smart_dns_check_count -gt 0 ]; then ((score++)); fi
if [ $china_env_detection -gt 0 ]; then ((score++)); fi
if [ $china_dns_config -gt 0 ]; then ((score++)); fi
if [ $dns_resolution_test -gt 0 ] || [ $domain_resolution_success -gt 0 ]; then ((score++)); fi
if [ $dns_server_test_count -gt 0 ] || [ $dns_switch_attempt -gt 0 ]; then ((score++)); fi

echo "清理后的DNS管理系统评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 DNS管理系统清理完全成功！"
    echo "  ✅ 旧的DNS检测逻辑已完全删除"
    echo "  ✅ 新的智能DNS检查正常工作"
    echo "  ✅ 中国网络环境优化功能正常"
    echo "  ✅ DNS解析测试环境适应性良好"
    echo "  ✅ DNS服务器测试功能正常"
    echo "  ✅ 系统整体功能完整"
elif [ $score -ge 4 ]; then
    echo "✅ DNS管理系统清理基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ DNS管理系统清理需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示清理优势
echo "6. DNS管理系统清理优势..."
echo "✅ 代码简化:"
echo "   • 删除了旧的check_dns_config函数"
echo "   • 移除了重复的DNS检测逻辑"
echo "   • 清理了未实现的函数声明"

echo
echo "✅ 功能整合:"
echo "   • 统一使用intelligent_dns_check()进行DNS检查"
echo "   • 集成中国网络环境优化功能"
echo "   • 环境适应性DNS解析测试"

echo
echo "✅ 性能优化:"
echo "   • 避免重复的DNS检测操作"
echo "   • 智能的DNS服务器选择策略"
echo "   • 基于网络环境的优化配置"

echo
echo "✅ 维护性提升:"
echo "   • 代码结构更清晰"
echo "   • 功能职责更明确"
echo "   • 减少了代码冗余"

echo
echo "详细日志文件:"
echo "  清理后DNS系统: $LOG_DIR/cleaned_dns_system.log"
echo "  测试参数文件: $LOG_DIR/cleaned_dns_params.yaml"

echo
echo "🧹 DNS管理系统清理测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
