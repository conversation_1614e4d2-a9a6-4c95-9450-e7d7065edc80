#!/bin/bash

# 简单的 ROS2 发布订阅延迟测试脚本
# 使用 ros2 topic pub 和 ros2 topic echo 来测试延迟

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 简单 ROS2 发布订阅延迟测试 ===${NC}"
echo

# 检查 ROS2 环境
source /opt/ros/humble/setup.bash

if [ -z "$ROS_DISTRO" ]; then
    echo -e "${RED}错误: ROS2 环境未设置${NC}"
    exit 1
fi

echo -e "${BLUE}1. 环境信息${NC}"
echo -e "  ROS 发行版: ${GREEN}$ROS_DISTRO${NC}"
echo -e "  测试时间: ${GREEN}$(date)${NC}"
echo

# 创建测试日志目录
LOG_DIR="/tmp/simple_pub_sub_test_$(date +%s)"
mkdir -p "$LOG_DIR"
echo -e "${BLUE}2. 创建测试日志目录: ${YELLOW}$LOG_DIR${NC}"
echo

# 测试函数：基本延迟测试
test_basic_latency() {
    echo -e "${BLUE}3. 基本延迟测试${NC}"
    
    # 测试话题
    TOPIC="/test_latency"
    
    echo -e "  ${YELLOW}启动订阅者（后台运行）...${NC}"
    # 启动订阅者，记录时间戳
    ros2 topic echo $TOPIC --csv > "$LOG_DIR/subscriber_output.log" 2>&1 &
    SUB_PID=$!
    echo -e "  ${GREEN}✓${NC} 订阅者已启动 (PID: $SUB_PID)"
    
    # 等待订阅者启动
    sleep 2
    
    echo -e "  ${YELLOW}开始发布消息...${NC}"
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    echo "测试开始时间: $START_TIME" > "$LOG_DIR/timing.log"
    
    # 发布10条消息，每秒1条
    for i in {1..10}; do
        MSG_TIME=$(date +%s.%N)
        MSG_DATA="message_${i}_at_${MSG_TIME}"
        
        echo "发布消息 $i: $MSG_DATA (时间: $MSG_TIME)" >> "$LOG_DIR/timing.log"
        
        # 发布消息
        ros2 topic pub --once $TOPIC std_msgs/msg/String "data: '$MSG_DATA'" > /dev/null 2>&1
        
        sleep 1
    done
    
    # 记录结束时间
    END_TIME=$(date +%s.%N)
    echo "测试结束时间: $END_TIME" >> "$LOG_DIR/timing.log"
    
    echo -e "  ${GREEN}✓${NC} 发布完成，等待订阅者接收..."
    
    # 等待订阅者接收
    sleep 3
    
    # 停止订阅者
    kill $SUB_PID 2>/dev/null
    wait $SUB_PID 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 基本延迟测试完成"
}

# 测试函数：高频延迟测试
test_high_frequency_latency() {
    echo
    echo -e "${BLUE}4. 高频延迟测试${NC}"
    
    TOPIC="/test_high_freq"
    
    echo -e "  ${YELLOW}启动高频订阅者...${NC}"
    ros2 topic echo $TOPIC --csv > "$LOG_DIR/high_freq_subscriber.log" 2>&1 &
    SUB_PID=$!
    
    # 等待订阅者启动
    sleep 2
    
    echo -e "  ${YELLOW}发布高频消息（10Hz，持续5秒）...${NC}"
    
    # 高频发布：10Hz，持续5秒
    ros2 topic pub --rate 10 $TOPIC std_msgs/msg/String "data: 'high_freq_test'" > "$LOG_DIR/high_freq_publisher.log" 2>&1 &
    PUB_PID=$!
    
    # 运行5秒
    sleep 5
    
    # 停止发布者和订阅者
    kill $PUB_PID $SUB_PID 2>/dev/null
    wait $PUB_PID $SUB_PID 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 高频延迟测试完成"
}

# 测试函数：连接建立时间
test_connection_time() {
    echo
    echo -e "${BLUE}5. 连接建立时间测试${NC}"
    
    TOPIC="/test_connection"
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    
    echo -e "  ${YELLOW}同时启动发布者和订阅者...${NC}"
    
    # 同时启动发布者和订阅者
    ros2 topic pub --rate 1 $TOPIC std_msgs/msg/String "data: 'connection_test'" > "$LOG_DIR/connection_pub.log" 2>&1 &
    PUB_PID=$!
    
    ros2 topic echo $TOPIC --csv > "$LOG_DIR/connection_sub.log" 2>&1 &
    SUB_PID=$!
    
    echo -e "  ${YELLOW}等待连接建立和消息传递...${NC}"
    
    # 等待10秒
    sleep 10
    
    # 记录结束时间
    END_TIME=$(date +%s.%N)
    
    # 停止进程
    kill $PUB_PID $SUB_PID 2>/dev/null
    wait $PUB_PID $SUB_PID 2>/dev/null
    
    # 计算总时间
    TOTAL_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
    echo -e "  ${GREEN}✓${NC} 连接测试完成，总时间: ${YELLOW}${TOTAL_TIME}${NC} 秒"
}

# 分析结果函数
analyze_results() {
    echo
    echo -e "${BLUE}6. 分析测试结果${NC}"
    
    # 分析基本延迟测试
    echo -e "${YELLOW}基本延迟测试结果:${NC}"
    if [ -f "$LOG_DIR/subscriber_output.log" ]; then
        received_count=$(wc -l < "$LOG_DIR/subscriber_output.log" 2>/dev/null || echo "0")
        echo -e "  发布消息数量: ${GREEN}10${NC}"
        echo -e "  接收消息数量: ${GREEN}$received_count${NC}"
        
        if [ "$received_count" -gt 0 ]; then
            success_rate=$(echo "scale=1; $received_count * 100 / 10" | bc -l)
            echo -e "  接收成功率: ${GREEN}${success_rate}%${NC}"
            
            if [ "$received_count" -eq 10 ]; then
                echo -e "  ${GREEN}✓${NC} 所有消息都成功接收"
            elif [ "$received_count" -gt 5 ]; then
                echo -e "  ${YELLOW}○${NC} 大部分消息成功接收"
            else
                echo -e "  ${RED}✗${NC} 消息丢失较多"
            fi
        else
            echo -e "  ${RED}✗${NC} 没有接收到任何消息"
        fi
    fi
    
    # 分析高频测试
    echo -e "${YELLOW}高频延迟测试结果:${NC}"
    if [ -f "$LOG_DIR/high_freq_subscriber.log" ]; then
        high_freq_count=$(wc -l < "$LOG_DIR/high_freq_subscriber.log" 2>/dev/null || echo "0")
        echo -e "  预期消息数量: ${GREEN}50${NC} (10Hz × 5秒)"
        echo -e "  实际接收数量: ${GREEN}$high_freq_count${NC}"
        
        if [ "$high_freq_count" -gt 40 ]; then
            echo -e "  ${GREEN}✓${NC} 高频传输性能良好"
        elif [ "$high_freq_count" -gt 20 ]; then
            echo -e "  ${YELLOW}○${NC} 高频传输性能一般"
        else
            echo -e "  ${RED}✗${NC} 高频传输性能较差"
        fi
    fi
    
    # 分析连接建立
    echo -e "${YELLOW}连接建立测试结果:${NC}"
    if [ -f "$LOG_DIR/connection_sub.log" ]; then
        connection_count=$(wc -l < "$LOG_DIR/connection_sub.log" 2>/dev/null || echo "0")
        echo -e "  连接测试接收数量: ${GREEN}$connection_count${NC}"
        
        if [ "$connection_count" -gt 8 ]; then
            echo -e "  ${GREEN}✓${NC} 连接建立快速"
        elif [ "$connection_count" -gt 5 ]; then
            echo -e "  ${YELLOW}○${NC} 连接建立正常"
        else
            echo -e "  ${RED}✗${NC} 连接建立较慢"
        fi
    fi
}

# 生成详细报告
generate_report() {
    echo
    echo -e "${BLUE}7. 生成测试报告${NC}"
    
    REPORT_FILE="$LOG_DIR/latency_test_report.txt"
    
    echo "ROS2 简单发布订阅延迟测试报告" > "$REPORT_FILE"
    echo "======================================" >> "$REPORT_FILE"
    echo "测试时间: $(date)" >> "$REPORT_FILE"
    echo "ROS 发行版: $ROS_DISTRO" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # 基本测试统计
    echo "1. 基本延迟测试:" >> "$REPORT_FILE"
    if [ -f "$LOG_DIR/subscriber_output.log" ]; then
        received_count=$(wc -l < "$LOG_DIR/subscriber_output.log" 2>/dev/null || echo "0")
        echo "发布消息数量: 10" >> "$REPORT_FILE"
        echo "接收消息数量: $received_count" >> "$REPORT_FILE"
        success_rate=$(echo "scale=1; $received_count * 100 / 10" | bc -l)
        echo "接收成功率: ${success_rate}%" >> "$REPORT_FILE"
    fi
    echo >> "$REPORT_FILE"
    
    # 高频测试统计
    echo "2. 高频延迟测试:" >> "$REPORT_FILE"
    if [ -f "$LOG_DIR/high_freq_subscriber.log" ]; then
        high_freq_count=$(wc -l < "$LOG_DIR/high_freq_subscriber.log" 2>/dev/null || echo "0")
        echo "预期消息数量: 50 (10Hz × 5秒)" >> "$REPORT_FILE"
        echo "实际接收数量: $high_freq_count" >> "$REPORT_FILE"
        high_freq_rate=$(echo "scale=1; $high_freq_count * 100 / 50" | bc -l)
        echo "高频接收率: ${high_freq_rate}%" >> "$REPORT_FILE"
    fi
    echo >> "$REPORT_FILE"
    
    # 连接测试统计
    echo "3. 连接建立测试:" >> "$REPORT_FILE"
    if [ -f "$LOG_DIR/connection_sub.log" ]; then
        connection_count=$(wc -l < "$LOG_DIR/connection_sub.log" 2>/dev/null || echo "0")
        echo "连接测试接收数量: $connection_count" >> "$REPORT_FILE"
        echo "连接测试时长: 10秒" >> "$REPORT_FILE"
    fi
    echo >> "$REPORT_FILE"
    
    # 时间戳分析
    if [ -f "$LOG_DIR/timing.log" ]; then
        echo "4. 时间戳记录:" >> "$REPORT_FILE"
        cat "$LOG_DIR/timing.log" >> "$REPORT_FILE"
    fi
    
    echo -e "  ${GREEN}✓${NC} 测试报告已生成: $REPORT_FILE"
}

# 清理函数
cleanup() {
    echo
    echo -e "${BLUE}8. 清理测试环境${NC}"
    
    # 确保所有测试进程都已停止
    pkill -f "ros2 topic" 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 测试进程已清理"
    echo -e "  ${BLUE}日志目录:${NC} $LOG_DIR"
    echo -e "  ${BLUE}查看报告:${NC} cat $LOG_DIR/latency_test_report.txt"
}

# 主测试流程
main() {
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试
    test_basic_latency
    test_high_frequency_latency
    test_connection_time
    
    # 分析结果
    analyze_results
    
    # 生成报告
    generate_report
    
    echo
    echo -e "${BLUE}9. 测试总结${NC}"
    
    # 简单的成功率评估
    basic_success=false
    high_freq_success=false
    connection_success=false
    
    if [ -f "$LOG_DIR/subscriber_output.log" ]; then
        received_count=$(wc -l < "$LOG_DIR/subscriber_output.log" 2>/dev/null || echo "0")
        if [ "$received_count" -gt 5 ]; then
            basic_success=true
        fi
    fi
    
    if [ -f "$LOG_DIR/high_freq_subscriber.log" ]; then
        high_freq_count=$(wc -l < "$LOG_DIR/high_freq_subscriber.log" 2>/dev/null || echo "0")
        if [ "$high_freq_count" -gt 20 ]; then
            high_freq_success=true
        fi
    fi
    
    if [ -f "$LOG_DIR/connection_sub.log" ]; then
        connection_count=$(wc -l < "$LOG_DIR/connection_sub.log" 2>/dev/null || echo "0")
        if [ "$connection_count" -gt 5 ]; then
            connection_success=true
        fi
    fi
    
    # 计算总体成功率
    success_count=0
    if [ "$basic_success" = true ]; then ((success_count++)); fi
    if [ "$high_freq_success" = true ]; then ((success_count++)); fi
    if [ "$connection_success" = true ]; then ((success_count++)); fi
    
    echo -e "  测试通过率: ${GREEN}$success_count/3${NC}"
    
    if [ $success_count -eq 3 ]; then
        echo -e "  ${GREEN}✅ 所有延迟测试通过！${NC}"
        echo -e "    • 基本消息传递正常"
        echo -e "    • 高频传输性能良好"
        echo -e "    • 连接建立快速"
    elif [ $success_count -eq 2 ]; then
        echo -e "  ${YELLOW}⚠️ 大部分延迟测试通过${NC}"
        echo -e "    • 系统性能基本正常"
        echo -e "    • 可能存在轻微延迟问题"
    else
        echo -e "  ${RED}❌ 延迟测试存在问题${NC}"
        echo -e "    • 请检查网络配置"
        echo -e "    • 可能需要优化 QoS 设置"
    fi
    
    echo
    echo -e "${BLUE}💡 延迟优化建议:${NC}"
    echo -e "  1. 使用 Reliable + TransientLocal QoS 配置"
    echo -e "  2. 适当增加初始化等待时间"
    echo -e "  3. 监控系统资源使用情况"
    echo -e "  4. 考虑使用更高优先级的进程调度"
}

# 执行主函数
main

echo
echo -e "${GREEN}🎊 简单 ROS2 发布订阅延迟测试完成！${NC}"
