#!/bin/bash

# WiFi 信号强度转换测试脚本
# 验证百分比到 dBm 的转换功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== WiFi 信号强度转换测试 ===${NC}"
echo

# 检查 nmcli 是否可用
if ! command -v nmcli >/dev/null 2>&1; then
    echo -e "${RED}错误: nmcli 命令不可用${NC}"
    echo "请确保已安装 NetworkManager"
    exit 1
fi

echo -e "${BLUE}1. 测试信号强度转换公式${NC}"
echo

# 定义转换函数（与 C++ 代码中的逻辑相同）
convert_percentage_to_dbm() {
    local percentage=$1

    # 限制百分比范围在 0-100 之间
    if [ $percentage -lt 0 ]; then
        percentage=0
    elif [ $percentage -gt 100 ]; then
        percentage=100
    fi

    # 转换公式：dBm = -100 + (percentage * 0.7)
    # 使用整数运算：dBm = -100 + (percentage * 7 / 10)
    local dbm=$(( -100 + (percentage * 7 / 10) ))

    # 确保结果在合理范围内 (-100 到 -30 dBm)
    if [ $dbm -gt -30 ]; then
        dbm=-30
    elif [ $dbm -lt -100 ]; then
        dbm=-100
    fi

    echo $dbm
}

echo -e "${YELLOW}转换公式验证:${NC}"
echo "  公式: dBm = -100 + (percentage * 0.7)"
echo "  范围: -100 dBm 到 -30 dBm"
echo

# 测试关键百分比值
test_values=(0 10 25 50 75 90 100)
echo -e "${YELLOW}关键值转换测试:${NC}"
for percentage in "${test_values[@]}"; do
    dbm=$(convert_percentage_to_dbm $percentage)
    
    # 根据信号强度给出评级
    if [ $dbm -ge -50 ]; then
        rating="${GREEN}优秀${NC}"
    elif [ $dbm -ge -60 ]; then
        rating="${GREEN}良好${NC}"
    elif [ $dbm -ge -70 ]; then
        rating="${YELLOW}一般${NC}"
    elif [ $dbm -ge -80 ]; then
        rating="${YELLOW}较弱${NC}"
    else
        rating="${RED}很弱${NC}"
    fi
    
    printf "  %3d%% -> %3d dBm (%s)\n" $percentage $dbm "$rating"
done

echo
echo -e "${BLUE}2. 实际 WiFi 网络信号强度测试${NC}"
echo

# 获取当前 WiFi 网络的信号强度
echo -e "${YELLOW}当前连接的 WiFi 网络:${NC}"
current_wifi=$(LANG=C nmcli -t -f active,ssid,signal dev wifi 2>/dev/null | grep '^yes' | head -1)

if [ -n "$current_wifi" ]; then
    # 解析输出: yes:SSID:SIGNAL
    ssid=$(echo "$current_wifi" | cut -d: -f2)
    percentage=$(echo "$current_wifi" | cut -d: -f3)
    dbm=$(convert_percentage_to_dbm $percentage)
    
    echo -e "  ${GREEN}✓${NC} SSID: $ssid"
    echo -e "  ${GREEN}✓${NC} 原始信号强度: $percentage%"
    echo -e "  ${GREEN}✓${NC} 转换后信号强度: $dbm dBm"
    
    # 信号质量评估
    if [ $dbm -ge -50 ]; then
        quality="${GREEN}优秀 - 信号很强${NC}"
    elif [ $dbm -ge -60 ]; then
        quality="${GREEN}良好 - 信号较强${NC}"
    elif [ $dbm -ge -70 ]; then
        quality="${YELLOW}一般 - 信号中等${NC}"
    elif [ $dbm -ge -80 ]; then
        quality="${YELLOW}较弱 - 可能影响性能${NC}"
    else
        quality="${RED}很弱 - 连接不稳定${NC}"
    fi
    
    echo -e "  ${BLUE}信号质量:${NC} $quality"
else
    echo -e "  ${YELLOW}○${NC} 当前没有连接到 WiFi 网络"
fi

echo
echo -e "${BLUE}3. 扫描附近 WiFi 网络信号强度${NC}"
echo

# 扫描附近的 WiFi 网络
echo -e "${YELLOW}附近 WiFi 网络信号强度 (前5个):${NC}"
LANG=C nmcli -t -f SSID,SIGNAL dev wifi list 2>/dev/null | head -5 | while IFS=: read ssid percentage; do
    if [ -n "$ssid" ] && [ -n "$percentage" ]; then
        dbm=$(convert_percentage_to_dbm $percentage)
        
        # 信号强度条形图
        if [ $percentage -ge 80 ]; then
            bars="████████"
            color="${GREEN}"
        elif [ $percentage -ge 60 ]; then
            bars="██████  "
            color="${GREEN}"
        elif [ $percentage -ge 40 ]; then
            bars="████    "
            color="${YELLOW}"
        elif [ $percentage -ge 20 ]; then
            bars="██      "
            color="${YELLOW}"
        else
            bars="▌       "
            color="${RED}"
        fi
        
        printf "  %-20s %s%s${NC} %3d%% (%3d dBm)\n" "$ssid" "$color" "$bars" $percentage $dbm
    fi
done

echo
echo -e "${BLUE}4. 信号强度范围说明${NC}"
echo

echo -e "${YELLOW}dBm 值含义:${NC}"
echo -e "  ${GREEN}-30 到 -50 dBm${NC}  优秀 - 最佳信号强度"
echo -e "  ${GREEN}-50 到 -60 dBm${NC}  良好 - 很好的信号强度"
echo -e "  ${YELLOW}-60 到 -70 dBm${NC}  一般 - 可接受的信号强度"
echo -e "  ${YELLOW}-70 到 -80 dBm${NC}  较弱 - 最低可用信号强度"
echo -e "  ${RED}-80 到 -90 dBm${NC}  很弱 - 连接不可靠"
echo -e "  ${RED}-90 到 -100 dBm${NC} 极弱 - 几乎无法连接"

echo
echo -e "${BLUE}5. 转换准确性验证${NC}"
echo

# 验证转换的准确性
echo -e "${YELLOW}转换准确性检查:${NC}"

# 检查边界值
boundary_tests=(
    "0:-100"
    "100:-30"
    "50:-65"
    "25:-82"
    "75:-47"
)

all_passed=true
for test in "${boundary_tests[@]}"; do
    input=$(echo $test | cut -d: -f1)
    expected=$(echo $test | cut -d: -f2)
    actual=$(convert_percentage_to_dbm $input)
    
    if [ $actual -eq $expected ]; then
        echo -e "  ${GREEN}✓${NC} $input% -> $actual dBm (期望: $expected dBm)"
    else
        echo -e "  ${RED}✗${NC} $input% -> $actual dBm (期望: $expected dBm)"
        all_passed=false
    fi
done

echo
if [ "$all_passed" = true ]; then
    echo -e "${GREEN}✅ 所有转换测试通过！${NC}"
    echo -e "  • 转换公式正确实现"
    echo -e "  • 边界值处理正确"
    echo -e "  • 输出范围符合预期"
else
    echo -e "${RED}❌ 部分转换测试失败${NC}"
    echo -e "  • 请检查转换公式实现"
fi

echo
echo -e "${BLUE}💡 使用建议:${NC}"
echo -e "  1. dBm 是对数单位，-3dBm 的差异表示信号强度减半"
echo -e "  2. 在 -70dBm 以上的信号通常能提供良好的连接质量"
echo -e "  3. 移动设备时监控信号强度变化有助于优化位置"
echo -e "  4. 2.4GHz 和 5GHz 频段的信号传播特性不同"

echo
echo -e "${GREEN}🎊 WiFi 信号强度转换测试完成！${NC}"
