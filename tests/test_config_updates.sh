#!/bin/bash

# 测试配置文件更新

echo "=== 测试配置文件更新 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/config_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

echo "1. 验证配置文件内容..."

# 检查主配置文件
echo "--- 检查主配置文件 (network_config.yaml) ---"
CONFIG_FILE="src/gen3_network_manager_core/config/network_config.yaml"

if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 主配置文件存在"
    
    # 检查智能DNS管理配置
    dns_config_count=$(grep -c "dns_servers:" "$CONFIG_FILE" 2>/dev/null || echo "0")
    china_dns_count=$(grep -c "china_primary:" "$CONFIG_FILE" 2>/dev/null || echo "0")
    china_network_count=$(grep -c "china_network:" "$CONFIG_FILE" 2>/dev/null || echo "0")
    quality_weights_count=$(grep -c "quality_weights:" "$CONFIG_FILE" 2>/dev/null || echo "0")
    
    echo "DNS服务器配置: $dns_config_count 个"
    echo "中国DNS配置: $china_dns_count 个"
    echo "中国网络配置: $china_network_count 个"
    echo "质量权重配置: $quality_weights_count 个"
    
    if [ $dns_config_count -gt 0 ] && [ $china_dns_count -gt 0 ] && [ $china_network_count -gt 0 ]; then
        echo "✅ 主配置文件包含所有新增参数"
    else
        echo "❌ 主配置文件缺少部分新增参数"
    fi
else
    echo "❌ 主配置文件不存在"
fi

echo

# 检查开发配置文件
echo "--- 检查开发配置文件 (development_config.yaml) ---"
DEV_CONFIG_FILE="src/gen3_network_manager_core/config/development_config.yaml"

if [ -f "$DEV_CONFIG_FILE" ]; then
    echo "✅ 开发配置文件存在"
    
    # 检查开发环境特殊配置
    dev_dns_config=$(grep -c "dns_servers:" "$DEV_CONFIG_FILE" 2>/dev/null || echo "0")
    dev_china_config=$(grep -c "china_network:" "$DEV_CONFIG_FILE" 2>/dev/null || echo "0")
    dev_async_config=$(grep -c "async_quality_check:" "$DEV_CONFIG_FILE" 2>/dev/null || echo "0")
    
    echo "开发DNS配置: $dev_dns_config 个"
    echo "开发中国网络配置: $dev_china_config 个"
    echo "开发异步配置: $dev_async_config 个"
    
    if [ $dev_dns_config -gt 0 ] && [ $dev_china_config -gt 0 ] && [ $dev_async_config -gt 0 ]; then
        echo "✅ 开发配置文件包含所有新增参数"
    else
        echo "❌ 开发配置文件缺少部分新增参数"
    fi
else
    echo "❌ 开发配置文件不存在"
fi

echo

# 检查生产配置文件
echo "--- 检查生产配置文件 (production_config.yaml) ---"
PROD_CONFIG_FILE="src/gen3_network_manager_core/config/production_config.yaml"

if [ -f "$PROD_CONFIG_FILE" ]; then
    echo "✅ 生产配置文件存在"
    
    # 检查生产环境特殊配置
    prod_dns_config=$(grep -c "dns_servers:" "$PROD_CONFIG_FILE" 2>/dev/null || echo "0")
    prod_china_config=$(grep -c "china_network:" "$PROD_CONFIG_FILE" 2>/dev/null || echo "0")
    prod_stability_config=$(grep -c "stability:" "$PROD_CONFIG_FILE" 2>/dev/null || echo "0")
    
    echo "生产DNS配置: $prod_dns_config 个"
    echo "生产中国网络配置: $prod_china_config 个"
    echo "生产稳定性配置: $prod_stability_config 个"
    
    if [ $prod_dns_config -gt 0 ] && [ $prod_china_config -gt 0 ]; then
        echo "✅ 生产配置文件包含所有新增参数"
    else
        echo "❌ 生产配置文件缺少部分新增参数"
    fi
else
    echo "❌ 生产配置文件不存在"
fi

echo

echo "2. 测试配置文件加载..."

# 使用主配置文件测试
echo "--- 使用主配置文件启动系统 ---"
timeout 15 ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$CONFIG_FILE" > "$LOG_DIR/main_config_test.log" 2>&1 &
MAIN_PID=$!
sleep 10
kill $MAIN_PID 2>/dev/null
wait $MAIN_PID 2>/dev/null

# 分析主配置文件测试结果
if [ -f "$LOG_DIR/main_config_test.log" ]; then
    main_init_success=$(grep -c "智能DNS管理器初始化完成\|网络管理器初始化完成" "$LOG_DIR/main_config_test.log" 2>/dev/null || echo "0")
    main_dns_load=$(grep -c "加载.*DNS服务器配置" "$LOG_DIR/main_config_test.log" 2>/dev/null || echo "0")
    main_china_detect=$(grep -c "网络环境检测" "$LOG_DIR/main_config_test.log" 2>/dev/null || echo "0")
    
    echo "主配置初始化成功: $main_init_success"
    echo "主配置DNS加载: $main_dns_load"
    echo "主配置中国检测: $main_china_detect"
    
    if [ $main_init_success -gt 0 ]; then
        echo "✅ 主配置文件加载成功"
    else
        echo "❌ 主配置文件加载失败"
    fi
else
    echo "❌ 主配置文件测试日志不存在"
fi

echo

# 使用开发配置文件测试
echo "--- 使用开发配置文件启动系统 ---"
timeout 15 ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$DEV_CONFIG_FILE" > "$LOG_DIR/dev_config_test.log" 2>&1 &
DEV_PID=$!
sleep 10
kill $DEV_PID 2>/dev/null
wait $DEV_PID 2>/dev/null

# 分析开发配置文件测试结果
if [ -f "$LOG_DIR/dev_config_test.log" ]; then
    dev_init_success=$(grep -c "智能DNS管理器初始化完成\|网络管理器初始化完成" "$LOG_DIR/dev_config_test.log" 2>/dev/null || echo "0")
    dev_dns_load=$(grep -c "加载.*DNS服务器配置" "$LOG_DIR/dev_config_test.log" 2>/dev/null || echo "0")
    dev_china_detect=$(grep -c "网络环境检测" "$LOG_DIR/dev_config_test.log" 2>/dev/null || echo "0")
    
    echo "开发配置初始化成功: $dev_init_success"
    echo "开发配置DNS加载: $dev_dns_load"
    echo "开发配置中国检测: $dev_china_detect"
    
    if [ $dev_init_success -gt 0 ]; then
        echo "✅ 开发配置文件加载成功"
    else
        echo "❌ 开发配置文件加载失败"
    fi
else
    echo "❌ 开发配置文件测试日志不存在"
fi

echo

echo "3. 配置参数验证..."

# 显示关键配置参数
echo "--- 关键配置参数示例 ---"

echo "主配置文件中的DNS服务器:"
grep -A5 "dns_servers:" "$CONFIG_FILE" | head -10

echo
echo "开发配置文件中的中国网络配置:"
grep -A10 "china_network:" "$DEV_CONFIG_FILE" | head -15

echo
echo "生产配置文件中的质量权重配置:"
grep -A8 "quality_weights:" "$PROD_CONFIG_FILE" | head -10

echo

# 总体评估
echo "4. 配置文件更新总体评估..."

score=0
max_score=6

# 评分标准
if [ -f "$CONFIG_FILE" ]; then ((score++)); fi
if [ -f "$DEV_CONFIG_FILE" ]; then ((score++)); fi
if [ -f "$PROD_CONFIG_FILE" ]; then ((score++)); fi
if [ $dns_config_count -gt 0 ] && [ $china_dns_count -gt 0 ]; then ((score++)); fi
if [ $main_init_success -gt 0 ]; then ((score++)); fi
if [ $dev_init_success -gt 0 ]; then ((score++)); fi

echo "配置文件更新评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 配置文件更新完全成功！"
    echo "  ✅ 所有配置文件都存在"
    echo "  ✅ 新增参数配置完整"
    echo "  ✅ 配置文件加载正常"
    echo "  ✅ 系统初始化成功"
elif [ $score -ge 4 ]; then
    echo "✅ 配置文件更新基本成功"
    echo "  • 大部分配置正常"
    echo "  • 可能需要微调"
else
    echo "❌ 配置文件更新需要改进"
    echo "  • 请检查配置文件和参数"
fi

echo

# 显示配置更新优势
echo "5. 配置文件更新优势..."
echo "✅ 智能DNS管理配置:"
echo "   • 完整的DNS服务器列表配置"
echo "   • 中国优化DNS服务器配置"
echo "   • DNS检测和切换参数配置"

echo
echo "✅ 中国网络环境配置:"
echo "   • 网络环境检测开关和间隔配置"
echo "   • 国内外网站测试列表配置"
echo "   • DNS优先级策略配置"

echo
echo "✅ 科学化质量评分配置:"
echo "   • 科学的权重分配参数"
echo "   • 去掉带宽评分的简化配置"
echo "   • 环境差异化的权重调整"

echo
echo "✅ 异步处理配置:"
echo "   • 异步质量检查参数配置"
echo "   • 异步回调处理配置"
echo "   • 并发控制和超时配置"

echo
echo "✅ 环境差异化配置:"
echo "   • 开发环境：高频监控，详细日志"
echo "   • 生产环境：稳定间隔，性能优化"
echo "   • 主配置：平衡的默认配置"

echo
echo "配置文件位置:"
echo "  主配置: $CONFIG_FILE"
echo "  开发配置: $DEV_CONFIG_FILE"
echo "  生产配置: $PROD_CONFIG_FILE"

echo
echo "测试日志文件:"
echo "  主配置测试: $LOG_DIR/main_config_test.log"
echo "  开发配置测试: $LOG_DIR/dev_config_test.log"

echo
echo "📝 配置文件更新测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
