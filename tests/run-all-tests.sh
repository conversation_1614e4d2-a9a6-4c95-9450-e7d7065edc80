#!/bin/bash

# ROS2 网络管理器测试套件
# 运行所有测试脚本和验证程序

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# 运行单个测试的函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local test_description="$3"
    
    ((TOTAL_TESTS++))
    
    log_header "$test_name"
    log_info "$test_description"
    
    if eval "$test_command"; then
        log_success "$test_name 通过"
        return 0
    else
        log_error "$test_name 失败"
        return 1
    fi
}

# 主测试流程
main() {
    log_header "ROS2 网络管理器测试套件"
    log_info "开始运行所有测试..."
    echo
    
    # 检查测试环境
    log_header "环境检查"
    
    # 检查是否在正确的目录
    if [ ! -f "README.md" ] || [ ! -d "src" ]; then
        log_error "请在项目根目录中运行此脚本"
        exit 1
    fi
    
    # 检查必要的工具
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3 未安装，将跳过 YAML 语法检查"
    fi
    
    if ! command -v ros2 &> /dev/null; then
        log_warning "ROS2 环境未设置，将跳过 ROS 相关测试"
    fi
    
    echo
    
    # 1. 配置验证测试
    if [ -f "tests/config-validation-test.sh" ]; then
        run_test "配置验证测试" \
                 "./tests/config-validation-test.sh" \
                 "验证配置文件存在性、语法正确性和参数完整性"
    else
        log_error "配置验证测试脚本不存在: tests/config-validation-test.sh"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi

    echo

    # 2. 调试配置测试
    if [ -f "tests/debug-config-test.sh" ]; then
        run_test "调试配置测试" \
                 "./tests/debug-config-test.sh" \
                 "验证 VSCode 调试配置和环境设置"
    else
        log_error "调试配置测试脚本不存在: tests/debug-config-test.sh"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi

    echo

    # 3. 网络接口测试
    if [ -f "tests/network-interface-test.cpp" ]; then
        run_test "网络接口测试" \
                 "cd tests && g++ -o network-interface-test network-interface-test.cpp && ./network-interface-test && rm -f network-interface-test" \
                 "测试网络接口类型识别和配置映射"
    else
        log_error "网络接口测试文件不存在: tests/network-interface-test.cpp"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 4. 文档验证测试
    if [ -f "verify_documentation.sh" ]; then
        run_test "文档验证测试" \
                 "./verify_documentation.sh > /dev/null 2>&1" \
                 "验证文档结构完整性和链接有效性"
    else
        log_error "文档验证脚本不存在: verify_documentation.sh"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi

    echo

    # 5. 构建测试
    if [ -f "build.sh" ]; then
        run_test "项目构建测试" \
                 "./build.sh > /dev/null 2>&1" \
                 "验证项目能够正常构建"
    else
        log_error "构建脚本不存在: build.sh"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi

    echo

    # 6. 架构配置测试
    if [ -f "scripts/get_arch.sh" ] && [ -f "scripts/update_launch_config.sh" ]; then
        run_test "架构配置测试" \
                 "./scripts/get_arch.sh > /dev/null && ./scripts/update_launch_config.sh > /dev/null" \
                 "验证架构检测和配置更新功能"
    else
        log_error "架构配置脚本不完整"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 测试结果汇总
    log_header "测试结果汇总"
    echo -e "${CYAN}总测试数:${NC} $TOTAL_TESTS"
    echo -e "${GREEN}通过测试:${NC} $PASSED_TESTS"
    echo -e "${RED}失败测试:${NC} $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "ROS2 网络管理器测试套件"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出模式"
    echo "  -q, --quiet    静默模式"
    echo
    echo "测试项目:"
    echo "  1. 配置验证测试 - 验证 YAML 配置文件"
    echo "  2. 调试配置测试 - 验证 VSCode 调试环境"
    echo "  3. 网络接口测试 - 测试接口类型识别"
    echo "  4. 文档验证测试 - 验证文档结构"
    echo "  5. 项目构建测试 - 验证编译构建"
    echo "  6. 架构配置测试 - 验证多架构支持"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        -q|--quiet)
            exec > /dev/null 2>&1
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主程序
main
