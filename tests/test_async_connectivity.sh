#!/bin/bash

# 测试异步连通性检查和参数缓存优化

echo "=== 测试异步连通性检查和参数缓存优化 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/async_connectivity_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 创建测试参数文件
cat > "$LOG_DIR/test_params.yaml" << EOF
network_manager_node:
  ros__parameters:
    # 基本定时器间隔
    network_check_interval: 1.0
    connectivity_check_interval: 5.0
    
    # 连通性检查参数（测试用）
    connectivity.dns_server: "www.google.com"
    connectivity.internet_server: "1.1.1.1"
    connectivity.dns_timeout_ms: 3000
    connectivity.ping_timeout_ms: 2500
    connectivity.gateway_timeout_ms: 1500
    connectivity.external_timeout_ms: 3000
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "usb0"
    
    # 其他配置
    quality_check_interval: 30.0
    switch_monitor_interval: 5.0
    status_update_interval: 2.0
    log_level: "info"
EOF

echo "1. 启动网络管理系统（使用测试参数）..."
# 启动网络管理器节点，使用测试参数文件
ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$LOG_DIR/test_params.yaml" > "$LOG_DIR/network_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（20秒）..."
echo "   - 观察参数缓存效果"
echo "   - 观察异步连通性检查"
echo "   - 监控性能优化"
sleep 20

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析测试结果..."

# 分析系统日志
echo "=== 参数缓存分析 ==="
if [ -f "$LOG_DIR/network_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    # 检查参数获取和缓存
    echo
    echo "--- 参数获取和缓存分析 ---"
    
    if grep -q "获取连通性检查参数" "$LOG_DIR/network_system.log"; then
        echo "✅ 在初始化时获取连通性检查参数"
        
        # 显示获取的参数
        echo "获取的参数:"
        grep "连通性检查参数\|超时配置" "$LOG_DIR/network_system.log" | head -5
        
        # 检查参数是否正确
        if grep -q "DNS服务器: www.google.com" "$LOG_DIR/network_system.log"; then
            echo "✅ DNS服务器参数正确获取"
        else
            echo "❌ DNS服务器参数获取失败"
        fi
        
        if grep -q "互联网服务器: 1.1.1.1" "$LOG_DIR/network_system.log"; then
            echo "✅ 互联网服务器参数正确获取"
        else
            echo "❌ 互联网服务器参数获取失败"
        fi
        
    else
        echo "❌ 未在初始化时获取连通性检查参数"
    fi
    
    echo
    echo "--- 异步连通性检查分析 ---"
    
    # 检查异步检查启动
    if grep -q "启动首次异步连通性检查" "$LOG_DIR/network_system.log"; then
        echo "✅ 异步连通性检查已启动"
    else
        echo "❌ 异步连通性检查未启动"
    fi
    
    # 检查异步检查执行
    async_start_count=$(grep -c "开始异步.*检查" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    async_complete_count=$(grep -c "异步.*检查完成" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "异步检查启动次数: $async_start_count"
    echo "异步检查完成次数: $async_complete_count"
    
    if [ $async_start_count -gt 0 ]; then
        echo "✅ 异步检查正常启动"
    else
        echo "❌ 异步检查未启动"
    fi
    
    if [ $async_complete_count -gt 0 ]; then
        echo "✅ 异步检查正常完成"
    else
        echo "❌ 异步检查未完成"
    fi
    
    echo
    echo "--- 定时器回调分析 ---"
    
    # 检查定时器回调模式
    if grep -q "连通性检查定时器触发（异步模式）" "$LOG_DIR/network_system.log"; then
        echo "✅ 定时器回调使用异步模式"
        
        # 检查是否使用上次检测结果
        if grep -q "使用上次检测结果" "$LOG_DIR/network_system.log"; then
            echo "✅ 定时器回调使用缓存的检测结果"
        else
            echo "❌ 定时器回调未使用缓存的检测结果"
        fi
        
    else
        echo "❌ 定时器回调未使用异步模式"
    fi
    
    # 统计定时器触发次数
    timer_trigger_count=$(grep -c "连通性检查定时器触发" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    echo "连通性检查定时器触发次数: $timer_trigger_count"
    
    echo
    echo "--- 性能优化效果分析 ---"
    
    # 检查是否避免了重复参数获取
    param_get_count=$(grep -c "get_parameter.*connectivity\." "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    echo "参数获取调用次数: $param_get_count"
    
    if [ $param_get_count -le 10 ]; then
        echo "✅ 参数获取次数合理（避免了重复获取）"
    else
        echo "⚠️ 参数获取次数较多，可能存在重复获取"
    fi
    
    # 检查异步检查的并发控制
    if grep -q "仍在进行中，跳过本次检查" "$LOG_DIR/network_system.log"; then
        echo "✅ 异步检查并发控制正常工作"
    else
        echo "ℹ️ 未检测到并发控制触发（可能检查间隔足够长）"
    fi
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 性能对比分析
echo "5. 性能优化效果评估..."

# 计算优化效果
init_param_count=$(grep -c "获取连通性检查参数" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
runtime_param_count=$(grep -c "get_parameter.*connectivity\." "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")

echo "初始化时参数获取: $init_param_count 次"
echo "运行时参数获取: $runtime_param_count 次"

if [ $init_param_count -gt 0 ] && [ $runtime_param_count -le $init_param_count ]; then
    echo "✅ 参数缓存优化生效"
else
    echo "❌ 参数缓存优化可能未生效"
fi

# 异步检查效果
async_mode_count=$(grep -c "异步模式" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
if [ $async_mode_count -gt 0 ]; then
    echo "✅ 异步检查模式生效"
else
    echo "❌ 异步检查模式未生效"
fi

echo

# 总体评估
echo "6. 总体评估..."

score=0
max_score=5

# 评分标准
if [ $init_param_count -gt 0 ]; then ((score++)); fi
if [ $async_start_count -gt 0 ]; then ((score++)); fi
if [ $async_complete_count -gt 0 ]; then ((score++)); fi
if [ $timer_trigger_count -gt 0 ]; then ((score++)); fi
if [ $param_get_count -le 10 ]; then ((score++)); fi

echo "优化效果评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 异步连通性检查和参数缓存优化完全成功！"
    echo "  ✅ 参数在初始化时正确缓存"
    echo "  ✅ 异步连通性检查正常工作"
    echo "  ✅ 定时器回调使用缓存结果"
    echo "  ✅ 避免了重复参数获取"
    echo "  ✅ 性能优化效果显著"
elif [ $score -ge 3 ]; then
    echo "✅ 异步连通性检查和参数缓存优化基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 异步连通性检查和参数缓存优化需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示优化优势
echo "7. 优化优势总结..."
echo "✅ 参数缓存优化:"
echo "   • 避免每次连通性检查时重复获取参数"
echo "   • 减少ROS参数服务器访问次数"
echo "   • 提高连通性检查性能"

echo
echo "✅ 异步连通性检查优化:"
echo "   • 避免定时器回调被长时间阻塞"
echo "   • 提高系统响应性"
echo "   • 支持并发控制，避免重复检查"
echo "   • 使用上次检测结果，确保及时状态发布"

echo
echo "✅ 整体性能提升:"
echo "   • 减少同步等待时间"
echo "   • 提高网络监控效率"
echo "   • 增强系统稳定性"

echo
echo "详细日志文件:"
echo "  网络管理系统: $LOG_DIR/network_system.log"
echo "  测试参数文件: $LOG_DIR/test_params.yaml"

echo
echo "🎊 异步连通性检查和参数缓存优化测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
