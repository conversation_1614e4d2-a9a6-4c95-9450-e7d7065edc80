#!/bin/bash

# 运行双节点 C++ 延迟测试

echo "=== ROS2 C++ 双节点延迟测试 ==="

# 设置环境
export ROS_DISTRO=humble
export LD_LIBRARY_PATH=/opt/ros/humble/lib:$LD_LIBRARY_PATH

# 启动 ROS2 daemon
echo "启动 ROS2 daemon..."
ros2 daemon start
sleep 1

echo "启动订阅者节点..."
# 在后台启动订阅者
cd build
./simple_latency_test sub > subscriber_output.log 2>&1 &
SUB_PID=$!

echo "订阅者已启动 (PID: $SUB_PID)"
echo "等待订阅者准备..."
sleep 3

echo "启动发布者节点..."
# 启动发布者，运行30秒后自动停止
timeout 30s ./simple_latency_test pub > publisher_output.log 2>&1 &
PUB_PID=$!

echo "发布者已启动 (PID: $PUB_PID)"
echo "等待测试完成..."

# 等待发布者完成
wait $PUB_PID

echo "发布者测试完成，等待订阅者处理剩余消息..."
sleep 5

# 停止订阅者
kill $SUB_PID 2>/dev/null
wait $SUB_PID 2>/dev/null

echo "测试完成！"
echo

echo "=== 发布者输出 ==="
cat publisher_output.log
echo

echo "=== 订阅者输出 ==="
cat subscriber_output.log

echo
echo "=== 测试总结 ==="

# 分析结果
if grep -q "延迟统计" subscriber_output.log; then
    echo "✅ 成功接收到消息并计算延迟"
    
    # 提取延迟信息
    echo "延迟详情:"
    grep -A 10 "延迟统计" subscriber_output.log | tail -6
    
    # 提取延迟评价
    if grep -q "优秀" subscriber_output.log; then
        echo "🎉 延迟性能: 优秀"
    elif grep -q "良好" subscriber_output.log; then
        echo "✅ 延迟性能: 良好"
    elif grep -q "一般" subscriber_output.log; then
        echo "⚠️ 延迟性能: 一般"
    else
        echo "❌ 延迟性能: 较差"
    fi
else
    echo "❌ 没有接收到消息或计算延迟失败"
fi

echo
echo "详细日志文件:"
echo "  发布者: $(pwd)/publisher_output.log"
echo "  订阅者: $(pwd)/subscriber_output.log"

echo
echo "🎊 C++ 双节点延迟测试完成！"
