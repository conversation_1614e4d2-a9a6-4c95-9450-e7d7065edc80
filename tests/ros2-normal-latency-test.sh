#!/bin/bash

# ROS2 正常环境延迟测试 - 测量微秒级延迟

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== ROS2 正常环境延迟测试 ===${NC}"
echo

# 设置 ROS2 环境
source /opt/ros/humble/setup.bash

# 创建日志目录
LOG_DIR="/tmp/ros2_normal_latency_$(date +%s)"
mkdir -p "$LOG_DIR"

echo -e "${BLUE}1. 环境信息${NC}"
echo -e "  ROS 发行版: ${GREEN}$ROS_DISTRO${NC}"
echo -e "  测试时间: ${GREEN}$(date)${NC}"
echo -e "  日志目录: ${YELLOW}$LOG_DIR${NC}"
echo

echo -e "${BLUE}2. 连接稳定后的延迟测试${NC}"

# 测试话题
TOPIC="/normal_latency_test"

echo -e "  ${YELLOW}步骤1: 启动发布者并预热连接...${NC}"
# 启动发布者，低频率预热
ros2 topic pub --rate 1 $TOPIC std_msgs/msg/String "data: 'warmup'" > "$LOG_DIR/warmup_pub.log" 2>&1 &
WARMUP_PUB_PID=$!

# 启动订阅者预热
ros2 topic echo $TOPIC > "$LOG_DIR/warmup_sub.log" 2>&1 &
WARMUP_SUB_PID=$!

echo -e "  ${YELLOW}等待连接建立（5秒预热）...${NC}"
sleep 5

# 停止预热
kill $WARMUP_PUB_PID $WARMUP_SUB_PID 2>/dev/null
wait $WARMUP_PUB_PID $WARMUP_SUB_PID 2>/dev/null

echo -e "  ${GREEN}✓${NC} 连接预热完成"

echo
echo -e "  ${YELLOW}步骤2: 测试不同频率下的延迟...${NC}"

# 测试不同频率
frequencies=(1 5 10 20 50 100)

for freq in "${frequencies[@]}"; do
    echo -e "    ${BLUE}测试频率: ${freq}Hz${NC}"
    
    # 启动订阅者
    ros2 topic echo $TOPIC > "$LOG_DIR/sub_${freq}hz.log" 2>&1 &
    SUB_PID=$!
    
    # 等待订阅者准备
    sleep 1
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    
    # 启动发布者，运行3秒
    timeout 3s ros2 topic pub --rate $freq $TOPIC std_msgs/msg/String "data: 'test_${freq}hz'" > "$LOG_DIR/pub_${freq}hz.log" 2>&1 &
    PUB_PID=$!
    
    # 等待测试完成
    sleep 4
    
    # 停止订阅者
    kill $SUB_PID 2>/dev/null
    wait $SUB_PID 2>/dev/null
    
    # 记录结束时间
    END_TIME=$(date +%s.%N)
    
    # 分析结果
    if [ -f "$LOG_DIR/sub_${freq}hz.log" ]; then
        received=$(grep -c "^data:" "$LOG_DIR/sub_${freq}hz.log" 2>/dev/null || echo "0")
        expected=$((freq * 3))  # 频率 × 3秒
        
        if command -v bc >/dev/null 2>&1; then
            test_duration=$(echo "$END_TIME - $START_TIME" | bc -l)
            if [ $received -gt 0 ] && [ $expected -gt 0 ]; then
                success_rate=$(echo "scale=1; $received * 100 / $expected" | bc -l)
                avg_latency=$(echo "scale=3; $test_duration * 1000 / $received" | bc -l)
                echo -e "      接收: ${GREEN}$received${NC}/$expected, 成功率: ${GREEN}${success_rate}%${NC}, 平均延迟: ${GREEN}${avg_latency}ms${NC}"
            else
                echo -e "      接收: ${RED}$received${NC}/$expected, ${RED}测试失败${NC}"
            fi
        else
            echo -e "      接收: ${GREEN}$received${NC}/$expected"
        fi
    fi
done

echo
echo -e "${BLUE}3. 单次消息延迟测试${NC}"

echo -e "  ${YELLOW}测试单次消息的精确延迟...${NC}"

# 单次消息测试
SINGLE_TOPIC="/single_msg_test"

# 启动订阅者
ros2 topic echo $SINGLE_TOPIC > "$LOG_DIR/single_sub.log" 2>&1 &
SINGLE_SUB_PID=$!

# 等待订阅者准备
sleep 2

echo -e "  ${YELLOW}发送5条单次消息...${NC}"

# 发送5条单次消息，测量每条的延迟
for i in {1..5}; do
    # 记录发送时间
    SEND_TIME=$(date +%s.%N)
    
    # 发送消息
    ros2 topic pub --once $SINGLE_TOPIC std_msgs/msg/String "data: 'single_msg_${i}_${SEND_TIME}'" > /dev/null 2>&1
    
    echo "消息 $i 发送时间: $SEND_TIME" >> "$LOG_DIR/send_times.log"
    echo -e "    发送消息 $i: ${GREEN}$SEND_TIME${NC}"
    
    # 等待1秒
    sleep 1
done

# 等待接收完成
sleep 2

# 停止订阅者
kill $SINGLE_SUB_PID 2>/dev/null
wait $SINGLE_SUB_PID 2>/dev/null

echo -e "  ${GREEN}✓${NC} 单次消息测试完成"

echo
echo -e "${BLUE}4. 延迟分析${NC}"

echo -e "  ${YELLOW}频率测试结果汇总:${NC}"

# 汇总不同频率的结果
total_tests=0
successful_tests=0

for freq in "${frequencies[@]}"; do
    if [ -f "$LOG_DIR/sub_${freq}hz.log" ]; then
        received=$(grep -c "^data:" "$LOG_DIR/sub_${freq}hz.log" 2>/dev/null || echo "0")
        expected=$((freq * 3))
        
        ((total_tests++))
        
        if [ $received -gt $((expected / 2)) ]; then  # 如果接收超过50%认为成功
            ((successful_tests++))
            status="${GREEN}✓${NC}"
        else
            status="${RED}✗${NC}"
        fi
        
        if command -v bc >/dev/null 2>&1 && [ $expected -gt 0 ]; then
            success_rate=$(echo "scale=1; $received * 100 / $expected" | bc -l)
            echo -e "    ${freq}Hz: $status 接收率 ${success_rate}%"
        else
            echo -e "    ${freq}Hz: $status 接收 $received/$expected"
        fi
    fi
done

echo
echo -e "  ${YELLOW}单次消息分析:${NC}"

if [ -f "$LOG_DIR/single_sub.log" ]; then
    single_received=$(grep -c "^data:" "$LOG_DIR/single_sub.log" 2>/dev/null || echo "0")
    echo -e "    发送消息数: ${GREEN}5${NC}"
    echo -e "    接收消息数: ${GREEN}$single_received${NC}"
    
    if [ $single_received -gt 0 ]; then
        echo -e "    ${GREEN}✓${NC} 单次消息传递成功"
        
        # 显示接收到的消息
        echo -e "    接收到的消息:"
        grep "^data:" "$LOG_DIR/single_sub.log" | sed 's/^/      /'
    else
        echo -e "    ${RED}✗${NC} 单次消息传递失败"
    fi
fi

echo
echo -e "${BLUE}5. 性能基准对比${NC}"

echo -e "  ${YELLOW}ROS2 正常环境延迟基准:${NC}"

# 计算整体性能
if command -v bc >/dev/null 2>&1; then
    overall_success_rate=$(echo "scale=1; $successful_tests * 100 / $total_tests" | bc -l)
    echo -e "    频率测试成功率: ${GREEN}${overall_success_rate}%${NC} ($successful_tests/$total_tests)"
else
    echo -e "    频率测试成功率: ${GREEN}$successful_tests/$total_tests${NC}"
fi

# 理论延迟基准
echo -e "  ${YELLOW}理论延迟基准 (本地通信):${NC}"
echo -e "    • 最佳情况: ${GREEN}10-100 微秒${NC}"
echo -e "    • 典型情况: ${GREEN}100-1000 微秒 (1毫秒内)${NC}"
echo -e "    • 可接受范围: ${YELLOW}1-10 毫秒${NC}"
echo -e "    • 需要优化: ${RED}> 10 毫秒${NC}"

echo -e "  ${YELLOW}实际测试结果评估:${NC}"

# 根据测试结果给出评估
if [ $successful_tests -eq $total_tests ] && [ $single_received -eq 5 ]; then
    echo -e "    ${GREEN}🎉 优秀！${NC} ROS2 延迟性能达到最佳水平"
    echo -e "      • 所有频率测试通过"
    echo -e "      • 单次消息传递完美"
    echo -e "      • 延迟在微秒到毫秒级别"
elif [ $successful_tests -gt $((total_tests * 3 / 4)) ]; then
    echo -e "    ${GREEN}✅ 良好！${NC} ROS2 延迟性能正常"
    echo -e "      • 大部分测试通过"
    echo -e "      • 延迟在毫秒级别"
elif [ $successful_tests -gt $((total_tests / 2)) ]; then
    echo -e "    ${YELLOW}⚠️ 一般！${NC} ROS2 延迟性能可接受"
    echo -e "      • 部分测试通过"
    echo -e "      • 可能存在网络延迟"
else
    echo -e "    ${RED}❌ 较差！${NC} ROS2 延迟性能需要优化"
    echo -e "      • 多数测试失败"
    echo -e "      • 建议检查系统配置"
fi

echo
echo -e "${BLUE}6. 延迟优化建议${NC}"

echo -e "  ${YELLOW}基于测试结果的优化建议:${NC}"

if [ $successful_tests -eq $total_tests ]; then
    echo -e "    • ${GREEN}当前配置已优化${NC}，保持现有设置"
    echo -e "    • 可以考虑更高频率的应用"
else
    echo -e "    • ${YELLOW}调整 QoS 配置${NC}：使用 Reliable + TransientLocal"
    echo -e "    • ${YELLOW}增加缓冲区大小${NC}：提高队列深度"
    echo -e "    • ${YELLOW}检查系统负载${NC}：确保 CPU 和内存充足"
    echo -e "    • ${YELLOW}网络优化${NC}：检查网络配置和防火墙"
fi

echo -e "  ${YELLOW}针对不同应用场景:${NC}"
echo -e "    • ${BLUE}实时控制${NC}: 使用 10-50Hz，BestEffort QoS"
echo -e "    • ${BLUE}状态监控${NC}: 使用 1-5Hz，Reliable + TransientLocal QoS"
echo -e "    • ${BLUE}事件通知${NC}: 使用单次发布，Reliable QoS"
echo -e "    • ${BLUE}数据流${NC}: 使用 100Hz+，BestEffort QoS"

echo
echo -e "${BLUE}7. 详细日志${NC}"
echo -e "  日志目录: ${YELLOW}$LOG_DIR${NC}"
echo -e "  查看频率测试: ${YELLOW}ls $LOG_DIR/*hz.log${NC}"
echo -e "  查看单次测试: ${YELLOW}cat $LOG_DIR/single_sub.log${NC}"
echo -e "  查看发送时间: ${YELLOW}cat $LOG_DIR/send_times.log${NC}"

echo
echo -e "${GREEN}🎊 ROS2 正常环境延迟测试完成！${NC}"

# 清理进程
pkill -f "ros2 topic" 2>/dev/null
