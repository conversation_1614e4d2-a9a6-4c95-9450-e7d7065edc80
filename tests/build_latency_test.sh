#!/bin/bash

# 编译延迟测试程序

echo "=== 编译 ROS2 C++ 延迟测试程序 ==="

# 设置 ROS2 环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# 创建构建目录
mkdir -p build
cd build

# 使用 cmake 编译
echo "配置 CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

echo "编译程序..."
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "可执行文件: $(pwd)/latency_test_node"
    
    # 运行测试
    echo ""
    echo "=== 运行延迟测试 ==="
    ./latency_test_node
else
    echo "❌ 编译失败！"
    exit 1
fi
