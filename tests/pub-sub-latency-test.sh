#!/bin/bash

# ROS2 发布订阅延迟测试脚本
# 测试发布者发送消息到订阅者接收的延迟

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

source /opt/ros/humble/setup.bash

echo -e "${BLUE}=== ROS2 发布订阅延迟测试 ===${NC}"
echo

# 检查 ROS2 环境
if [ -z "$ROS_DISTRO" ]; then
    echo -e "${RED}错误: ROS2 环境未设置${NC}"
    echo "请先 source ROS2 环境"
    exit 1
fi

echo -e "${BLUE}1. 检查测试环境${NC}"
echo -e "  ROS 发行版: ${GREEN}$ROS_DISTRO${NC}"

# 检查项目是否已构建
if [ ! -d "install" ]; then
    echo -e "${YELLOW}警告: install 目录不存在，请先构建项目${NC}"
    echo "运行: ./build.sh"
    exit 1
fi

# Source 工作空间
if [ -f "install/setup.bash" ]; then
    source install/setup.bash
    echo -e "  ${GREEN}✓${NC} 已 source 工作空间"
else
    # 尝试使用架构特定的路径
    if [ -f "install/x86_64/Debug/setup.bash" ]; then
        source install/x86_64/Debug/setup.bash
        echo -e "  ${GREEN}✓${NC} 已 source 工作空间 (架构特定路径)"
    else
        echo -e "  ${RED}✗${NC} 未找到 setup.bash 文件"
        echo "请先运行 ./build.sh 构建项目"
        exit 1
    fi
fi

echo

# 创建测试日志目录
LOG_DIR="/tmp/pub_sub_latency_test_$(date +%s)"
mkdir -p "$LOG_DIR"
echo -e "${BLUE}2. 创建测试日志目录: ${YELLOW}$LOG_DIR${NC}"
echo

# 测试1: 基本发布订阅延迟测试
test_basic_pub_sub_latency() {
    echo -e "${BLUE}3. 基本发布订阅延迟测试${NC}"
    
    # 启动一个简单的发布者
    echo -e "  ${YELLOW}启动测试发布者...${NC}"
    ros2 topic pub --rate 1 /test_topic std_msgs/msg/String "data: 'test_message_$(date +%s)'" > "$LOG_DIR/publisher.log" 2>&1 &
    PUB_PID=$!
    
    # 启动订阅者并记录时间戳
    echo -e "  ${YELLOW}启动测试订阅者...${NC}"
    timeout 10s ros2 topic echo /test_topic --csv > "$LOG_DIR/subscriber.log" 2>&1 &
    SUB_PID=$!
    
    # 等待测试完成
    echo -e "  ${YELLOW}运行测试 10 秒...${NC}"
    sleep 10
    
    # 停止进程
    kill $PUB_PID $SUB_PID 2>/dev/null
    wait $PUB_PID $SUB_PID 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 基本发布订阅测试完成"
}

# 测试2: 网络管理器消息延迟测试
test_network_manager_latency() {
    echo
    echo -e "${BLUE}4. 网络管理器消息延迟测试${NC}"
    
    # 启动网络管理器
    echo -e "  ${YELLOW}启动网络管理器...${NC}"
    timeout 15s ros2 run gen3_network_manager_core network_manager_node > "$LOG_DIR/network_manager.log" 2>&1 &
    MANAGER_PID=$!
    
    # 等待网络管理器启动
    sleep 2
    
    # 启动订阅者监听网络状态
    echo -e "  ${YELLOW}启动网络状态订阅者...${NC}"
    timeout 12s ros2 topic echo /network_status --csv > "$LOG_DIR/network_status_sub.log" 2>&1 &
    STATUS_SUB_PID=$!
    
    # 启动订阅者监听切换状态
    echo -e "  ${YELLOW}启动切换状态订阅者...${NC}"
    timeout 12s ros2 topic echo /switch_status --csv > "$LOG_DIR/switch_status_sub.log" 2>&1 &
    SWITCH_SUB_PID=$!
    
    # 等待测试完成
    echo -e "  ${YELLOW}运行网络管理器测试 15 秒...${NC}"
    sleep 15
    
    # 停止所有进程
    kill $MANAGER_PID $STATUS_SUB_PID $SWITCH_SUB_PID 2>/dev/null
    wait $MANAGER_PID $STATUS_SUB_PID $SWITCH_SUB_PID 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 网络管理器消息延迟测试完成"
}

# 测试3: 连接建立时间测试
test_connection_establishment() {
    echo
    echo -e "${BLUE}5. 连接建立时间测试${NC}"
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    
    # 启动发布者
    echo -e "  ${YELLOW}启动发布者...${NC}"
    ros2 topic pub --once /connection_test std_msgs/msg/String "data: 'connection_test'" > "$LOG_DIR/connection_pub.log" 2>&1 &
    CONN_PUB_PID=$!
    
    # 立即启动订阅者
    echo -e "  ${YELLOW}启动订阅者...${NC}"
    timeout 5s ros2 topic echo /connection_test --csv > "$LOG_DIR/connection_sub.log" 2>&1 &
    CONN_SUB_PID=$!
    
    # 等待连接建立
    sleep 5
    
    # 记录结束时间
    END_TIME=$(date +%s.%N)
    
    # 计算连接建立时间
    CONNECTION_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
    
    # 停止进程
    kill $CONN_PUB_PID $CONN_SUB_PID 2>/dev/null
    wait $CONN_PUB_PID $CONN_SUB_PID 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 连接建立时间: ${YELLOW}${CONNECTION_TIME}${NC} 秒"
}

# 分析测试结果
analyze_results() {
    echo
    echo -e "${BLUE}6. 分析测试结果${NC}"
    
    # 分析基本发布订阅
    if [ -f "$LOG_DIR/subscriber.log" ]; then
        echo -e "${YELLOW}基本发布订阅结果:${NC}"
        msg_count=$(wc -l < "$LOG_DIR/subscriber.log" 2>/dev/null || echo "0")
        echo -e "  接收到消息数量: ${GREEN}$msg_count${NC}"
        
        if [ "$msg_count" -gt 0 ]; then
            echo -e "  ${GREEN}✓${NC} 基本发布订阅正常工作"
        else
            echo -e "  ${RED}✗${NC} 基本发布订阅未接收到消息"
        fi
    fi
    
    # 分析网络管理器消息
    echo -e "${YELLOW}网络管理器消息结果:${NC}"
    
    if [ -f "$LOG_DIR/network_status_sub.log" ]; then
        status_count=$(wc -l < "$LOG_DIR/network_status_sub.log" 2>/dev/null || echo "0")
        echo -e "  网络状态消息数量: ${GREEN}$status_count${NC}"
    else
        status_count=0
        echo -e "  网络状态消息数量: ${RED}0${NC}"
    fi
    
    if [ -f "$LOG_DIR/switch_status_sub.log" ]; then
        switch_count=$(wc -l < "$LOG_DIR/switch_status_sub.log" 2>/dev/null || echo "0")
        echo -e "  切换状态消息数量: ${GREEN}$switch_count${NC}"
    else
        switch_count=0
        echo -e "  切换状态消息数量: ${RED}0${NC}"
    fi
    
    # 检查网络管理器日志中的发布信息
    if [ -f "$LOG_DIR/network_manager.log" ]; then
        pub_count=$(grep -c -E "(发布|publish|PUB)" "$LOG_DIR/network_manager.log" 2>/dev/null || echo "0")
        echo -e "  网络管理器发布次数: ${GREEN}$pub_count${NC}"

        # 检查初始化定时器执行
        if grep -q -E "(一次性初始化定时器触发|INIT_TIMER.*触发)" "$LOG_DIR/network_manager.log"; then
            echo -e "  ${GREEN}✓${NC} 一次性初始化定时器已触发"
        else
            echo -e "  ${YELLOW}○${NC} 一次性初始化定时器未触发"
            # 显示一些日志内容用于调试
            echo -e "  ${BLUE}调试信息:${NC} 日志文件前10行:"
            head -10 "$LOG_DIR/network_manager.log" | sed 's/^/    /'
        fi
    fi
}

# 生成延迟分析报告
generate_latency_report() {
    echo
    echo -e "${BLUE}7. 生成延迟分析报告${NC}"
    
    REPORT_FILE="$LOG_DIR/pub_sub_latency_report.txt"
    
    echo "ROS2 发布订阅延迟测试报告" > "$REPORT_FILE"
    echo "生成时间: $(date)" >> "$REPORT_FILE"
    echo "======================================" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # 基本统计
    echo "1. 基本统计:" >> "$REPORT_FILE"
    if [ -f "$LOG_DIR/subscriber.log" ]; then
        msg_count=$(wc -l < "$LOG_DIR/subscriber.log" 2>/dev/null || echo "0")
        echo "基本发布订阅消息数量: $msg_count" >> "$REPORT_FILE"
    fi
    
    if [ -f "$LOG_DIR/network_status_sub.log" ]; then
        status_count=$(wc -l < "$LOG_DIR/network_status_sub.log" 2>/dev/null || echo "0")
        echo "网络状态消息数量: $status_count" >> "$REPORT_FILE"
    fi
    
    if [ -f "$LOG_DIR/switch_status_sub.log" ]; then
        switch_count=$(wc -l < "$LOG_DIR/switch_status_sub.log" 2>/dev/null || echo "0")
        echo "切换状态消息数量: $switch_count" >> "$REPORT_FILE"
    fi
    echo >> "$REPORT_FILE"
    
    # 网络管理器发布统计
    echo "2. 网络管理器发布统计:" >> "$REPORT_FILE"
    if [ -f "$LOG_DIR/network_manager.log" ]; then
        pub_count=$(grep -c -E "(发布|publish|PUB)" "$LOG_DIR/network_manager.log" 2>/dev/null || echo "0")
        echo "总发布次数: $pub_count" >> "$REPORT_FILE"

        # 提取发布相关日志
        echo >> "$REPORT_FILE"
        echo "发布相关日志:" >> "$REPORT_FILE"
        grep -E "(发布|publish|PUB|一次性初始化定时器|INIT_TIMER)" "$LOG_DIR/network_manager.log" | head -10 >> "$REPORT_FILE" 2>/dev/null || echo "未找到相关日志" >> "$REPORT_FILE"
    fi
    
    echo -e "  ${GREEN}✓${NC} 延迟分析报告已生成: $REPORT_FILE"
}

# 清理函数
cleanup() {
    echo
    echo -e "${BLUE}8. 清理测试环境${NC}"
    
    # 确保所有测试进程都已停止
    pkill -f "ros2 topic" 2>/dev/null
    pkill -f "network_manager_node" 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} 测试进程已清理"
    echo -e "  ${BLUE}日志目录:${NC} $LOG_DIR"
}

# 主测试流程
main() {
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试
    test_basic_pub_sub_latency
    test_network_manager_latency
    test_connection_establishment
    
    # 分析结果
    analyze_results
    
    # 生成报告
    generate_latency_report
    
    echo
    echo -e "${BLUE}9. 测试总结${NC}"
    
    # 检查测试结果
    success_count=0
    total_checks=3
    
    if [ -f "$LOG_DIR/subscriber.log" ] && [ $(wc -l < "$LOG_DIR/subscriber.log" 2>/dev/null || echo "0") -gt 0 ]; then
        ((success_count++))
    fi
    
    if [ -f "$LOG_DIR/network_manager.log" ] && grep -q "一次性初始化定时器触发" "$LOG_DIR/network_manager.log"; then
        ((success_count++))
    fi
    
    if [ -f "$LOG_DIR/network_manager.log" ]; then
        pub_count=$(grep -c -E "(发布|publish|PUB)" "$LOG_DIR/network_manager.log" 2>/dev/null || echo "0")
        if [ "$pub_count" -gt 0 ]; then
            ((success_count++))
        fi
    fi
    
    echo -e "  测试通过率: ${GREEN}$success_count/$total_checks${NC}"
    
    if [ $success_count -eq $total_checks ]; then
        echo -e "  ${GREEN}✅ 发布订阅延迟测试通过！${NC}"
        echo -e "    • 基本发布订阅正常工作"
        echo -e "    • 网络管理器消息传递正常"
        echo -e "    • 一次性初始化定时器正常触发"
    elif [ $success_count -gt 1 ]; then
        echo -e "  ${YELLOW}⚠️ 发布订阅延迟测试部分通过${NC}"
        echo -e "    • 大部分功能正常"
        echo -e "    • 可能存在轻微延迟问题"
    else
        echo -e "  ${RED}❌ 发布订阅延迟测试失败${NC}"
        echo -e "    • 请检查 ROS2 环境配置"
        echo -e "    • 可能需要重新构建项目"
    fi
    
    echo
    echo -e "${BLUE}💡 关于 ROS2 消息传递延迟:${NC}"
    echo -e "  1. 发布者可以立即发布消息"
    echo -e "  2. 订阅者只有在 rclcpp::spin() 运行时才能接收消息"
    echo -e "  3. 连接建立需要时间（通常几百毫秒到几秒）"
    echo -e "  4. 一次性初始化定时器确保在合适时机执行逻辑"
    echo -e "  5. 本地通信延迟通常在微秒到毫秒级别"
}

# 执行主函数
main

echo
echo -e "${GREEN}🎊 ROS2 发布订阅延迟测试完成！${NC}"
