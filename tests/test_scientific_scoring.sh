#!/bin/bash

# 测试科学的网络评分算法

echo "=== 测试科学的网络评分算法 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/scientific_scoring_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 创建测试参数文件（包含科学评分权重）
cat > "$LOG_DIR/test_params.yaml" << EOF
network_manager_node:
  ros__parameters:
    # 基本定时器间隔
    network_check_interval: 1.0
    connectivity_check_interval: 10.0
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "usb0"
    
    # 网络切换配置
    network_switch:
      auto_switch_enabled: true
      preferred_network_type: 1
      min_signal_strength: -70
      min_connectivity_score: 50.0
      
      # 科学评分权重配置（去掉带宽评分）
      quality_weights:
        latency: 0.3          # 延迟权重30%
        packet_loss: 0.25     # 丢包率权重25%
        signal_strength: 0.2  # 信号强度权重20%
        connectivity: 0.25    # 连通性权重25%
        # download_speed: 0.0   # 暂时去掉带宽评分
        # upload_speed: 0.0     # 暂时去掉带宽评分
    
    # 质量检查配置
    quality_check_interval: 10.0
    switch_monitor_interval: 5.0
    status_update_interval: 2.0
    
    # 连通性检查参数
    connectivity.dns_server: "www.google.com"
    connectivity.internet_server: "8.8.8.8"
    connectivity.dns_timeout_ms: 3000
    connectivity.ping_timeout_ms: 2500
    connectivity.gateway_timeout_ms: 1500
    connectivity.external_timeout_ms: 3000
EOF

echo "1. 启动网络管理系统（使用科学评分算法）..."
# 启动网络管理器节点，使用测试参数文件
ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$LOG_DIR/test_params.yaml" > "$LOG_DIR/network_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（30秒）..."
echo "   - 观察科学评分算法的执行"
echo "   - 监控各项评分指标"
echo "   - 验证评分逻辑的科学性"
sleep 30

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析科学评分算法测试结果..."

# 分析系统日志
echo "=== 科学评分算法分析 ==="
if [ -f "$LOG_DIR/network_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    echo
    echo "--- 评分算法执行分析 ---"
    
    # 检查评分计算
    score_calc_count=$(grep -c "网络评分计算完成" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    score_detail_count=$(grep -c "各项得分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "评分计算次数: $score_calc_count"
    echo "详细评分记录次数: $score_detail_count"
    
    if [ $score_calc_count -gt 0 ]; then
        echo "✅ 科学评分算法正常执行"
    else
        echo "❌ 科学评分算法未执行"
    fi
    
    echo
    echo "--- 科学评分组件分析 ---"
    
    # 检查各个评分组件
    latency_score_count=$(grep -c "延迟评分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    packet_loss_score_count=$(grep -c "丢包率评分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    signal_score_count=$(grep -c "信号强度评分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    connectivity_score_count=$(grep -c "连通性评分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "延迟评分计算次数: $latency_score_count"
    echo "丢包率评分计算次数: $packet_loss_score_count"
    echo "信号强度评分计算次数: $signal_score_count"
    echo "连通性评分计算次数: $connectivity_score_count"
    
    # 验证各组件是否工作
    components_working=0
    if [ $latency_score_count -gt 0 ]; then
        echo "✅ 延迟评分组件正常工作"
        ((components_working++))
    else
        echo "❌ 延迟评分组件未工作"
    fi
    
    if [ $packet_loss_score_count -gt 0 ]; then
        echo "✅ 丢包率评分组件正常工作"
        ((components_working++))
    else
        echo "❌ 丢包率评分组件未工作"
    fi
    
    if [ $signal_score_count -gt 0 ]; then
        echo "✅ 信号强度评分组件正常工作"
        ((components_working++))
    else
        echo "❌ 信号强度评分组件未工作"
    fi
    
    if [ $connectivity_score_count -gt 0 ]; then
        echo "✅ 连通性评分组件正常工作"
        ((components_working++))
    else
        echo "❌ 连通性评分组件未工作"
    fi
    
    echo
    echo "--- 评分结果分析 ---"
    
    # 显示实际的评分结果
    echo "实际评分结果示例:"
    grep "网络评分计算完成" "$LOG_DIR/network_system.log" | head -3
    
    echo
    echo "详细评分分解示例:"
    grep "各项得分" "$LOG_DIR/network_system.log" | head -3
    
    echo
    echo "--- 科学性验证 ---"
    
    # 检查网络类型修正因子
    type_factor_count=$(grep -c "网络类型修正因子\|稳定性因子" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "修正因子应用次数: $type_factor_count"
    
    if [ $type_factor_count -gt 0 ]; then
        echo "✅ 网络类型和稳定性修正因子正常应用"
    else
        echo "ℹ️ 修正因子可能在DEBUG级别，未显示在日志中"
    fi
    
    # 检查是否有网络质量问题检测
    quality_issue_count=$(grep -c "网络质量问题\|连接质量评分" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    
    echo "网络质量问题检测次数: $quality_issue_count"
    
    if [ $quality_issue_count -gt 0 ]; then
        echo "✅ 网络质量问题检测正常工作"
        echo "质量问题示例:"
        grep "网络质量问题\|连接质量评分" "$LOG_DIR/network_system.log" | head -2
    else
        echo "ℹ️ 未检测到网络质量问题（可能网络状况良好）"
    fi
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 总体评估
echo "5. 科学评分算法总体评估..."

score=0
max_score=5

# 评分标准
if [ $score_calc_count -gt 0 ]; then ((score++)); fi
if [ $components_working -ge 2 ]; then ((score++)); fi
if [ $score_detail_count -gt 0 ]; then ((score++)); fi
if [ $quality_issue_count -gt 0 ]; then ((score++)); fi
if [ $latency_score_count -gt 0 ] && [ $connectivity_score_count -gt 0 ]; then ((score++)); fi

echo "科学评分算法评估得分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 科学评分算法完全成功！"
    echo "  ✅ 评分算法正常执行"
    echo "  ✅ 各评分组件正常工作"
    echo "  ✅ 详细评分记录完整"
    echo "  ✅ 网络质量检测有效"
    echo "  ✅ 科学性验证通过"
elif [ $score -ge 3 ]; then
    echo "✅ 科学评分算法基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 科学评分算法需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示科学评分算法的优势
echo "6. 科学评分算法优势总结..."
echo "✅ 延迟评分优化:"
echo "   • 使用指数函数，更符合人类感知"
echo "   • 基于网络类型的期望延迟"
echo "   • 对极低延迟给予奖励"

echo
echo "✅ 丢包率评分优化:"
echo "   • 使用指数衰减函数"
echo "   • 即使小丢包率也显著影响评分"
echo "   • 零丢包给予满分奖励"

echo
echo "✅ 信号强度评分优化:"
echo "   • 基于实际信号强度标准"
echo "   • 不同网络类型差异化处理"
echo "   • WiFi和5G分别优化"

echo
echo "✅ 连通性评分优化:"
echo "   • 网关、DNS、互联网分权重评估"
echo "   • DNS解析时间修正"
echo "   • 多DNS服务器奖励机制"

echo
echo "✅ 整体算法优化:"
echo "   • 网络类型修正因子"
echo "   • 稳定性修正机制"
echo "   • 科学的权重分配"
echo "   • 暂时去掉带宽评分，简化算法"

echo
echo "详细日志文件:"
echo "  网络管理系统: $LOG_DIR/network_system.log"
echo "  测试参数文件: $LOG_DIR/test_params.yaml"

echo
echo "🎊 科学评分算法测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
