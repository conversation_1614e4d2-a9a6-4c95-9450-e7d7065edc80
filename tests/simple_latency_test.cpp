#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <chrono>
#include <vector>
#include <numeric>
#include <thread>

using namespace std::chrono_literals;

// 发布者节点
class LatencyPublisher : public rclcpp::Node
{
public:
    LatencyPublisher() : Node("latency_publisher")
    {
        publisher_ = this->create_publisher<std_msgs::msg::String>("latency_test", 10);
        
        // 等待订阅者连接
        RCLCPP_INFO(this->get_logger(), "发布者启动，等待订阅者连接...");
        std::this_thread::sleep_for(3s);
        
        // 开始发布测试消息
        start_publishing();
    }

private:
    void start_publishing()
    {
        RCLCPP_INFO(this->get_logger(), "开始发布延迟测试消息...");
        
        // 测试不同频率
        test_frequency(1.0, 10, "1Hz测试");
        test_frequency(10.0, 20, "10Hz测试");
        test_frequency(100.0, 50, "100Hz测试");
        
        RCLCPP_INFO(this->get_logger(), "发布者测试完成");
    }
    
    void test_frequency(double freq, int count, const std::string& name)
    {
        RCLCPP_INFO(this->get_logger(), "开始 %s - 频率: %.1f Hz, 消息数: %d", 
                   name.c_str(), freq, count);
        
        auto interval = std::chrono::duration<double>(1.0 / freq);
        
        for (int i = 0; i < count; ++i) {
            auto now = std::chrono::high_resolution_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
                now.time_since_epoch()).count();
            
            std_msgs::msg::String msg;
            msg.data = std::to_string(timestamp);
            
            publisher_->publish(msg);
            
            if (i < count - 1) {
                std::this_thread::sleep_for(interval);
            }
        }
        
        RCLCPP_INFO(this->get_logger(), "%s 发布完成", name.c_str());
        std::this_thread::sleep_for(2s); // 等待接收完成
    }

    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publisher_;
};

// 订阅者节点
class LatencySubscriber : public rclcpp::Node
{
public:
    LatencySubscriber() : Node("latency_subscriber")
    {
        subscription_ = this->create_subscription<std_msgs::msg::String>(
            "latency_test", 10,
            std::bind(&LatencySubscriber::message_callback, this, std::placeholders::_1));
        
        RCLCPP_INFO(this->get_logger(), "订阅者启动，等待消息...");
        
        // 启动统计定时器
        stats_timer_ = this->create_wall_timer(
            5s, std::bind(&LatencySubscriber::print_stats, this));
    }

private:
    void message_callback(const std_msgs::msg::String::SharedPtr msg)
    {
        auto receive_time = std::chrono::high_resolution_clock::now();
        
        try {
            auto send_timestamp = std::stoull(msg->data);
            auto send_time = std::chrono::time_point<std::chrono::high_resolution_clock>(
                std::chrono::nanoseconds(send_timestamp));
            
            auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                receive_time - send_time);
            
            latencies_.push_back(latency.count());
            total_messages_++;
            
            RCLCPP_DEBUG(this->get_logger(), "消息 %d: 延迟 %ld μs", 
                        total_messages_, latency.count());
            
        } catch (const std::exception& e) {
            RCLCPP_WARN(this->get_logger(), "解析消息失败: %s", e.what());
        }
    }
    
    void print_stats()
    {
        if (latencies_.empty()) {
            RCLCPP_INFO(this->get_logger(), "还没有接收到消息...");
            return;
        }
        
        // 计算统计信息
        auto min_lat = *std::min_element(latencies_.begin(), latencies_.end());
        auto max_lat = *std::max_element(latencies_.begin(), latencies_.end());
        auto avg_lat = std::accumulate(latencies_.begin(), latencies_.end(), 0.0) / latencies_.size();
        
        // 计算中位数
        std::vector<long> sorted_lat = latencies_;
        std::sort(sorted_lat.begin(), sorted_lat.end());
        auto median_lat = sorted_lat[sorted_lat.size() / 2];
        
        RCLCPP_INFO(this->get_logger(), 
                   "\n=== 延迟统计 (共 %zu 条消息) ===\n"
                   "  最小延迟: %ld μs\n"
                   "  最大延迟: %ld μs\n"
                   "  平均延迟: %.1f μs\n"
                   "  中位延迟: %ld μs",
                   latencies_.size(), min_lat, max_lat, avg_lat, median_lat);
        
        // 延迟评价
        if (avg_lat < 100) {
            RCLCPP_INFO(this->get_logger(), "  延迟评价: 🎉 优秀 (< 100μs)");
        } else if (avg_lat < 1000) {
            RCLCPP_INFO(this->get_logger(), "  延迟评价: ✅ 良好 (< 1ms)");
        } else if (avg_lat < 10000) {
            RCLCPP_INFO(this->get_logger(), "  延迟评价: ⚠️ 一般 (< 10ms)");
        } else {
            RCLCPP_INFO(this->get_logger(), "  延迟评价: ❌ 较差 (> 10ms)");
        }
    }

    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subscription_;
    rclcpp::TimerBase::SharedPtr stats_timer_;
    std::vector<long> latencies_;
    int total_messages_ = 0;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    
    if (argc < 2) {
        std::cout << "用法: " << argv[0] << " [pub|sub]" << std::endl;
        std::cout << "  pub: 运行发布者" << std::endl;
        std::cout << "  sub: 运行订阅者" << std::endl;
        return 1;
    }
    
    std::string mode = argv[1];
    
    if (mode == "pub") {
        RCLCPP_INFO(rclcpp::get_logger("main"), "启动发布者模式");
        auto publisher = std::make_shared<LatencyPublisher>();
        rclcpp::spin(publisher);
    } else if (mode == "sub") {
        RCLCPP_INFO(rclcpp::get_logger("main"), "启动订阅者模式");
        auto subscriber = std::make_shared<LatencySubscriber>();
        rclcpp::spin(subscriber);
    } else {
        std::cout << "错误: 未知模式 '" << mode << "'" << std::endl;
        std::cout << "请使用 'pub' 或 'sub'" << std::endl;
        return 1;
    }
    
    rclcpp::shutdown();
    return 0;
}
