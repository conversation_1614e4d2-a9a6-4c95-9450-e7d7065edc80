#!/bin/bash

# 测试初始化定时器迁移效果

echo "=== 测试初始化定时器迁移效果 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/init_timer_migration_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

echo "1. 启动网络管理系统（包含网络管理器和网络切换器）..."
# 启动网络管理器节点（包含网络切换器）
ros2 run gen3_network_manager_core network_manager_node > "$LOG_DIR/network_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（15秒）..."
echo "   - 等待一次性初始化定时器触发（1秒后）"
echo "   - 观察网络管理器和网络切换器的初始化逻辑执行情况"
sleep 15

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析测试结果..."

# 分析系统日志（包含网络管理器和网络切换器）
echo "=== 网络管理系统分析 ==="
if [ -f "$LOG_DIR/network_system.log" ]; then
    echo "系统日志文件存在，开始分析..."

    # 检查网络管理器
    echo
    echo "--- 网络管理器分析 ---"

    # 检查网络管理器一次性初始化定时器
    if grep -q "network_manager.*一次性初始化定时器触发" "$LOG_DIR/network_system.log"; then
        echo "✅ 网络管理器一次性初始化定时器已触发"

        # 检查初始化逻辑执行
        if grep -q "首次状态更新" "$LOG_DIR/network_system.log"; then
            echo "✅ 首次状态更新已在初始化定时器中执行"
        else
            echo "❌ 首次状态更新未在初始化定时器中执行"
        fi
    else
        echo "❌ 网络管理器一次性初始化定时器未触发"
    fi

    # 检查发布次数
    pub_count=$(grep -c "发布\|publish\|PUB" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    echo "网络管理器发布次数: $pub_count"

    echo
    echo "--- 网络切换器分析 ---"

    # 检查网络切换器一次性初始化定时器
    if grep -q "network_switch.*一次性初始化定时器触发" "$LOG_DIR/network_system.log"; then
        echo "✅ 网络切换器一次性初始化定时器已触发"

        # 检查质量检查逻辑执行
        if grep -q "首次质量检查" "$LOG_DIR/network_system.log"; then
            echo "✅ 首次质量检查已在初始化定时器中执行"
        else
            echo "❌ 首次质量检查未在初始化定时器中执行"
        fi

        # 检查切换监控逻辑执行
        if grep -q "首次切换监控" "$LOG_DIR/network_system.log"; then
            echo "✅ 首次切换监控已在初始化定时器中执行"
        else
            echo "❌ 首次切换监控未在初始化定时器中执行"
        fi
    else
        echo "❌ 网络切换器一次性初始化定时器未触发"
    fi

    # 检查订阅次数
    sub_count=$(grep -c "收到网络状态更新\|SUB.*收到" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")
    echo "网络切换器接收消息次数: $sub_count"

    echo
    echo "--- 立即执行逻辑检查 ---"
    # 检查是否还有在初始化阶段立即执行的逻辑（排除在初始化定时器中的执行）
    immediate_exec_lines=$(grep "立即执行" "$LOG_DIR/network_system.log" | grep -v "INIT_TIMER" | wc -l)
    if [ $immediate_exec_lines -gt 0 ]; then
        echo "❌ 仍有 $immediate_exec_lines 处立即执行的逻辑未迁移"
        grep "立即执行" "$LOG_DIR/network_system.log" | grep -v "INIT_TIMER"
    else
        echo "✅ 所有立即执行的逻辑已迁移到初始化定时器中"
    fi

else
    echo "❌ 系统日志文件不存在"
fi

echo

# 检查迁移前后的对比
echo "5. 迁移效果验证..."

# 检查初始化阶段是否干净（排除在初始化定时器中的执行）
init_clean=true
immediate_exec_count=$(grep "立即执行" "$LOG_DIR/network_system.log" 2>/dev/null | grep -v "INIT_TIMER" | wc -l)

if [ $immediate_exec_count -gt 0 ]; then
    echo "❌ 仍有 $immediate_exec_count 处立即执行的逻辑未迁移"
    init_clean=false
else
    echo "✅ 所有立即执行的逻辑已迁移"
fi

# 检查一次性定时器是否正常工作
timer_working=true
timer_count=$(grep -c "一次性初始化定时器触发" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")

if [ $timer_count -eq 0 ]; then
    echo "❌ 一次性初始化定时器未正常工作"
    timer_working=false
elif [ $timer_count -eq 2 ]; then
    echo "✅ 一次性初始化定时器正常工作（网络管理器和网络切换器都已触发）"
else
    echo "⚠️ 一次性初始化定时器部分工作（触发了 $timer_count 次，预期2次）"
fi

# 检查业务逻辑是否正常执行
business_working=true
business_count=$(grep -c "首次.*执行" "$LOG_DIR/network_system.log" 2>/dev/null || echo "0")

if [ $business_count -eq 0 ]; then
    echo "❌ 业务逻辑未在初始化定时器中执行"
    business_working=false
else
    echo "✅ 业务逻辑已在初始化定时器中执行（$business_count 项业务逻辑）"
fi

echo

# 总体评估
echo "7. 总体评估..."

score=0
max_score=3

if [ "$init_clean" = true ]; then ((score++)); fi
if [ "$timer_working" = true ]; then ((score++)); fi
if [ "$business_working" = true ]; then ((score++)); fi

echo "迁移评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 初始化定时器迁移完全成功！"
    echo "  ✅ 所有立即执行逻辑已迁移"
    echo "  ✅ 一次性初始化定时器正常工作"
    echo "  ✅ 业务逻辑在合适时机执行"
elif [ $score -ge 2 ]; then
    echo "✅ 初始化定时器迁移基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 初始化定时器迁移需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示时序对比
echo "8. 时序对比..."

echo "迁移前时序:"
echo "  0s: init() -> 立即执行业务逻辑 ❌"
echo "  Xs: spin() -> 定时器开始工作"

echo
echo "迁移后时序:"
echo "  0s: init() -> 创建定时器（不立即执行）✅"
echo "  1s: init_timer -> 执行业务逻辑 ✅"
echo "  Xs: spin() -> 定时器正常工作"

echo

# 显示优势
echo "9. 迁移优势..."
echo "✅ 避免在初始化阶段执行可能无效的逻辑"
echo "✅ 给系统足够时间建立连接"
echo "✅ 确保业务逻辑在合适的时机执行"
echo "✅ 提高系统启动的可靠性"
echo "✅ 减少消息丢失的风险"

echo
echo "详细日志文件:"
echo "  网络管理系统: $LOG_DIR/network_system.log"

echo
echo "🎊 初始化定时器迁移测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
