#!/bin/bash

# 测试中国网络环境DNS优化

echo "=== 测试中国网络环境DNS优化 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/china_dns_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 创建中国网络环境DNS测试参数文件
cat > "$LOG_DIR/china_dns_params.yaml" << EOF
network_manager_node:
  ros__parameters:
    # DNS管理配置
    dns_check_interval: 15.0
    dns_test_timeout_ms: 5000
    dns_max_consecutive_failures: 2
    dns_priority_update_threshold: 0.8
    
    # 标准DNS服务器配置
    dns_servers:
      primary: ["8.8.8.8", "1.1.1.1"]
      secondary: ["114.114.114.114", "223.5.5.5"]
      backup: ["208.67.222.222", "9.9.9.9"]
    
    # 中国网络环境DNS服务器配置
    dns_servers:
      china_primary: ["114.114.114.114", "223.5.5.5"]      # 114DNS, 阿里DNS
      china_secondary: ["119.29.29.29", "180.76.76.76"]    # 腾讯DNS, 百度DNS
      china_backup: ["1.2.4.8", "210.2.4.8"]              # 中国电信DNS
      international: ["8.8.8.8", "1.1.1.1", "208.67.222.222"]  # Google, Cloudflare, OpenDNS
    
    # 中国网络检测配置
    china_network:
      enable_detection: true
      detection_interval: 300.0
      prefer_domestic_dns: true
      domestic_test_sites: ["www.baidu.com", "www.qq.com", "www.taobao.com", "www.163.com"]
      international_test_sites: ["www.google.com", "www.facebook.com", "www.twitter.com"]
    
    # 其他网络管理配置
    network_check_interval: 10.0
    connectivity_check_interval: 20.0
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "usb0"
EOF

echo "1. 启动中国网络环境DNS优化系统..."
# 启动网络管理器节点，使用中国DNS优化配置
ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$LOG_DIR/china_dns_params.yaml" > "$LOG_DIR/china_dns_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（60秒）..."
echo "   - 观察中国网络环境检测"
echo "   - 监控DNS服务器配置优化"
echo "   - 验证国内外网站连通性测试"
echo "   - 测试智能DNS切换逻辑"
sleep 60

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析中国网络环境DNS优化测试结果..."

# 分析系统日志
echo "=== 中国网络环境DNS优化分析 ==="
if [ -f "$LOG_DIR/china_dns_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    echo
    echo "--- 网络环境检测分析 ---"
    
    # 检查网络环境检测
    env_detection_count=$(grep -c "开始检测网络环境\|网络环境检测完成" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    china_detection=$(grep "在中国境内:" "$LOG_DIR/china_dns_system.log" | tail -1 | grep -o "是\|否" || echo "未知")
    intl_access=$(grep "可访问国际网站:" "$LOG_DIR/china_dns_system.log" | tail -1 | grep -o "是\|否" || echo "未知")
    prefer_domestic=$(grep "优先国内DNS:" "$LOG_DIR/china_dns_system.log" | tail -1 | grep -o "是\|否" || echo "未知")
    detected_region=$(grep "检测地区:" "$LOG_DIR/china_dns_system.log" | tail -1 | awk -F': ' '{print $2}' || echo "未知")
    
    echo "网络环境检测次数: $env_detection_count"
    echo "检测结果 - 在中国境内: $china_detection"
    echo "检测结果 - 可访问国际网站: $intl_access"
    echo "检测结果 - 优先国内DNS: $prefer_domestic"
    echo "检测结果 - 检测地区: $detected_region"
    
    if [ $env_detection_count -gt 0 ]; then
        echo "✅ 网络环境检测正常执行"
    else
        echo "❌ 网络环境检测未执行"
    fi
    
    echo
    echo "--- 中国DNS配置分析 ---"
    
    # 检查中国DNS配置加载
    china_dns_load=$(grep -c "加载中国优化DNS服务器配置" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    dns_strategy=$(grep "使用.*DNS配置策略" "$LOG_DIR/china_dns_system.log" | tail -1 | awk -F'使用' '{print $2}' | awk -F'DNS配置策略' '{print $1}' || echo "未知")
    china_dns_count=$(grep "已加载.*个中国优化DNS服务器配置" "$LOG_DIR/china_dns_system.log" | tail -1 | grep -o '[0-9]\+' | head -1 || echo "0")
    
    echo "中国DNS配置加载次数: $china_dns_load"
    echo "DNS配置策略: $dns_strategy"
    echo "中国优化DNS服务器数量: $china_dns_count 个"
    
    if [ $china_dns_load -gt 0 ]; then
        echo "✅ 中国DNS配置加载成功"
    else
        echo "❌ 中国DNS配置加载失败"
    fi
    
    echo
    echo "--- 网站连通性测试分析 ---"
    
    # 检查网站连通性测试
    domestic_test_count=$(grep -c "测试国内网络连通性\|成功访问国内网站" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    intl_test_count=$(grep -c "检测国际网站访问能力\|成功访问国际网站" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    
    echo "国内网站连通性测试次数: $domestic_test_count"
    echo "国际网站连通性测试次数: $intl_test_count"
    
    if [ $domestic_test_count -gt 0 ]; then
        echo "✅ 国内网站连通性测试正常"
    else
        echo "❌ 国内网站连通性测试未执行"
    fi
    
    if [ $intl_test_count -gt 0 ]; then
        echo "✅ 国际网站连通性测试正常"
    else
        echo "❌ 国际网站连通性测试未执行"
    fi
    
    echo
    echo "--- DNS服务器管理分析 ---"
    
    # 检查DNS服务器配置显示
    dns_server_config_count=$(grep -c "DNS服务器:.*地区:.*国内:" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    domestic_dns_count=$(grep -c "国内: 是" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    international_dns_count=$(grep -c "国内: 否" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    
    echo "DNS服务器配置显示次数: $dns_server_config_count"
    echo "国内DNS服务器数量: $domestic_dns_count 个"
    echo "国际DNS服务器数量: $international_dns_count 个"
    
    if [ $dns_server_config_count -gt 0 ]; then
        echo "✅ DNS服务器配置显示正常"
    else
        echo "❌ DNS服务器配置显示异常"
    fi
    
    echo
    echo "--- 智能DNS检查分析 ---"
    
    # 检查智能DNS检查
    smart_dns_check_count=$(grep -c "开始智能DNS检查" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    china_optimized_count=$(grep -c "中国优化.*DNS服务器" "$LOG_DIR/china_dns_system.log" 2>/dev/null || echo "0")
    
    echo "智能DNS检查次数: $smart_dns_check_count"
    echo "中国优化DNS使用次数: $china_optimized_count"
    
    if [ $smart_dns_check_count -gt 0 ]; then
        echo "✅ 智能DNS检查正常执行"
    else
        echo "❌ 智能DNS检查未执行"
    fi
    
    echo
    echo "--- 关键日志示例 ---"
    
    echo "网络环境检测日志:"
    grep "网络环境检测完成\|在中国境内\|可访问国际网站" "$LOG_DIR/china_dns_system.log" | head -5
    
    echo
    echo "中国DNS配置日志:"
    grep "中国优化DNS\|DNS配置策略" "$LOG_DIR/china_dns_system.log" | head -3
    
    echo
    echo "DNS服务器配置日志:"
    grep "DNS服务器:.*地区:" "$LOG_DIR/china_dns_system.log" | head -5
    
    echo
    echo "网站连通性测试日志:"
    grep "成功访问.*网站\|网络连通性测试" "$LOG_DIR/china_dns_system.log" | head -5
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 总体评估
echo "5. 中国网络环境DNS优化总体评估..."

score=0
max_score=7

# 评分标准
if [ $env_detection_count -gt 0 ]; then ((score++)); fi
if [ $china_dns_load -gt 0 ]; then ((score++)); fi
if [ $domestic_test_count -gt 0 ]; then ((score++)); fi
if [ $intl_test_count -gt 0 ]; then ((score++)); fi
if [ $dns_server_config_count -gt 0 ]; then ((score++)); fi
if [ $smart_dns_check_count -gt 0 ]; then ((score++)); fi
if [ "$china_dns_count" -gt 0 ]; then ((score++)); fi

echo "中国网络环境DNS优化评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 中国网络环境DNS优化完全成功！"
    echo "  ✅ 网络环境检测正常工作"
    echo "  ✅ 中国DNS配置加载成功"
    echo "  ✅ 国内网站连通性测试正常"
    echo "  ✅ 国际网站连通性测试正常"
    echo "  ✅ DNS服务器配置管理正常"
    echo "  ✅ 智能DNS检查正常执行"
    echo "  ✅ 中国优化DNS服务器配置完整"
elif [ $score -ge 5 ]; then
    echo "✅ 中国网络环境DNS优化基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 中国网络环境DNS优化需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示中国网络环境优化优势
echo "6. 中国网络环境DNS优化优势..."
echo "✅ 智能网络环境检测:"
echo "   • 自动检测是否在中国大陆"
echo "   • 测试国际网站访问能力"
echo "   • 根据网络环境选择最优DNS策略"

echo
echo "✅ 中国优化DNS配置:"
echo "   • 国内DNS服务器: 114DNS, 阿里DNS, 腾讯DNS, 百度DNS"
echo "   • 国际DNS服务器: Google DNS, Cloudflare DNS, OpenDNS"
echo "   • 根据网络环境动态调整DNS优先级"

echo
echo "✅ 网站连通性测试:"
echo "   • 测试国内主流网站连通性"
echo "   • 测试国际网站访问能力"
echo "   • 基于连通性结果优化DNS选择"

echo
echo "✅ 智能DNS切换策略:"
echo "   • 在中国环境下优先使用国内DNS"
echo "   • 可访问国际网站时添加国际DNS备份"
echo "   • 根据成功率和响应时间动态调整"

echo
echo "✅ 地区化参数配置:"
echo "   • 支持国内外不同的DNS服务器列表"
echo "   • 可配置的网站连通性测试列表"
echo "   • 灵活的DNS优先级策略"

echo
echo "详细日志文件:"
echo "  中国DNS系统: $LOG_DIR/china_dns_system.log"
echo "  测试参数文件: $LOG_DIR/china_dns_params.yaml"

echo
echo "🇨🇳 中国网络环境DNS优化测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
