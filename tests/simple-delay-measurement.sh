#!/bin/bash

# 简单的 ROS2 延迟测量脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== ROS2 简单延迟测量 ===${NC}"
echo

# 设置 ROS2 环境
source /opt/ros/humble/setup.bash

# 创建日志目录
LOG_DIR="/tmp/simple_delay_$(date +%s)"
mkdir -p "$LOG_DIR"

echo -e "${BLUE}测试1: 连接建立延迟${NC}"

# 测试话题
TOPIC="/delay_test"

# 记录开始时间
echo -e "  ${YELLOW}记录开始时间...${NC}"
START_TIME=$(date +%s.%N)
echo "开始时间: $START_TIME"

# 启动发布者
echo -e "  ${YELLOW}启动发布者...${NC}"
ros2 topic pub --rate 2 $TOPIC std_msgs/msg/String "data: 'test'" > "$LOG_DIR/pub.log" 2>&1 &
PUB_PID=$!
PUB_START=$(date +%s.%N)
echo "发布者启动时间: $PUB_START"

# 等待1秒
sleep 1

# 启动订阅者
echo -e "  ${YELLOW}启动订阅者...${NC}"
SUB_START=$(date +%s.%N)
echo "订阅者启动时间: $SUB_START"

# 运行订阅者5秒
timeout 5s ros2 topic echo $TOPIC > "$LOG_DIR/sub.log" 2>&1 &
SUB_PID=$!

# 等待测试完成
sleep 6

# 停止发布者
kill $PUB_PID 2>/dev/null
wait $PUB_PID 2>/dev/null

# 记录结束时间
END_TIME=$(date +%s.%N)
echo "结束时间: $END_TIME"

echo
echo -e "${BLUE}延迟计算:${NC}"

if command -v bc >/dev/null 2>&1; then
    # 计算各种延迟
    PUB_TO_SUB_DELAY=$(echo "$SUB_START - $PUB_START" | bc -l)
    TOTAL_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
    
    echo -e "  ${YELLOW}时间间隔:${NC}"
    echo -e "    发布者启动到订阅者启动: ${GREEN}${PUB_TO_SUB_DELAY}${NC} 秒"
    echo -e "    总测试时间: ${GREEN}${TOTAL_TIME}${NC} 秒"
    
    # 分析消息传递
    if [ -f "$LOG_DIR/sub.log" ]; then
        msg_count=$(grep -c "^data:" "$LOG_DIR/sub.log" 2>/dev/null || echo "0")
        echo -e "  ${YELLOW}消息统计:${NC}"
        echo -e "    接收到的消息数: ${GREEN}$msg_count${NC}"
        
        if [ $msg_count -gt 0 ]; then
            echo -e "    ${GREEN}✓${NC} 消息传递成功"
            
            # 估算首次消息接收延迟
            # 订阅者运行了5秒，发布者2Hz，预期应该收到约10条消息
            expected_msgs=10
            if [ $msg_count -ge 8 ]; then
                echo -e "    ${GREEN}✓${NC} 连接建立快速（接收率高）"
            elif [ $msg_count -ge 5 ]; then
                echo -e "    ${YELLOW}○${NC} 连接建立正常"
            else
                echo -e "    ${RED}✗${NC} 连接建立较慢（接收率低）"
            fi
            
            # 计算接收率
            if command -v bc >/dev/null 2>&1; then
                receive_rate=$(echo "scale=1; $msg_count * 100 / $expected_msgs" | bc -l)
                echo -e "    消息接收率: ${GREEN}${receive_rate}%${NC}"
            fi
        else
            echo -e "    ${RED}✗${NC} 没有接收到消息"
        fi
    fi
else
    echo -e "  ${RED}需要安装 bc 来进行精确计算${NC}"
fi

echo
echo -e "${BLUE}测试2: 快速连接测试${NC}"

# 快速连接测试
FAST_TOPIC="/fast_test"

echo -e "  ${YELLOW}同时启动发布者和订阅者...${NC}"

FAST_START=$(date +%s.%N)

# 同时启动
ros2 topic pub --rate 5 $FAST_TOPIC std_msgs/msg/String "data: 'fast'" > "$LOG_DIR/fast_pub.log" 2>&1 &
FAST_PUB_PID=$!

ros2 topic echo $FAST_TOPIC > "$LOG_DIR/fast_sub.log" 2>&1 &
FAST_SUB_PID=$!

# 运行3秒
sleep 3

# 停止
kill $FAST_PUB_PID $FAST_SUB_PID 2>/dev/null
wait $FAST_PUB_PID $FAST_SUB_PID 2>/dev/null

FAST_END=$(date +%s.%N)

if command -v bc >/dev/null 2>&1; then
    FAST_TOTAL=$(echo "$FAST_END - $FAST_START" | bc -l)
    echo -e "  ${GREEN}✓${NC} 快速测试完成，总时间: ${YELLOW}${FAST_TOTAL}${NC} 秒"
fi

# 分析快速测试
if [ -f "$LOG_DIR/fast_sub.log" ]; then
    fast_msgs=$(grep -c "^data:" "$LOG_DIR/fast_sub.log" 2>/dev/null || echo "0")
    echo -e "  快速测试接收消息数: ${GREEN}$fast_msgs${NC}"
    
    # 5Hz × 3秒 = 预期15条消息
    if [ $fast_msgs -ge 12 ]; then
        echo -e "  ${GREEN}✓${NC} 连接建立非常快"
    elif [ $fast_msgs -ge 8 ]; then
        echo -e "  ${YELLOW}○${NC} 连接建立较快"
    elif [ $fast_msgs -ge 3 ]; then
        echo -e "  ${YELLOW}○${NC} 连接建立正常"
    else
        echo -e "  ${RED}✗${NC} 连接建立慢"
    fi
fi

echo
echo -e "${BLUE}延迟总结:${NC}"

# 总结延迟性能
echo -e "  ${YELLOW}关键延迟指标:${NC}"

if command -v bc >/dev/null 2>&1; then
    echo -e "    • 发布者到订阅者启动间隔: ${GREEN}${PUB_TO_SUB_DELAY}${NC} 秒"
    
    # 根据延迟给出评价
    delay_ms=$(echo "$PUB_TO_SUB_DELAY * 1000" | bc -l)
    delay_ms_int=$(echo "$delay_ms" | cut -d. -f1)
    
    if [ "$delay_ms_int" -lt 100 ]; then
        echo -e "    • 延迟评价: ${GREEN}优秀${NC} (< 100ms)"
    elif [ "$delay_ms_int" -lt 500 ]; then
        echo -e "    • 延迟评价: ${GREEN}良好${NC} (< 500ms)"
    elif [ "$delay_ms_int" -lt 1000 ]; then
        echo -e "    • 延迟评价: ${YELLOW}一般${NC} (< 1s)"
    else
        echo -e "    • 延迟评价: ${RED}较慢${NC} (> 1s)"
    fi
fi

# 消息传递性能
basic_msgs=$(grep -c "^data:" "$LOG_DIR/sub.log" 2>/dev/null || echo "0")
fast_msgs=$(grep -c "^data:" "$LOG_DIR/fast_sub.log" 2>/dev/null || echo "0")

echo -e "    • 基本测试消息接收: ${GREEN}$basic_msgs${NC}/10 (预期)"
echo -e "    • 快速测试消息接收: ${GREEN}$fast_msgs${NC}/15 (预期)"

# 整体评价
total_expected=25
total_received=$((basic_msgs + fast_msgs))

if command -v bc >/dev/null 2>&1; then
    overall_rate=$(echo "scale=1; $total_received * 100 / $total_expected" | bc -l)
    echo -e "    • 整体消息接收率: ${GREEN}${overall_rate}%${NC}"
    
    if [ $(echo "$overall_rate >= 80" | bc -l) -eq 1 ]; then
        echo -e "  ${GREEN}🎉 延迟性能优秀！${NC}"
    elif [ $(echo "$overall_rate >= 60" | bc -l) -eq 1 ]; then
        echo -e "  ${GREEN}✅ 延迟性能良好${NC}"
    elif [ $(echo "$overall_rate >= 40" | bc -l) -eq 1 ]; then
        echo -e "  ${YELLOW}⚠️ 延迟性能一般${NC}"
    else
        echo -e "  ${RED}❌ 延迟性能需要改进${NC}"
    fi
fi

echo
echo -e "${BLUE}详细日志:${NC}"
echo -e "  日志目录: ${YELLOW}$LOG_DIR${NC}"
echo -e "  基本测试订阅者: ${YELLOW}cat $LOG_DIR/sub.log${NC}"
echo -e "  快速测试订阅者: ${YELLOW}cat $LOG_DIR/fast_sub.log${NC}"

echo
echo -e "${GREEN}🎊 延迟测量完成！${NC}"

# 清理
pkill -f "ros2 topic" 2>/dev/null
