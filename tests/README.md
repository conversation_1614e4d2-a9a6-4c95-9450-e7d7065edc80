# 测试套件

本目录包含 ROS2 网络管理器项目的所有测试脚本和验证程序。

## 📋 测试文件列表

### 🔧 测试脚本

| 文件名 | 功能 | 描述 |
|--------|------|------|
| `run-all-tests.sh` | 测试套件主入口 | 运行所有测试并生成汇总报告 |
| `config-validation-test.sh` | 配置验证测试 | 验证 YAML 配置文件的存在性和语法 |
| `debug-config-test.sh` | 调试配置测试 | 验证 VSCode 调试配置和环境设置 |
| `network-interface-test.cpp` | 网络接口测试 | 测试网络接口类型识别逻辑 |
| `nmcli-chinese-support-test.sh` | nmcli 中文支持测试 | 验证 nmcli 命令在不同语言环境下的兼容性 |
| `signal-strength-conversion-test.sh` | 信号强度转换测试 | 验证 WiFi 信号百分比到 dBm 的转换功能 |

### 📖 文档
| 文件名 | 功能 | 描述 |
|--------|------|------|
| `README.md` | 测试文档 | 本文件，测试套件使用说明 |

## 🚀 快速开始

### 运行所有测试
```bash
# 在项目根目录运行
cd tests
./run-all-tests.sh
```

### 运行单个测试
```bash
# 配置验证测试
./tests/config-validation-test.sh

# 调试配置测试  
./tests/debug-config-test.sh

# 网络接口测试
cd tests
g++ -o network-interface-test network-interface-test.cpp
./network-interface-test

# nmcli 中文支持测试
./tests/nmcli-chinese-support-test.sh

# 信号强度转换测试
./tests/signal-strength-conversion-test.sh
```

## 🧪 测试项目详解

### 1. 配置验证测试 (config-validation-test.sh)
**目标**: 验证项目配置文件的完整性和正确性
- ✅ 检查配置文件是否存在
- ✅ 验证 YAML 语法正确性
- ✅ 显示关键配置参数
- ✅ 验证构建后的配置文件安装

**测试的配置文件**:
- `src/gen3_network_manager_core/config/network_config.yaml`
- `src/gen3_network_manager_core/config/development_config.yaml`
- `src/gen3_network_manager_core/config/production_config.yaml`

### 2. 调试配置测试 (debug-config-test.sh)
**目标**: 验证开发环境和调试配置
- ✅ 检查当前系统架构
- ✅ 验证 ROS Humble 环境
- ✅ 检查构建输出目录
- ✅ 验证可执行文件存在
- ✅ 检查 VSCode launch.json 配置
- ✅ 验证 ROS 共享库可用性
- ✅ 测试环境变量设置

### 3. 网络接口测试 (network-interface-test.cpp)
**目标**: 测试网络接口类型识别算法
- ✅ 测试精确匹配配置的接口名称
- ✅ 测试模式匹配（基于接口名称前缀）
- ✅ 测试后备匹配（传统硬编码规则）
- ✅ 验证不同接口类型的正确识别

**测试场景**:
- WiFi 接口: `wlan0`, `wlan1`
- 以太网接口: `eth0`, `eth1`
- 5G 接口: `usb0`, `usb1`
- 未知接口: `unknown_interface`

### 4. nmcli 中文支持测试 (nmcli-chinese-support-test.sh)
**目标**: 验证 nmcli 命令在不同语言环境下的兼容性
- ✅ 测试中文环境下的 nmcli 输出格式
- ✅ 测试英文环境下的 nmcli 输出格式
- ✅ 验证修复后的命令在不同环境下都能正常工作
- ✅ 检查 WiFi 信息获取和扫描功能

**测试内容**:
- 语言环境差异验证（是/否 vs yes/no）
- LANG=C 强制英文输出验证
- WiFi 连接状态检测
- WiFi 网络扫描功能

### 5. 信号强度转换测试 (signal-strength-conversion-test.sh)
**目标**: 验证 WiFi 信号百分比到 dBm 的转换功能
- ✅ 验证转换公式的正确性
- ✅ 测试边界值处理（0%, 100%）
- ✅ 显示实际 WiFi 网络的转换结果
- ✅ 提供信号质量评估和建议

**测试内容**:
- 转换公式验证（dBm = -100 + percentage × 0.7）
- 关键百分比值的转换测试
- 实际 WiFi 网络信号强度转换
- 信号质量评级和可视化显示

### 4. 文档验证测试
**目标**: 验证项目文档的完整性
- ✅ 检查主要文档文件存在
- ✅ 验证分类文档目录结构
- ✅ 检查文档链接有效性
- ✅ 验证配置文件完整性

### 5. 项目构建测试
**目标**: 验证项目能够正常构建
- ✅ 执行完整构建流程
- ✅ 检查构建输出
- ✅ 验证安装目录结构

### 6. 架构配置测试
**目标**: 验证多架构支持功能
- ✅ 测试架构检测脚本
- ✅ 验证配置更新脚本
- ✅ 检查架构自适应功能

## 📊 测试结果解读

### 成功输出示例
```
=== ROS2 网络管理器测试套件 ===
[INFO] 开始运行所有测试...

=== 配置验证测试 ===
[PASS] 配置验证测试 通过

=== 调试配置测试 ===
[PASS] 调试配置测试 通过

=== 测试结果汇总 ===
总测试数: 6
通过测试: 6
失败测试: 0
🎉 所有测试通过！
```

### 失败处理
如果测试失败，脚本会显示具体的错误信息和失败的测试项目。根据错误信息进行相应的修复：

- **配置文件错误**: 检查 YAML 语法和文件路径
- **环境问题**: 确保 ROS2 Humble 正确安装
- **构建失败**: 检查依赖项和编译环境
- **文档问题**: 验证文档文件和链接

## 🔧 测试环境要求

### 必需工具
- **Bash**: 运行测试脚本
- **Python3**: YAML 语法验证
- **G++**: 编译 C++ 测试程序
- **ROS2 Humble**: ROS 环境测试

### 可选工具
- **VSCode**: 调试配置验证
- **Git**: 版本控制验证

## 📝 添加新测试

### 1. 创建测试脚本
```bash
# 创建新的测试脚本
touch tests/my-new-test.sh
chmod +x tests/my-new-test.sh
```

### 2. 更新测试套件
在 `run-all-tests.sh` 中添加新的测试项目：
```bash
# 添加到主测试流程中
run_test "我的新测试" \
         "cd .. && ./tests/my-new-test.sh" \
         "测试描述"
```

### 3. 更新文档
在本 README 中添加新测试的说明。

## 🔗 相关文档

- [调试配置文档](../docs/DEBUG_CONFIGURATION.md) - 完整调试环境设置
- [架构配置文档](../docs/ARCHITECTURE_CONFIG.md) - 多架构支持说明
- [故障排除文档](../docs/troubleshooting/) - 问题解决指南
- [开发文档](../docs/development/) - 开发环境和工具

## 🎯 最佳实践

1. **定期运行**: 在代码变更后运行完整测试套件
2. **单项测试**: 开发特定功能时运行相关的单项测试
3. **环境验证**: 在新环境中首先运行测试验证配置
4. **持续集成**: 将测试套件集成到 CI/CD 流程中

---

**返回**: [项目主页](../README.md) | [文档索引](../docs/README.md)
