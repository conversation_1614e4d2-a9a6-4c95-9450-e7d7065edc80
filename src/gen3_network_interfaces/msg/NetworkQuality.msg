std_msgs/Header header

# 网络标识
uint8 network_type             # 网络类型 (WiFi/5G)
string interface_name          # 接口名称

# 信号质量
int32 signal_strength          # 信号强度 (dBm)
float64 signal_quality         # 信号质量百分比 (0-100)

# 连通性测试
bool dns_working               # DNS解析正常

# 性能指标
float64 latency_ms             # 延迟测试结果
float64 jitter_ms              # 抖动
float64 download_speed_mbps    # 下载速度
float64 upload_speed_mbps      # 上传速度
float64 packet_loss_rate       # 丢包率

# DNS性能
float64 dns_resolution_time_ms # DNS解析时间
string[] working_dns_servers   # 可用DNS服务器
string[] failed_dns_servers    # 失效DNS服务器

# 综合评分
float64 overall_score          # 综合质量评分 (0-100)
bool is_suitable_for_switching # 是否适合作为切换目标

# 5G特定信息
string operator_name           # 运营商名称 (仅5G网络)
string network_mode            # 网络制式 (LTE/SA/NR，仅5G网络)

# 检测时间
builtin_interfaces/Time test_start_time
builtin_interfaces/Time test_end_time