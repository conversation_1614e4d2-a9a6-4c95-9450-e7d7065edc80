# 请求
string ssid                    # WiFi网络名称
string password                # 网络密码
string security_type           # 加密类型 (可选，自动检测)
bool save_to_list              # 是否保存到已知网络列表
uint32 priority                # 优先级 (可选)
bool is_bound_network          # 是否标记为绑定网络
uint32 timeout_seconds         # 连接超时时间 (默认30秒)

---

# 响应
bool success                   # 连接是否成功
string message                 # 结果消息
string error_code              # 错误代码 (失败时)

# 连接结果信息
string ip_address              # 获得的IP地址
string gateway                 # 网关地址
string[] dns_servers           # DNS服务器
int32 signal_strength          # 信号强度
float64 connection_time_seconds # 连接耗时 