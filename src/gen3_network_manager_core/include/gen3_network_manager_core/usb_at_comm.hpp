// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__USB_AT_COMM_HPP_
#define GEN3_NETWORK_MANAGER_CORE__USB_AT_COMM_HPP_

#include <string>
#include <vector>
#include <cstdint>
#include <memory>

#include "rclcpp/rclcpp.hpp"

namespace gen3_network_manager_core
{

// USB 设备 ID 结构体
struct UsbDeviceId {
  uint16_t match_flags;
  uint16_t id_vendor;
  uint16_t id_product;
  uint8_t b_interface_number;
  uint8_t b_interface_class;
  uint8_t b_interface_sub_class;
  uint8_t b_interface_protocol;
};

// USB AT 命令通信类
class UsbAtComm
{
public:
  explicit UsbAtComm(const rclcpp::Node::SharedPtr & node);
  ~UsbAtComm();

  // 初始化和清理
  bool init();
  void cleanup();

  // 设备发现和连接
  bool find_device();
  bool open_device();
  bool close_device();

  // AT 命令发送和接收
  bool send_at_command(const std::string & command, std::string & response, int timeout_ms = 1000);
  bool send_at_command_simple(const std::string & command, int timeout_ms = 1000);

  // 状态查询
  bool is_connected() const { return device_fd_ >= 0; }
  std::string get_device_path() const { return device_path_; }

private:
  // 内部方法
  bool match_device_id(const UsbDeviceId & device_id);
  bool get_device_descriptor(const std::string & dev_path);
  bool find_device_recursive(const std::string & dir_path);
  void control_setup();
  
  // USB 通信方法
  int bulk_out(const uint8_t * data, int len);
  int bulk_in(uint8_t * data, int len, int timeout_ms);

  // 成员变量
  rclcpp::Node::SharedPtr node_;
  rclcpp::Logger logger_;

  // USB 设备信息
  std::string device_path_;
  int device_fd_;
  int ep_in_;
  int ep_out_;
  int ep_len_;
  int interface_num_;

  // 支持的设备 ID 列表
  static const std::vector<UsbDeviceId> supported_devices_;
  
  // USB 设备目录
  static constexpr const char* USB_DEV_DIR = "/dev/bus/usb";
  
  // 匹配标志
  static constexpr uint16_t USB_DEVICE_ID_MATCH_VENDOR = 0x0001;
  static constexpr uint16_t USB_DEVICE_ID_MATCH_PRODUCT = 0x0002;
  static constexpr uint16_t USB_DEVICE_ID_MATCH_INT_NUMBER = 0x0004;
  static constexpr uint16_t USB_DEVICE_ID_MATCH_INT_CLASS = 0x0008;
  static constexpr uint16_t USB_DEVICE_ID_MATCH_INT_SUBCLASS = 0x0010;
  static constexpr uint16_t USB_DEVICE_ID_MATCH_INT_PROTOCOL = 0x0020;
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__USB_AT_COMM_HPP_
