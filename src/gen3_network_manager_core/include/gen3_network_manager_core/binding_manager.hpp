// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__BINDING_MANAGER_HPP_
#define GEN3_NETWORK_MANAGER_CORE__BINDING_MANAGER_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>
#include <thread>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/service.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/binding_status.hpp"
#include "gen3_network_interfaces/srv/start_binding.hpp"
#include "gen3_network_interfaces/action/network_binding.hpp"

namespace gen3_network_manager_core
{

class BindingManager
{
public:
  explicit BindingManager(const rclcpp::Node::SharedPtr & node);
  ~BindingManager();

  // 初始化函数
  bool init();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<gen3_network_interfaces::msg::BindingStatus>::SharedPtr binding_status_pub_;

  // 服务
  rclcpp::Service<gen3_network_interfaces::srv::StartBinding>::SharedPtr start_binding_srv_;

  // 动作服务器
  using NetworkBindingAction = gen3_network_interfaces::action::NetworkBinding;
  using GoalHandleNetworkBinding = rclcpp_action::ServerGoalHandle<NetworkBindingAction>;
  
  rclcpp_action::Server<NetworkBindingAction>::SharedPtr binding_action_server_;
  
  // 定时器
  rclcpp::TimerBase::SharedPtr binding_status_timer_;
  
  // 内部状态
  std::mutex binding_mutex_;
  gen3_network_interfaces::msg::BindingStatus current_binding_status_;
  std::atomic<bool> binding_in_progress_;
  std::string current_session_id_;
  std::thread binding_thread_;
  
  // 回调函数
  void binding_status_timer_callback();
  void handle_start_binding(
    const std::shared_ptr<gen3_network_interfaces::srv::StartBinding::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::StartBinding::Response> response);
  
  // 动作服务器回调
  rclcpp_action::GoalResponse handle_goal(
    const rclcpp_action::GoalUUID & uuid,
    std::shared_ptr<const NetworkBindingAction::Goal> goal);
  
  rclcpp_action::CancelResponse handle_cancel(
    const std::shared_ptr<GoalHandleNetworkBinding> goal_handle);
  
  void handle_accepted(
    const std::shared_ptr<GoalHandleNetworkBinding> goal_handle);
  
  // 绑定流程执行
  void execute_binding(const std::shared_ptr<GoalHandleNetworkBinding> goal_handle);
  
  // 辅助函数
  bool start_binding_process(uint8_t method, uint32_t timeout, const std::string & device_name);
  void update_binding_status(uint8_t status, uint8_t progress, const std::string & message);
  void publish_binding_status();
  std::string generate_session_id();
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__BINDING_MANAGER_HPP_ 