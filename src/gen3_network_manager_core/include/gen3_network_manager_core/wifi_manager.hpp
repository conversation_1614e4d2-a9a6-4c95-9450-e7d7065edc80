// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__WIFI_MANAGER_HPP_
#define GEN3_NETWORK_MANAGER_CORE__WIFI_MANAGER_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/service.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/wi_fi_network.hpp"
#include "gen3_network_interfaces/srv/connect_wi_fi.hpp"
#include "gen3_network_interfaces/srv/get_network_list.hpp"
#include "gen3_network_interfaces/srv/set_network_priority.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

namespace gen3_network_manager_core
{

class WiFiManager
{
public:
  // 已保存的网络结构体
  struct SavedNetwork {
    std::string ssid;
    std::string password;
    std::string security_type;
    uint32_t priority;
    bool auto_connect;
  };

  explicit WiFiManager(const rclcpp::Node::SharedPtr & node);
  ~WiFiManager();

  // 初始化函数
  bool init();
  
  // 公共接口
  bool connect_to_wifi(const std::string & ssid, const std::string & password,
                      const std::string & security_type = "", bool save_to_list = true);
  bool get_current_wifi_info(std::string & ssid, int32_t & signal_strength, std::string & security_type);
  bool scan_networks(std::vector<gen3_network_interfaces::msg::WiFiNetwork> & networks);
  void load_wifi_configuration();
  
  // 获取当前连接的WiFi SSID
  std::string get_current_ssid();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr wifi_status_pub_;
  rclcpp::Publisher<gen3_network_interfaces::msg::WiFiNetwork>::SharedPtr wifi_scan_results_pub_;

  // 服务
  rclcpp::Service<gen3_network_interfaces::srv::ConnectWiFi>::SharedPtr connect_wifi_srv_;
  rclcpp::Service<gen3_network_interfaces::srv::GetNetworkList>::SharedPtr get_network_list_srv_;
  rclcpp::Service<gen3_network_interfaces::srv::SetNetworkPriority>::SharedPtr set_network_priority_srv_;

  // 定时器
  rclcpp::TimerBase::SharedPtr scan_timer_;
  
  // 内部状态
  std::mutex wifi_mutex_;
  std::vector<gen3_network_interfaces::msg::WiFiNetwork> available_networks_;
  std::vector<SavedNetwork> saved_networks_;
  gen3_network_interfaces::msg::WiFiNetwork current_network_;
  std::atomic<bool> is_scanning_;
  std::string wifi_interface_;
  
  // 回调函数
  void scan_timer_callback();
  void handle_connect_wifi(
    const std::shared_ptr<gen3_network_interfaces::srv::ConnectWiFi::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::ConnectWiFi::Response> response);
  void handle_get_network_list(
    const std::shared_ptr<gen3_network_interfaces::srv::GetNetworkList::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::GetNetworkList::Response> response);
  void handle_set_network_priority(
    const std::shared_ptr<gen3_network_interfaces::srv::SetNetworkPriority::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::SetNetworkPriority::Response> response);
  
  // 辅助函数
  bool load_saved_networks();
  bool save_network_config(const gen3_network_interfaces::msg::WiFiNetwork & network);
  bool update_network_priority(const std::string & ssid, uint32_t priority);
  void publish_wifi_status(const std::string & status);
  
  // 新增函数声明
  bool scan_wifi_networks(std::vector<gen3_network_manager_core::utils::NetworkUtils::WiFiInfo> & networks);
  void start_scanning();
  void stop_scanning();
  bool save_networks();
  void add_or_update_saved_network(const SavedNetwork & network);
  bool connect_to_wifi(const std::string & ssid, const std::string & password,
                      uint8_t security_type, bool save_network);

  // 异步扫描相关函数
  void perform_background_scan();
  void update_cached_networks(const std::vector<gen3_network_manager_core::utils::NetworkUtils::WiFiInfo> & scan_results);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__WIFI_MANAGER_HPP_ 