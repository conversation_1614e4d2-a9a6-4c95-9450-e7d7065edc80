// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__NR90_SERIAL_COMM_HPP_
#define GEN3_NETWORK_MANAGER_CORE__NR90_SERIAL_COMM_HPP_

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <termios.h>

#include "rclcpp/rclcpp.hpp"

namespace gen3_network_manager_core
{

// 前向声明
class UsbAtComm;

// 错误代码定义
enum class Nr90Error {
  SUCCESS = 0,
  DEVICE_OPEN_FAILED = -1,
  DEVICE_CONFIG_FAILED = -2,
  COMMAND_SEND_FAILED = -3,
  RESPONSE_TIMEOUT = -4,
  RESPONSE_INVALID = -5,
  DEVICE_NOT_READY = -6,
  MEMORY_ALLOC_FAILED = -7,
  INVALID_PARAM = -8
};

// AT命令响应结构体
struct Nr90Response {
  std::string response;           // 响应内容
  size_t length;                  // 响应长度
  bool is_ok;                     // 是否成功响应
  bool is_error;                  // 是否错误响应
  std::chrono::system_clock::time_point timestamp; // 响应时间戳

  Nr90Response() : length(0), is_ok(false), is_error(false) {
    timestamp = std::chrono::system_clock::now();
  }
};

// 服务小区信息结构体
struct Nr90ServingCellInfo {
  std::string act_type;           // 制式: "WCDMA", "LTE", "NR"
  int pcid;                       // 物理小区号 (LTE/NR)
  std::string state;              // UE状态: "SEARCH", "LIMSRV", "NOCONN", "CONNECT"
  int mcc;                        // 移动国家码
  int mnc;                        // 移动网络码
  int tac;                        // 跟踪区编码 (LTE/NR)
  int lac;                        // 位置区域码 (WCDMA)
  int arfcn;                      // 小区的ARFCN
  int band;                       // 频段
  int ul_bandwidth;               // 上行频段带宽 (LTE/NR)
  int dl_bandwidth;               // 下行频段带宽 (LTE/NR)
  int rsrp;                       // 参考信号接收功率 (LTE/NR)
  int rsrq;                       // 参考信号接收质量 (LTE/NR)
  int rscp;                       // 接收信号码功率电平 (WCDMA)
  int rssi;                       // 接收信号强度指示
  int sinr;                       // 信噪比 (LTE/NR)

  Nr90ServingCellInfo() : pcid(-1), mcc(-1), mnc(-1), tac(-1), lac(-1),
                         arfcn(-1), band(-1), ul_bandwidth(-1), dl_bandwidth(-1),
                         rsrp(-999), rsrq(-999), rscp(-999), rssi(-999), sinr(-999) {}
};

// AT命令定义结构体
struct Nr90AtCommand {
  std::string command;            // AT命令
  std::string description;        // 命令描述
  int timeout_seconds;            // 超时时间(秒)
  bool is_critical;               // 是否为关键命令
  
  Nr90AtCommand(const std::string & cmd, const std::string & desc, 
                int timeout = 3, bool critical = false)
    : command(cmd), description(desc), timeout_seconds(timeout), is_critical(critical) {}
};

class Nr90SerialComm
{
public:
  explicit Nr90SerialComm(const rclcpp::Node::SharedPtr & node);
  ~Nr90SerialComm();

  // 设备管理
  Nr90Error init(const std::string & device_path, int baud_rate = 115200);
  Nr90Error open_device();
  Nr90Error configure_device();
  Nr90Error close_device();
  void cleanup();

  // 串口通信
  Nr90Error send_command(const std::string & command);
  Nr90Error read_response(Nr90Response & response, int timeout_seconds = 3);
  Nr90Error send_at_command(const std::string & command, Nr90Response & response, 
                           int timeout_seconds = 3);

  // AT命令验证
  Nr90Error test_basic_communication();
  Nr90Error get_device_info(std::string & device_info);
  Nr90Error check_network_status(std::string & network_status);
  Nr90Error verify_5g_capability(bool & has_5g);
  
  // 网络信息获取
  Nr90Error get_signal_strength(int32_t & rssi, int32_t & rsrp, int32_t & rsrq);
  Nr90Error get_network_registration(std::string & status);
  Nr90Error get_operator_info(std::string & operator_name);
  // Nr90Error get_network_type(std::string & network_type);
  Nr90Error get_cell_info(std::string & cell_id, uint32_t & frequency);

  // USB网卡拨号模式控制
  Nr90Error set_usb_dial_mode(int mode);
  Nr90Error get_usb_dial_status(int & mode, std::string & status);

  // 网络信息查询
  Nr90Error get_network_info(std::string & access_tech, std::string & operator_code, int & band);
  Nr90Error get_serving_cell_info(Nr90ServingCellInfo & cell_info);

  // 网络搜索偏好配置
  Nr90Error set_network_search_config(int mode_pref);
  Nr90Error get_network_search_config(int & mode_pref);

  // 状态检查
  bool is_device_open() const { return fd_ >= 0; }
  bool is_device_configured() const { return is_configured_; }
  bool is_device_ready();

  // 工具函数
  static std::string error_to_string(Nr90Error error);
  static bool is_response_ok(const std::string & response);
  static bool is_response_error(const std::string & response);

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 设备配置
  std::string device_path_;
  int fd_;
  int baud_rate_;
  struct termios original_termios_;
  bool is_configured_;

  // USB AT 通信备用方案
  std::unique_ptr<UsbAtComm> usb_at_comm_;
  bool use_usb_fallback_;

  // 预定义的AT命令列表
  static const std::vector<Nr90AtCommand> at_commands_;
  
  // 内部函数
  Nr90Error configure_serial_port();
  Nr90Error write_data(const std::string & data);
  Nr90Error read_data(std::string & data, int timeout_seconds);
  
  // 响应解析函数
  bool parse_signal_response(const std::string & response, int32_t & rssi,
                            int32_t & rsrp, int32_t & rsrq);
  bool parse_registration_response(const std::string & response, std::string & status);
  bool parse_operator_response(const std::string & response, std::string & operator_name);
  bool parse_network_type_response(const std::string & response, std::string & network_type);
  bool parse_cell_info_response(const std::string & response, std::string & cell_id,
                               uint32_t & frequency);
  bool parse_usb_dial_response(const std::string & response, int & mode, std::string & status);
  bool parse_network_info_response(const std::string & response, std::string & access_tech,
                                  std::string & operator_code, int & band);
  bool parse_serving_cell_info_response(const std::string & response, Nr90ServingCellInfo & cell_info);
  bool parse_network_search_config_response(const std::string & response, int & mode_pref);

  // 辅助函数
  std::string get_network_search_mode_name(int mode_pref);
  
  // 工具函数
  void log_debug(const std::string & message);
  void log_info(const std::string & message);
  void log_warn(const std::string & message);
  void log_error(const std::string & message);
  
  // 字符串处理
  std::string trim(const std::string & str);
  std::vector<std::string> split(const std::string & str, char delimiter);
  bool contains_ignore_case(const std::string & haystack, const std::string & needle);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__NR90_SERIAL_COMM_HPP_
