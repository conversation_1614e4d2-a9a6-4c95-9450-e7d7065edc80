// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__UTILS__NETWORK_UTILS_HPP_
#define GEN3_NETWORK_MANAGER_CORE__UTILS__NETWORK_UTILS_HPP_

#include <string>
#include <vector>
#include <map>
#include <tuple>

#include "rclcpp/rclcpp.hpp"

namespace gen3_network_manager_core
{
namespace utils
{

class NetworkUtils
{
public:
  // 网络接口信息
  struct InterfaceInfo {
    std::string name;
    std::string ip_address;
    std::string mac_address;
    std::string netmask;
    std::string gateway;
    bool is_up;
    bool is_running;
  };

  // WiFi网络信息
  struct WiFiInfo {
    std::string ssid;
    std::string bssid;
    int32_t signal_strength;        // 信号强度 (dBm)
    uint32_t frequency;             // 频率 (MHz)
    std::string security_type;
    bool is_connected;
  };
  
  // 5G网络信息
  struct FiveGInfo {
    std::string operator_name;      // 运营商名称
    std::string network_type;       // 网络类型 (5G, 4G, 3G等)
    std::string registration_status; // 注册状态
    int32_t signal_strength;        // 信号强度 (dBm)
    int32_t signal_quality;         // 信号质量 (0-100%)
    uint32_t frequency;             // 频率 (MHz)
    std::string cell_id;            // 小区ID
    std::string imsi;               // IMSI
    std::string imei;               // IMEI
    bool is_connected;              // 是否已连接
    std::string device_path;        // 设备路径
  };
  
  // 网络配置
  struct NetworkConfig {
    std::string wifi_interface;
    std::string ethernet_interface;
    std::string g5_interface;  // 恢复为单个5G接口（初始化时选择好的）
  };

  // 设置ROS节点
  static void set_node(const rclcpp::Node::SharedPtr & node);
  
  // 获取网络配置
  static NetworkConfig get_network_config();
  
  // 获取所有网络接口
  static std::vector<InterfaceInfo> get_network_interfaces();
  
  // 获取特定网络接口信息
  static InterfaceInfo get_interface_info(const std::string & interface_name);
  
  // 获取当前WiFi连接信息
  static WiFiInfo get_current_wifi_info();
  
  // 扫描可用WiFi网络
  static std::vector<WiFiInfo> scan_wifi_networks();
  
  // 连接到WiFi网络
  static bool connect_to_wifi(
    const std::string & ssid, const std::string & password,
    const std::string & security_type = "");
  
  // 断开WiFi连接
  static bool disconnect_wifi();
  
  // 5G网络相关函数
  static FiveGInfo get_5g_network_info(const std::string & device_path = "/dev/ttyUSB0");
  static bool test_5g_connectivity(const std::string & device_path = "/dev/ttyUSB0");
  static int32_t get_5g_signal_strength(const std::string & device_path = "/dev/ttyUSB0");
  static bool is_5g_interface(const std::string & interface_name);
  
  // 获取DNS服务器列表
  static std::vector<std::string> get_dns_servers();

  // 信号强度转换函数
  static int32_t convert_signal_percentage_to_dbm(int percentage);
  
  // 设置DNS服务器
  static bool set_dns_servers(const std::vector<std::string> & dns_servers);
  
  // 测试网络连接（使用国内DNS服务器）
  static bool test_connectivity(const std::string & host = "***************", int timeout_ms = 1000);

  // 测试DNS解析（返回成功标志和解析时间）
  static std::tuple<bool, double> test_dns_resolution_with_time(const std::string & domain = "www.baidu.com", int timeout_ms = 1000);

  // 测试多个域名的DNS解析时间（计算平均解析时间）
  static std::tuple<bool, double> test_dns_resolution_with_time(const std::vector<std::string> & domains, int timeout_ms = 1000);

  // 测试DNS解析（保留兼容性）
  static bool test_dns_resolution(const std::string & domain = "www.baidu.com", int timeout_ms = 1000);

  // 测试网络延迟（支持多个地址，计算平均延迟）
  static std::tuple<bool, double> test_latency(const std::vector<std::string> & hosts, int count = 3);

  // 测试单个地址延迟（保留兼容性）
  static std::tuple<bool, double> test_latency(const std::string & host, int count = 3);

  // 整合的网络质量测试（同时获取延迟和丢包率）
  // 返回: <成功标志, 平均延迟(ms), 平均丢包率(%)>
  static std::tuple<bool, double, double> test_network_quality(const std::vector<std::string> & hosts, int count = 5);

  // 单地址网络质量测试（保留兼容性）
  static std::tuple<bool, double, double> test_network_quality(const std::string & host, int count = 5);
  
  // 测试网络速度
  static std::tuple<bool, double, double> test_network_speed(const std::string & url = "");
  
  // 启用/禁用网络接口
  static bool set_interface_state(const std::string & interface_name, bool enable);
  
  // 设置网络路由
  static bool set_default_route(const std::string & interface_name);
  
  // 获取默认路由接口
  static std::string get_default_route_interface();

  // 路由管理增强功能
  static std::vector<std::string> get_default_routes();
  static bool backup_routes(std::vector<std::string> & backup);
  static bool restore_routes(const std::vector<std::string> & backup);
  static bool has_default_route_for_interface(const std::string & interface_name);
  static bool update_route_priority(const std::string & interface_name, int priority);
  static bool set_route_metric(const std::string & interface_name, int metric);
  static bool set_wifi_route_priority(const std::string & wifi_interface);
  static bool clean_duplicate_wifi_routes(const std::string & wifi_interface);
  static bool wait_for_ip_address(const std::string & interface_name, int timeout_seconds);
  static bool manage_route_intelligently(const std::string & target_interface, uint8_t network_type);
  
  // 获取网络类型
  static uint8_t get_network_type(
    const std::string & interface_name,
    const std::string & wifi_interface,
    const std::string & ethernet_interface,
    const std::string & fiveg_interface);

  // 执行系统命令（公开接口）
  static std::string exec_command(const std::string & cmd);

  // 网卡存在性检测
  static bool interface_exists(const std::string & interface_name);

  // 从多个5G网卡选项中选择第一个可用的
  static std::string select_available_5g_interface(const std::vector<std::string> & interface_candidates);

  // 获取当前选择的5G接口（从配置中选择第一个可用的）
  static std::string get_current_5g_interface();
  
private:
  // ROS节点
  static rclcpp::Node::SharedPtr node_;
  
  // 缓存的网络配置
  static NetworkConfig cached_config_;
  static bool config_loaded_;
};

}  // namespace utils
}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__UTILS__NETWORK_UTILS_HPP_ 