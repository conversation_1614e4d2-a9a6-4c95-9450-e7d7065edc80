#ifndef GEN3_NETWORK_MANAGER_CORE__UTILS__ENUM_UTILS_HPP_
#define GEN3_NETWORK_MANAGER_CORE__UTILS__ENUM_UTILS_HPP_

#include <string>
#include "gen3_network_interfaces/msg/network_status.hpp"
#include "gen3_network_interfaces/msg/binding_status.hpp"

namespace gen3_network_manager_core
{
namespace utils
{

/**
 * @brief 枚举值转换工具类
 * 
 * 提供统一的枚举值到可读字符串的转换功能，避免代码重复
 */
class EnumUtils
{
public:
  /**
   * @brief 将网络类型枚举转换为可读字符串
   * @param network_type 网络类型枚举值
   * @return 可读的网络类型字符串
   */
  static std::string network_type_to_string(uint8_t network_type);

  /**
   * @brief 将连接状态枚举转换为可读字符串
   * @param connection_status 连接状态枚举值
   * @return 可读的连接状态字符串
   */
  static std::string connection_status_to_string(uint8_t connection_status);

  /**
   * @brief 将绑定状态枚举转换为可读字符串
   * @param binding_status 绑定状态枚举值
   * @return 可读的绑定状态字符串
   */
  static std::string binding_status_to_string(uint8_t binding_status);

  /**
   * @brief 将绑定方式枚举转换为可读字符串
   * @param binding_method 绑定方式枚举值
   * @return 可读的绑定方式字符串
   */
  static std::string binding_method_to_string(uint8_t binding_method);
};

}  // namespace utils
}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__UTILS__ENUM_UTILS_HPP_
