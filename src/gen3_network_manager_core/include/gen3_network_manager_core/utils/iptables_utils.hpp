#ifndef GEN3_NETWORK_MANAGER_CORE__UTILS__IPTABLES_UTILS_HPP_
#define GEN3_NETWORK_MANAGER_CORE__UTILS__IPTABLES_UTILS_HPP_

#include <string>
#include <vector>

namespace gen3_network_manager_core {
namespace utils {

/**
 * @brief iptables管理工具类
 * 
 * 提供NAT规则管理、IP转发设置等功能，用于支持内网设备通过外网卡上网
 */
class IptablesUtils {
public:
  /**
   * @brief 为指定外网接口设置NAT规则
   * 
   * @param wan_interface 外网接口名称（如wlan0, eth2等）
   * @param lan_interfaces 内网接口名称列表（如eth0, eth1等）
   * @return true 设置成功
   * @return false 设置失败
   */
  static bool setup_nat_for_interface(const std::string & wan_interface, 
                                     const std::vector<std::string> & lan_interfaces);

  /**
   * @brief 清理所有NAT规则
   * 
   * @return true 清理成功
   * @return false 清理失败
   */
  static bool cleanup_nat_rules();

  /**
   * @brief 启用IP转发
   * 
   * @return true 启用成功
   * @return false 启用失败
   */
  static bool enable_ip_forwarding();

  /**
   * @brief 保存iptables规则到系统
   * 
   * @return true 保存成功
   * @return false 保存失败
   */
  static bool save_iptables_rules();

  /**
   * @brief 获取当前的NAT规则列表
   * 
   * @return std::vector<std::string> NAT规则列表
   */
  static std::vector<std::string> get_current_nat_rules();

  /**
   * @brief 检查指定接口的NAT规则是否存在
   * 
   * @param wan_interface 外网接口名称
   * @return true 规则存在
   * @return false 规则不存在
   */
  static bool check_nat_rule_exists(const std::string & wan_interface);

private:
  /**
   * @brief 执行系统命令并返回输出
   * 
   * @param cmd 要执行的命令
   * @return std::string 命令输出
   */
  static std::string exec_command(const std::string & cmd);
};

}  // namespace utils
}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__UTILS__IPTABLES_UTILS_HPP_
