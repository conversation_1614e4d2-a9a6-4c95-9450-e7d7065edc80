// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__NETWORK_SWITCH_HPP_
#define GEN3_NETWORK_MANAGER_CORE__NETWORK_SWITCH_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>
#include <future>
#include <thread>

#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/network_status.hpp"
#include "gen3_network_interfaces/msg/network_quality.hpp"

namespace gen3_network_manager_core
{

class NetworkSwitch
{
public:
  explicit NetworkSwitch(const rclcpp::Node::SharedPtr & node);
  ~NetworkSwitch();

  // 初始化函数
  bool init();
  
  // 公共接口
  bool switch_to_network(uint8_t network_type, const std::string & wifi_ssid = "", bool force_switch = false);
  bool get_best_network(uint8_t & network_type, std::string & wifi_ssid);
  bool is_switching() const;

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr switch_status_pub_;
  rclcpp::Publisher<gen3_network_interfaces::msg::NetworkQuality>::SharedPtr network_quality_pub_;

  // 订阅者
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr network_status_sub_;

  // 定时器
  rclcpp::TimerBase::SharedPtr quality_check_timer_;
  rclcpp::TimerBase::SharedPtr switch_monitor_timer_;
  rclcpp::TimerBase::SharedPtr init_timer_;  // 一次性初始化定时器
  
  // 内部状态
  std::mutex switch_mutex_;
  std::atomic<bool> is_switching_;
  gen3_network_interfaces::msg::NetworkStatus current_status_;
  std::map<std::string, gen3_network_interfaces::msg::NetworkQuality> network_quality_map_;

  // 异步网络质量检查状态
  std::atomic<bool> quality_check_in_progress_{false};
  std::future<gen3_network_interfaces::msg::NetworkQuality> quality_check_future_;
  std::string current_quality_check_interface_;
  
  // 网络切换参数
  struct SwitchParams {
    uint8_t target_network_type;
    std::string target_wifi_ssid;
    bool force_switch;
    bool in_progress;
    rclcpp::Time start_time;
    uint32_t timeout_seconds;
  };
  SwitchParams current_switch_params_;
  
  // 回调函数
  void network_status_callback(const gen3_network_interfaces::msg::NetworkStatus::SharedPtr msg);
  void quality_check_timer_callback();
  void switch_monitor_timer_callback();
  void init_timer_callback();  // 一次性初始化定时器回调
  
  // 辅助函数
  bool check_network_quality(uint8_t network_type, const std::string & interface_name);
  bool execute_network_switch(uint8_t network_type, const std::string & wifi_ssid);
  void publish_switch_status(const std::string & status);
  void publish_network_quality(const gen3_network_interfaces::msg::NetworkQuality & quality);
  float calculate_network_score(const gen3_network_interfaces::msg::NetworkQuality & quality);

  // WiFi密码管理
  std::string get_saved_wifi_password(const std::string & ssid);

  // 异步网络质量检查辅助函数
  void start_async_quality_check(uint8_t network_type, const std::string & interface_name);
  void check_async_quality_result();
  gen3_network_interfaces::msg::NetworkQuality perform_quality_check_async(
    uint8_t network_type, const std::string & interface_name);

  // 科学评分算法辅助函数
  double calculate_latency_score(double latency_ms, uint8_t network_type);
  double calculate_packet_loss_score(double packet_loss_rate);
  double calculate_signal_strength_score(int32_t signal_strength, uint8_t network_type);
  // double calculate_bandwidth_score(double bandwidth_mbps, bool is_download);  // 暂时去掉带宽评分
  double calculate_connectivity_score(const gen3_network_interfaces::msg::NetworkQuality & quality);
  double get_network_type_factor(uint8_t network_type);
  double calculate_stability_factor(const gen3_network_interfaces::msg::NetworkQuality & quality);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__NETWORK_SWITCH_HPP_ 