# 网络管理器配置文件说明

本目录包含网络管理器的各种配置文件，用于不同的使用场景。

## 配置文件列表

### 1. network_config.yaml
**默认配置文件** - 适用于大多数场景的平衡配置

### 2. development_config.yaml
**开发环境配置** - 高频监控，便于调试
- 更短的监控间隔
- 更宽松的网络质量要求
- 更多的重连尝试次数
- 更长的超时时间

### 3. production_config.yaml
**生产环境配置** - 平衡性能和监控
- 适中的监控间隔
- 较高的网络质量要求
- 标准的重连策略
- 优化的资源使用

## 新增功能配置

### NAT和内网卡配置
所有配置文件都包含以下NAT相关配置：

```yaml
# NAT和内网卡配置
enable_nat: true                    # 启用NAT功能
lan_interfaces: ["eth0", "eth1"]    # 内网卡列表
```

**功能说明**：
- `enable_nat`: 控制是否启用NAT转发功能
- `lan_interfaces`: 配置内网卡接口列表，支持多个内网接口

**使用场景**：
- 设备作为网关，需要为内网设备提供外网访问
- WiFi和5G自动切换时，确保内网设备能正常上网
- 企业级部署中的多网卡环境

## 使用方法

### 1. 使用默认配置
```bash
ros2 launch gen3_network_manager_core network_manager.launch.py
```

### 2. 使用开发环境配置
```bash
ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=src/gen3_network_manager_core/config/development_config.yaml
```

### 3. 使用生产环境配置
```bash
ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=src/gen3_network_manager_core/config/production_config.yaml
```

### 4. 使用自定义配置文件
```bash
ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=/path/to/your/custom_config.yaml
```

## 配置参数说明

### 定时器间隔参数
- `network_check_interval`: 网络状态监控间隔（秒）
- `quality_check_interval`: 网络质量检查间隔（秒）
- `connectivity_check_interval`: 连通性检查间隔（秒）
- `wifi_scan_interval`: WiFi网络扫描间隔（秒）
- `switch_monitor_interval`: 网络切换监控间隔（秒）
- `dns_check_interval`: DNS配置检查间隔（秒）
- `status_update_interval`: 状态更新发布间隔（秒）
- `binding_status_interval`: 绑定状态发布间隔（秒）

### 网络功能参数
- `enable_auto_switch`: 是否启用自动网络切换
- `preferred_network_type`: 首选网络类型（1=WiFi, 2=5G, 3=以太网）
- `min_signal_strength`: 最低信号强度要求（dBm）
- `min_connectivity_score`: 最低连接质量评分（0-100）

### WiFi配置
- `wifi.min_signal_strength`: WiFi最低信号强度
- `wifi.reconnect_attempts`: 自动重连尝试次数
- `wifi.known_networks`: 已知网络列表

### 网络切换配置
- `network_switch.cooldown_period`: 切换冷却时间
- `network_switch.quality_weights`: 质量评分权重

## 配置文件对比

| 参数 | 默认配置 | 开发配置 | 生产配置 |
|------|----------|----------|----------|
| 网络监控间隔 | 1.0s | 0.5s | 2.0s |
| WiFi扫描间隔 | 60s | 30s | 120s |
| 质量检查间隔 | 5.0s | 2.0s | 10.0s |
| 最低信号强度 | -70dBm | -80dBm | -70dBm |
| 最低质量评分 | 50.0 | 30.0 | 60.0 |
| 切换冷却时间 | 60s | 30s | 120s |

## 自定义配置

您可以基于现有配置文件创建自定义配置：

1. 复制一个现有配置文件
2. 根据需要修改参数值
3. 使用 `config_file` 参数指定您的配置文件

## 注意事项

1. 配置文件必须遵循YAML格式
2. 所有参数都有默认值，可以只配置需要修改的参数
3. 定时器间隔不建议设置过小，可能影响系统性能
4. 生产环境建议使用较长的监控间隔以节省资源
