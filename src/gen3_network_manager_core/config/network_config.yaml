gen3_network_manager:
  ros__parameters:
    # 日志配置
    log_level: "debug"  # 可选值: debug, info, warn, error, fatal

    # 定时器间隔配置（秒）
    network_check_interval: 1.0          # network_monitor.cpp 中声明
    quality_check_interval: 5.0          # network_switch.cpp 中声明
    connectivity_check_interval: 10.0    # network_monitor.cpp 中声明
    wifi_scan_interval: 60.0
    switch_monitor_interval: 1.0         # network_switch.cpp 中声明
    dns_check_interval: 30.0
    status_update_interval: 15.0
    binding_status_interval: 2.0

    # 网络功能配置
    enable_auto_switch: true
    preferred_network_type: 1
    min_signal_strength: -70
    min_connectivity_score: 50.0

    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth2", "eth3"]  # 内网卡列表

    # 网络接口配置
    # wifi_interface: "wlp1s0"
    # ethernet_interface: "wlan1"
    # 5g_interface: "br-0f6ab56894f0"

    # wifi_interface: "wlan0"
    # ethernet_interface: "wlan1"
    # 5g_interface: "eth0"

        # 网络接口配置
    # wifi_interface: "wlp1s0"
    # ethernet_interface: "wlan1"
    # 5g_interface: "br-0f6ab56894f0"

    # wifi_interface: "wlan0"
    # ethernet_interface: "wlan1"
    # 5g_interface: "eth0"

    wifi_interface: "wlan0"
    ethernet_interface: "eth9"
    # 5G网卡配置 - 支持多个选项，按优先级排序
    5g_interfaces: ["eth0", "usb0", "wwan0", "enp0s20f0u3", "usb5g0"]  # 系统启动时自动检测可用网卡

    # === 高优先级WiFi网络配置 ===
    # ROS2参数系统需要使用简单数组格式，各数组索引对应
    priority_wifi_networks:
      ssids: ["K3_5g", "Office_WiFi", "ZYHY-Guest", "CMCC", "Guest_WiFi"]
      passwords: ["your_password_here", "office_password", "", "cmcc_password", ""]
      priorities: [100, 90, 80, 70, 50]
      security_types: ["WPA2", "WPA2", "OPEN", "WPA2", "OPEN"]

    # === 智能DNS管理配置 ===
    # DNS检测和切换参数
    dns_test_timeout_ms: 3000
    dns_max_consecutive_failures: 3
    dns_priority_update_threshold: 0.8

    # 科学化网络质量评分权重配置
    network_switch:
      auto_switch_enabled: true
      preferred_network_type: 1
      min_signal_strength: -70
      min_connectivity_score: 50.0

      # 科学评分权重（总和应为1.0）
      quality_weights:
        latency: 0.3          # 延迟权重30%
        packet_loss: 0.25     # 丢包率权重25%
        signal_strength: 0.2  # 信号强度权重20%
        connectivity: 0.25    # 连通性权重25%
        # 带宽评分已暂时去掉
        # download_speed: 0.0
        # upload_speed: 0.0

      # 不同网络类型的延迟期望值配置（毫秒）
      expected_latency:
        wifi: 200.0        # WiFi网络期望延迟
        fiveg: 100.0       # 5G网络期望延迟
        ethernet: 60.0     # 以太网期望延迟
        default: 100.0     # 默认期望延迟

    # === DNS服务器配置 ===
    dns_servers:
      # 国际DNS服务器（全球通用）
      primary: ["8.8.8.8", "1.1.1.1"]                    # Google DNS, Cloudflare DNS
      secondary: ["114.114.114.114", "223.5.5.5"]        # 114DNS, 阿里DNS
      backup: ["208.67.222.222", "9.9.9.9"]              # OpenDNS, Quad9 DNS

      # 中国优化DNS服务器配置
      china_primary: ["114.114.114.114", "223.5.5.5"]    # 114DNS, 阿里DNS
      china_secondary: ["119.29.29.29", "180.76.76.76"]  # 腾讯DNS, 百度DNS
      china_backup: ["1.2.4.8", "210.2.4.8"]            # 中国电信DNS
      international: ["8.8.8.8", "1.1.1.1", "208.67.222.222"]  # 国际DNS备份

    # === 中国网络环境检测配置 ===
    china_network:
      # 是否启用中国网络环境检测
      enable_detection: true

      # 网络环境检测间隔（秒）
      detection_interval: 300.0  # 5分钟检测一次

      # 是否优先使用国内DNS服务器
      prefer_domestic_dns: true

      # 国内网站连通性测试列表
      domestic_test_sites:
        - "www.baidu.com"
        - "www.qq.com"
        - "www.taobao.com"
        - "www.163.com"
        - "www.sina.com.cn"

    # === 连通性检查配置 ===
    connectivity:
      # DNS解析测试域名
      dns_server: "www.baidu.com"

      # 互联网连通性测试服务器
      internet_server: "114.114.114.114"

      # 超时配置（毫秒）
      dns_timeout_ms: 3000
      ping_timeout_ms: 2500
      gateway_timeout_ms: 1500
      external_timeout_ms: 3000

    # === 蜂窝网络(5G)配置 ===
    # 5G模块设备配置
    fiveg_device_path: "/dev/ttyUSB0"        # 5G模块串口设备路径
    fiveg_baud_rate: 115200                  # 串口波特率

    # 5G状态监控间隔配置（秒）
    fiveg_status_interval: 10.0              # 状态更新间隔
    fiveg_quality_interval: 5.0              # 信号质量更新间隔

    # === 异步处理配置 ===
    # 异步网络质量检查配置
    async_quality_check:
      enable: true
      max_concurrent_checks: 3
      check_timeout_ms: 10000

    # 异步网络质量回调配置
    async_quality_callback:
      enable: true
      processing_timeout_ms: 5000