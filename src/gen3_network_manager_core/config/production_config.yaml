/**:
  ros__parameters:
    # 生产环境配置 - 平衡监控和性能

    # 日志配置
    log_level: "warn"  # 生产环境使用warn级别，减少日志输出

    # 定时器间隔配置（秒）- 对应代码中的参数声明
    network_check_interval: 2.0      # 网络监控 2秒 (network_monitor.cpp)
    quality_check_interval: 10.0     # 质量检查 10秒 (network_switch.cpp)
    connectivity_check_interval: 15.0 # 连通性检查 15秒 (network_monitor.cpp)
    wifi_scan_interval: 120.0        # WiFi扫描 2分钟
    switch_monitor_interval: 2.0     # 切换监控 2秒 (network_switch.cpp)
    dns_check_interval: 60.0         # DNS检查 1分钟
    status_update_interval: 15.0     # 状态更新 15秒
    binding_status_interval: 5.0     # 绑定状态 5秒
    
    # 网络功能配置
    enable_auto_switch: "true"
    preferred_network_type: 1        # WiFi优先
    min_signal_strength: -70         # 标准信号要求
    min_connectivity_score: 60.0    # 较高的质量要求

    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表

    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    # 5G网卡配置 - 支持多个选项，按优先级排序
    5g_interfaces: ["eth2", "usb0", "wwan0", "enp0s20f0u3", "usb5g0"]  # 系统启动时自动检测可用网卡
    
    # WiFi设置
    wifi:
      min_signal_strength: -70
      reconnect_attempts: 3
      scan_interval: 120.0
      known_networks:
        - ssid: "Gen3_WiFi"
          priority: 100
          auto_connect: "true"
        - ssid: "Gen3_WiFi_5G"
          priority: 90
          auto_connect: "true"
        - ssid: "Gen3_Guest"
          priority: 50
          auto_connect: "false"
    
    # 5G设置
    5g:
      apn: "cmnet"
      pin: ""
      connect_timeout: 30.0
      auto_reconnect: "true"
    
    # === 智能网络切换设置 ===
    network_switch:
      auto_switch_enabled: true
      preferred_network_type: 1
      min_signal_strength: -70
      min_connectivity_score: 60.0
      cooldown_period: 120.0         # 较长的冷却时间

      # 科学化质量评分权重（生产环境平衡配置）
      quality_weights:
        latency: 0.3               # 延迟权重
        packet_loss: 0.25          # 丢包率权重
        signal_strength: 0.2       # 信号强度权重
        connectivity: 0.25         # 连通性权重

      # 生产环境延迟期望值配置（毫秒）- 严格的期望值
      expected_latency:
        wifi: 18.0        # WiFi网络期望延迟（生产环境更严格）
        fiveg: 12.0       # 5G网络期望延迟（生产环境更严格）
        ethernet: 3.0     # 以太网期望延迟（生产环境更严格）
        default: 40.0     # 默认期望延迟（生产环境更严格）
        # 带宽评分已暂时去掉
        # download_speed: 0.0
        # upload_speed: 0.0

    # === 智能DNS管理设置 ===
    # DNS检测和切换参数（生产环境稳定配置）
    dns_test_timeout_ms: 3000        # 生产环境标准超时
    dns_max_consecutive_failures: 3  # 生产环境保守的切换策略
    dns_priority_update_threshold: 0.8 # 生产环境稳定的优先级更新

    # DNS服务器配置
    dns_servers:
      # 国际DNS服务器（生产环境可靠配置）
      primary: ["8.8.8.8", "1.1.1.1"]
      secondary: ["114.114.114.114", "223.5.5.5"]
      backup: ["208.67.222.222", "9.9.9.9"]

      # 中国优化DNS服务器配置
      china_primary: ["114.114.114.114", "223.5.5.5"]    # 114DNS, 阿里DNS
      china_secondary: ["119.29.29.29", "180.76.76.76"]  # 腾讯DNS, 百度DNS
      china_backup: ["1.2.4.8", "210.2.4.8"]            # 中国电信DNS
      international: ["8.8.8.8", "1.1.1.1", "208.67.222.222"]

    # === 中国网络环境检测配置 ===
    china_network:
      # 生产环境启用中国网络检测
      enable_detection: true

      # 生产环境标准检测间隔
      detection_interval: 600.0  # 10分钟检测一次

      # 优先使用国内DNS
      prefer_domestic_dns: true

      # 国内网站测试列表（生产环境完整）
      domestic_test_sites:
        - "www.baidu.com"
        - "www.qq.com"
        - "www.taobao.com"
        - "www.163.com"
        - "www.sina.com.cn"

    # === 连通性检查配置 ===
    connectivity:
      dns_server: "www.baidu.com"      # 生产环境使用国内DNS测试
      internet_server: "114.114.114.114"  # 使用国内DNS测试

      # 生产环境超时配置
      dns_timeout_ms: 3000
      ping_timeout_ms: 2500
      gateway_timeout_ms: 1500
      external_timeout_ms: 3000

    # === 异步处理配置 ===
    async_quality_check:
      enable: true
      max_concurrent_checks: 3         # 生产环境标准并发数
      check_timeout_ms: 10000          # 生产环境标准超时

    async_quality_callback:
      enable: true
      processing_timeout_ms: 5000      # 生产环境标准处理超时

    # === 生产环境特殊配置 ===
    # 稳定性配置
    stability:
      enable_failsafe_mode: true       # 启用故障安全模式
      max_switch_attempts: 5          # 最大切换尝试次数
      switch_cooldown_period: 60.0    # 切换冷却期（秒）

    # 性能配置
    performance:
      enable_metrics: false           # 生产环境关闭详细指标
      optimize_for_stability: true   # 优化稳定性
      reduce_cpu_usage: true         # 降低CPU使用率
    
    # 绑定设置
    binding:
      binding_timeout: 300
      binding_method: 3
      ble_name: "Gen3_Robot"
