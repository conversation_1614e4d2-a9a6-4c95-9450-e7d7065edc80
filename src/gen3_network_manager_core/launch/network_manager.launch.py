from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, SetEnvironmentVariable, LogInfo
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, EnvironmentVariable
from launch_ros.substitutions import FindPackageShare
import os
import yaml
import sys

def generate_launch_description():
    # 添加调试输出
    print("===== 网络管理器启动调试信息 =====", file=sys.stderr)
    
    # 配置日志格式环境变量，显示完整时间戳
    set_log_format = SetEnvironmentVariable(
        'RCUTILS_LOGGING_BUFFERED_STREAM', '1'
    )

    set_log_format_detailed = SetEnvironmentVariable(
        'RCUTILS_LOGGING_USE_STDOUT', '1'
    )

    # 设置日志时间戳格式
    set_timestamp_format = SetEnvironmentVariable(
        'RCUTILS_CONSOLE_OUTPUT_FORMAT',
        '[{severity}] [{time}] [{name}]: {message}'
    )

    # 获取包路径
    try:
        pkg_share = FindPackageShare('gen3_network_manager_core').find('gen3_network_manager_core')
        print(f"包路径: {pkg_share}", file=sys.stderr)
    except Exception as e:
        print(f"获取包路径失败: {e}", file=sys.stderr)
        # 尝试使用相对路径
        pkg_share = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"使用相对路径: {pkg_share}", file=sys.stderr)
    
    # 配置文件路径参数
    config_file_path = PathJoinSubstitution([
        FindPackageShare('gen3_network_manager_core'),
        'config',
        'network_config.yaml'
    ])
    
    # 尝试从环境变量读取日志级别
    env_log_level = os.environ.get('RCUTILS_LOGGING_LEVEL', '')
    if env_log_level:
        print(f"从环境变量读取日志级别: {env_log_level}", file=sys.stderr)
        default_log_level = env_log_level
    else:
        default_log_level = 'info'  # 默认值
        print(f"环境变量中未设置日志级别，使用默认值: {default_log_level}", file=sys.stderr)
    
    # 尝试从network_config.yaml读取日志级别
    network_config_path = os.path.join(pkg_share, 'config', 'network_config.yaml')
    print(f"尝试读取配置文件: {network_config_path}", file=sys.stderr)
    
    try:
        if os.path.exists(network_config_path):
            print(f"配置文件存在，正在读取...", file=sys.stderr)
            with open(network_config_path, 'r') as f:
                config_data = yaml.safe_load(f)
                print(f"YAML内容: {config_data}", file=sys.stderr)
                if config_data and 'gen3_network_manager' in config_data and 'ros__parameters' in config_data['gen3_network_manager']:
                    params = config_data['gen3_network_manager']['ros__parameters']
                    if 'log_level' in params:
                        config_log_level = params['log_level']
                        print(f"从network_config.yaml读取日志级别: {config_log_level}", file=sys.stderr)
                        # 如果环境变量未设置，则使用配置文件中的值
                        if not env_log_level:
                            default_log_level = config_log_level
                            print(f"使用配置文件中的日志级别: {default_log_level}", file=sys.stderr)
                    else:
                        print(f"network_config.yaml中未找到log_level参数，使用默认值: {default_log_level}", file=sys.stderr)
                else:
                    print(f"network_config.yaml格式无效，使用默认日志级别: {default_log_level}", file=sys.stderr)
        else:
            print(f"未找到network_config.yaml，使用默认日志级别: {default_log_level}", file=sys.stderr)
    except Exception as e:
        print(f"读取network_config.yaml出错: {e}", file=sys.stderr)
        print(f"使用默认日志级别: {default_log_level}", file=sys.stderr)

    # 声明启动参数
    log_level = LaunchConfiguration('log_level')
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value=default_log_level,
        description='日志级别'
    )

    # 配置文件路径参数
    config_file = LaunchConfiguration('config_file')
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=config_file_path,
        description='网络配置文件路径'
    )
    
    # 打印最终使用的日志级别
    print(f"最终使用的日志级别: {default_log_level}", file=sys.stderr)
    print(f"配置文件路径: {config_file_path}", file=sys.stderr)
    
    # 设置日志级别环境变量（确保节点能够继承）
    set_log_level_env = SetEnvironmentVariable(
        'RCUTILS_LOGGING_LEVEL', 
        default_log_level
    )
    
    # 添加日志信息
    log_info = LogInfo(
        msg=["使用日志级别: ", log_level]
    )
    
    # 网络管理器节点
    network_manager_node = Node(
        package='gen3_network_manager_core',
        executable='network_manager_node',
        name='gen3_network_manager',
        output='screen',
        arguments=['--ros-args', '--log-level', log_level],  # 明确通过命令行参数设置日志级别
        parameters=[
            config_file,  # 加载配置文件
            {'log_level': log_level},  # 日志级别可以通过启动参数覆盖
        ],
        remappings=[
            ('network_status', '/gen3/network/status'),
            ('network_quality', '/gen3/network/quality'),
            ('binding_status', '/gen3/network/binding_status'),
            ('switch_network', '/gen3/network/switch'),
            ('connect_wifi', '/gen3/network/connect_wifi'),
            ('get_network_list', '/gen3/network/list'),
            ('set_network_priority', '/gen3/network/priority'),
            ('start_binding', '/gen3/network/start_binding'),
            ('network_binding', '/gen3/network/binding'),
        ]
    )
    
    return LaunchDescription([
        # 设置日志格式环境变量
        set_log_format,
        set_log_format_detailed,
        set_timestamp_format,
        set_log_level_env,  # 设置日志级别环境变量

        # 启动参数
        log_level_arg,
        config_file_arg,
        
        # 日志信息
        log_info,

        # 网络管理器节点
        network_manager_node,
    ])