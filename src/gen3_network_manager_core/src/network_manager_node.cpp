// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <memory>
#include <string>
#include <iostream>

#include "rclcpp/rclcpp.hpp"
#include "rcutils/logging.h"

#include "gen3_network_manager_core/network_manager.hpp"
#include "gen3_network_manager_core/wifi_manager.hpp"
#include "gen3_network_manager_core/network_switch.hpp"
#include "gen3_network_manager_core/binding_manager.hpp"
#include "gen3_network_manager_core/network_monitor.hpp"
#include "gen3_network_manager_core/dns_manager.hpp"

int main(int argc, char ** argv)
{
  // 初始化ROS
  RCLCPP_INFO(rclcpp::get_logger("gen3_network_manager"), "[MAIN] 初始化ROS2系统");
  rclcpp::init(argc, argv);

  // 创建节点
  auto node = std::make_shared<rclcpp::Node>("gen3_network_manager");
  RCLCPP_INFO(node->get_logger(), "[MAIN] 创建主节点: gen3_network_manager");

  // 从配置文件获取日志级别
  // 首先声明参数，这样ROS2就知道要从配置文件中查找这个参数
  node->declare_parameter("log_level", "info");

  std::string log_level_str = "info";  // 默认为info级别
  log_level_str = node->get_parameter("log_level").as_string();

  RCLCPP_INFO(node->get_logger(), "[MAIN] 配置的日志级别: %s", log_level_str.c_str());

  // 简化的日志级别设置（避免编译错误）
  // 在实际的ROS2环境中，这些常量会被正确定义
  try {
    rcutils_ret_t ret = RCUTILS_RET_OK;
    if (log_level_str == "debug") {
      RCLCPP_INFO(node->get_logger(), "[MAIN] 设置日志级别为DEBUG");
      ret = rcutils_logging_set_logger_level(node->get_logger().get_name(), RCUTILS_LOG_SEVERITY_DEBUG);
    } else if (log_level_str == "info") {
      RCLCPP_INFO(node->get_logger(), "[MAIN] 设置日志级别为INFO");
      ret = rcutils_logging_set_logger_level(node->get_logger().get_name(), RCUTILS_LOG_SEVERITY_INFO);
    } else if (log_level_str == "warn" || log_level_str == "warning") {
      RCLCPP_INFO(node->get_logger(), "[MAIN] 设置日志级别为WARN");
      ret = rcutils_logging_set_logger_level(node->get_logger().get_name(), RCUTILS_LOG_SEVERITY_WARN);
    } else if (log_level_str == "error") {
      RCLCPP_INFO(node->get_logger(), "[MAIN] 设置日志级别为ERROR");
      ret = rcutils_logging_set_logger_level(node->get_logger().get_name(), RCUTILS_LOG_SEVERITY_ERROR);
    } else {
      RCLCPP_WARN(node->get_logger(), "[MAIN] 未知的日志级别: %s，使用默认级别INFO", log_level_str.c_str());
    }
    
    if (ret != RCUTILS_RET_OK) {
      RCLCPP_WARN(node->get_logger(), "[MAIN] 设置日志级别返回非OK状态: %d", ret);
    }
  } catch (const std::exception & e) {
    RCLCPP_ERROR(node->get_logger(), "[MAIN] 设置日志级别失败: %s", e.what());
  }

  RCLCPP_INFO(node->get_logger(), "[MAIN] 启动Gen3网络管理系统...");

  try {
    // 创建各个管理器实例
    auto network_manager = std::make_shared<gen3_network_manager_core::NetworkManager>(node);

    auto wifi_manager = std::make_shared<gen3_network_manager_core::WiFiManager>(node);

    auto network_switch = std::make_shared<gen3_network_manager_core::NetworkSwitch>(node);

    auto binding_manager = std::make_shared<gen3_network_manager_core::BindingManager>(node);

    auto network_monitor = std::make_shared<gen3_network_manager_core::NetworkMonitor>(node);

    auto dns_manager = std::make_shared<gen3_network_manager_core::DNSManager>(node);
    
    // 初始化各个管理器
    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化网络管理器");
    if (!network_manager->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] 网络管理器初始化失败");
      return 1;
    }

    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化WiFi管理器");
    if (!wifi_manager->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] WiFi管理器初始化失败");
      return 1;
    }

    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化网络切换器");
    if (!network_switch->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] 网络切换器初始化失败");
      return 1;
    }

    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化绑定管理器");
    if (!binding_manager->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] 绑定管理器初始化失败");
      return 1;
    }

    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化网络监控器");
    if (!network_monitor->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] 网络监控器初始化失败");
      return 1;
    }

    RCLCPP_INFO(node->get_logger(), "[MAIN] 初始化DNS管理器");
    if (!dns_manager->init()) {
      RCLCPP_ERROR(node->get_logger(), "[MAIN] DNS管理器初始化失败");
      return 1;
    }

    // 运行节点
    RCLCPP_INFO(node->get_logger(), "[MAIN] 开始运行网络管理系统主循环");
    rclcpp::spin(node);

    RCLCPP_INFO(node->get_logger(), "[MAIN] 网络管理系统主循环结束");

  } catch (const std::exception & e) {
    RCLCPP_ERROR(node->get_logger(), "[MAIN] 发生异常: %s", e.what());
    return 1;
  }

  // 关闭ROS
  RCLCPP_INFO(node->get_logger(), "[MAIN] 关闭ROS2系统");
  rclcpp::shutdown();
  return 0;
} 