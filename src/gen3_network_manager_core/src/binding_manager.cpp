// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/binding_manager.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <random>
#include <uuid/uuid.h>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace gen3_network_manager_core
{

BindingManager::BindingManager(const rclcpp::Node::SharedPtr & node)
: node_(node), binding_in_progress_(false), logger_(rclcpp::get_logger("binding_manager"))
{
  RCLCPP_INFO(logger_, "创建绑定管理器");
  
  // 声明参数
  node_->declare_parameter("binding_status_interval", 2.0);
}

BindingManager::~BindingManager()
{
  RCLCPP_INFO(logger_, "销毁绑定管理器");
  
  // 停止绑定线程
  if (binding_thread_.joinable()) {
    binding_thread_.join();
  }
}

bool BindingManager::init()
{
  RCLCPP_INFO(logger_, "初始化绑定管理器");
  
  try {
    // 创建发布者
    binding_status_pub_ = node_->create_publisher<gen3_network_interfaces::msg::BindingStatus>(
      "binding_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建绑定状态发布者: binding_status");

    // 创建服务
    start_binding_srv_ = node_->create_service<gen3_network_interfaces::srv::StartBinding>(
      "start_binding", std::bind(&BindingManager::handle_start_binding, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建启动绑定服务: start_binding");

    // 创建动作服务器
    binding_action_server_ = rclcpp_action::create_server<NetworkBindingAction>(
      node_,
      "network_binding",
      std::bind(&BindingManager::handle_goal, this, _1, _2),
      std::bind(&BindingManager::handle_cancel, this, _1),
      std::bind(&BindingManager::handle_accepted, this, _1));
    RCLCPP_INFO(logger_, "[ACTION] 创建网络绑定动作服务器: network_binding");

    // 创建定时器
    double binding_status_interval = node_->get_parameter("binding_status_interval").as_double();

    binding_status_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(binding_status_interval),
      std::bind(&BindingManager::binding_status_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建绑定状态定时器，间隔: %.1f秒", binding_status_interval);

    // 立即执行一次绑定状态发布
    RCLCPP_DEBUG(logger_, "[TIMER] 立即执行首次绑定状态发布");
    binding_status_timer_callback();
    
    // 初始化绑定状态
    current_binding_status_.binding_status = 
      gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_IDLE;
    current_binding_status_.binding_method = 
      gen3_network_interfaces::msg::BindingStatus::BINDING_METHOD_UNKNOWN;
    current_binding_status_.progress_percentage = 0;
    current_binding_status_.current_step = "空闲";
    current_binding_status_.status_message = "准备就绪";
    
    RCLCPP_INFO(logger_, "绑定管理器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "绑定管理器初始化失败: %s", e.what());
    return false;
  }
}

void BindingManager::binding_status_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 绑定状态定时器触发");
  publish_binding_status();
  RCLCPP_DEBUG(logger_, "[TIMER] 绑定状态定时器处理完成");
}

void BindingManager::handle_start_binding(
  const std::shared_ptr<gen3_network_interfaces::srv::StartBinding::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::StartBinding::Response> response)
{
  RCLCPP_INFO(
    logger_, "[SRV] 收到启动绑定请求 - 方式: %d, 设备名: %s, 超时: %d秒, 重置现有绑定: %s",
    request->binding_method, request->device_name.c_str(),
    request->timeout_seconds, request->reset_existing_binding ? "是" : "否");

  // 检查是否已有绑定进行中
  if (binding_in_progress_.load()) {
    response->success = false;
    response->message = "已有绑定进行中";
    response->error_code = "BINDING_IN_PROGRESS";
    RCLCPP_WARN(logger_, "[SRV] 绑定启动失败: 已有绑定进行中");
    return;
  }

  // 如果需要重置现有绑定
  if (request->reset_existing_binding) {
    RCLCPP_INFO(logger_, "[SRV] 重置现有绑定状态");
    // 重置绑定状态
    std::lock_guard<std::mutex> lock(binding_mutex_);
    current_binding_status_.binding_status =
      gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_IDLE;
    current_binding_status_.device_id = "";
    current_binding_status_.user_device_id = "";
  }

  // 启动绑定流程
  RCLCPP_INFO(logger_, "[SRV] 开始启动绑定流程");
  bool result = start_binding_process(
    request->binding_method, request->timeout_seconds, request->device_name);

  if (result) {
    response->success = true;
    response->message = "绑定启动成功";
    response->error_code = "";
    response->session_id = current_session_id_;
    response->session_start_time = node_->now();
    response->estimated_duration_seconds = request->timeout_seconds;
    RCLCPP_INFO(logger_, "[SRV] 绑定启动成功，会话ID: %s", current_session_id_.c_str());
  } else {
    response->success = false;
    response->message = "绑定启动失败";
    response->error_code = "START_FAILED";
    RCLCPP_ERROR(logger_, "[SRV] 绑定启动失败");
  }
}

rclcpp_action::GoalResponse BindingManager::handle_goal(
  const rclcpp_action::GoalUUID & uuid,
  std::shared_ptr<const NetworkBindingAction::Goal> goal)
{
  (void)uuid;  // 未使用的参数
  
  RCLCPP_INFO(
    logger_, "收到绑定动作目标: 方式=%d, 设备名=%s",
    goal->binding_method, goal->device_name.c_str());
  
  // 检查是否已有绑定进行中
  if (binding_in_progress_.load()) {
    RCLCPP_WARN(logger_, "已有绑定进行中，拒绝新的绑定请求");
    return rclcpp_action::GoalResponse::REJECT;
  }
  
  // 检查绑定方式是否有效
  if (goal->binding_method != NetworkBindingAction::Goal::BINDING_METHOD_BLE && 
      goal->binding_method != NetworkBindingAction::Goal::BINDING_METHOD_QR_CODE && 
      goal->binding_method != NetworkBindingAction::Goal::BINDING_METHOD_BOTH) {
    RCLCPP_WARN(logger_, "无效的绑定方式: %d", goal->binding_method);
    return rclcpp_action::GoalResponse::REJECT;
  }
  
  return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse BindingManager::handle_cancel(
  const std::shared_ptr<GoalHandleNetworkBinding> goal_handle)
{
  (void)goal_handle;  // 未使用的参数
  
  RCLCPP_INFO(logger_, "收到取消绑定请求");
  
  // 设置绑定状态为空闲
  {
    std::lock_guard<std::mutex> lock(binding_mutex_);
    current_binding_status_.binding_status = 
      gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_IDLE;
    current_binding_status_.progress_percentage = 0;
    current_binding_status_.current_step = "已取消";
    current_binding_status_.status_message = "绑定已取消";
  }
  
  binding_in_progress_.store(false);
  
  return rclcpp_action::CancelResponse::ACCEPT;
}

void BindingManager::handle_accepted(const std::shared_ptr<GoalHandleNetworkBinding> goal_handle)
{
  // 启动新线程执行绑定流程
  binding_thread_ = std::thread(
    std::bind(&BindingManager::execute_binding, this, goal_handle));
}

void BindingManager::execute_binding(const std::shared_ptr<GoalHandleNetworkBinding> goal_handle)
{
  RCLCPP_DEBUG(logger_, "开始执行绑定流程");
  
  const auto goal = goal_handle->get_goal();
  auto feedback = std::make_shared<NetworkBindingAction::Feedback>();
  auto result = std::make_shared<NetworkBindingAction::Result>();
  
  // 生成会话ID
  current_session_id_ = generate_session_id();
  
  // 设置绑定状态
  binding_in_progress_.store(true);
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_WAITING,
    0,
    "等待设备连接");
  
  // 设置初始反馈
  feedback->current_step = NetworkBindingAction::Feedback::STEP_INITIALIZING;
  feedback->step_description = "初始化绑定流程";
  feedback->progress_percentage = 0;
  feedback->status_message = "准备就绪";
  feedback->ble_advertising = false;
  feedback->qr_scanner_active = false;
  feedback->connection_attempts = 0;
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  // 根据绑定方式启动对应的绑定流程
  if (goal->binding_method == NetworkBindingAction::Goal::BINDING_METHOD_BLE || 
      goal->binding_method == NetworkBindingAction::Goal::BINDING_METHOD_BOTH) {
    feedback->ble_advertising = true;
    goal_handle->publish_feedback(feedback);
  }
  
  if (goal->binding_method == NetworkBindingAction::Goal::BINDING_METHOD_QR_CODE || 
      goal->binding_method == NetworkBindingAction::Goal::BINDING_METHOD_BOTH) {
    feedback->qr_scanner_active = true;
    goal_handle->publish_feedback(feedback);
  }
  
  // 等待连接
  feedback->current_step = NetworkBindingAction::Feedback::STEP_WAITING_CONNECTION;
  feedback->step_description = "等待设备连接";
  feedback->progress_percentage = 10;
  feedback->status_message = "请使用App扫描二维码或通过蓝牙连接";
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_WAITING,
    10,
    "等待设备连接");
  
  // 模拟等待连接
  std::this_thread::sleep_for(std::chrono::seconds(3));
  
  // 检查是否被取消
  if (goal_handle->is_canceling()) {
    result->success = false;
    result->message = "绑定已取消";
    result->error_code = "CANCELED";
    goal_handle->canceled(result);
    binding_in_progress_.store(false);
    return;
  }
  
  // 接收配置
  feedback->current_step = NetworkBindingAction::Feedback::STEP_RECEIVING_CONFIG;
  feedback->step_description = "接收网络配置";
  feedback->progress_percentage = 30;
  feedback->status_message = "正在从App接收网络配置";
  feedback->connection_attempts = 1;
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONFIGURING,
    30,
    "接收网络配置");
  
  // 模拟接收配置
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 检查是否被取消
  if (goal_handle->is_canceling()) {
    result->success = false;
    result->message = "绑定已取消";
    result->error_code = "CANCELED";
    goal_handle->canceled(result);
    binding_in_progress_.store(false);
    return;
  }
  
  // 连接WiFi
  feedback->current_step = NetworkBindingAction::Feedback::STEP_CONNECTING_WIFI;
  feedback->step_description = "连接WiFi网络";
  feedback->progress_percentage = 50;
  feedback->status_message = "正在连接WiFi网络";
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONNECTING,
    50,
    "连接WiFi网络");
  
  // 模拟连接WiFi
  std::this_thread::sleep_for(std::chrono::seconds(3));
  
  // 检查是否被取消
  if (goal_handle->is_canceling()) {
    result->success = false;
    result->message = "绑定已取消";
    result->error_code = "CANCELED";
    goal_handle->canceled(result);
    binding_in_progress_.store(false);
    return;
  }
  
  // 注册平台
  feedback->current_step = NetworkBindingAction::Feedback::STEP_REGISTERING_PLATFORM;
  feedback->step_description = "注册到平台";
  feedback->progress_percentage = 70;
  feedback->status_message = "正在向平台注册设备";
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_REGISTERING,
    70,
    "注册到平台");
  
  // 模拟注册平台
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 检查是否被取消
  if (goal_handle->is_canceling()) {
    result->success = false;
    result->message = "绑定已取消";
    result->error_code = "CANCELED";
    goal_handle->canceled(result);
    binding_in_progress_.store(false);
    return;
  }
  
  // 完成绑定
  feedback->current_step = NetworkBindingAction::Feedback::STEP_FINALIZING;
  feedback->step_description = "完成绑定";
  feedback->progress_percentage = 90;
  feedback->status_message = "正在完成绑定流程";
  feedback->step_start_time = node_->now();
  
  goal_handle->publish_feedback(feedback);
  
  update_binding_status(
    gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_SUCCESS,
    90,
    "完成绑定");
  
  // 模拟完成绑定
  std::this_thread::sleep_for(std::chrono::seconds(1));
  
  // 获取网络配置
  auto network_config = gen3_network_manager_core::utils::NetworkUtils::get_network_config();

  // 获取当前WiFi信息
  auto current_wifi = gen3_network_manager_core::utils::NetworkUtils::get_current_wifi_info();

  // 获取当前网络接口信息
  auto interfaces = gen3_network_manager_core::utils::NetworkUtils::get_network_interfaces();
  std::string wifi_ip = "";
  for (const auto & iface : interfaces) {
    if (iface.name == network_config.wifi_interface && !iface.ip_address.empty()) {
      wifi_ip = iface.ip_address;
      break;
    }
  }

  // 设置绑定结果
  result->success = true;
  result->message = "绑定成功";
  result->error_code = "";
  result->device_id = "GEN3-" + current_session_id_.substr(0, 8);
  result->user_device_id = "USER-" + current_session_id_.substr(9, 8);
  result->binding_completion_time = node_->now();
  result->wifi_configured = !current_wifi.ssid.empty();
  result->wifi_ssid = current_wifi.ssid.empty() ?
    node_->declare_parameter("default_wifi_ssid", "Gen3_WiFi") : current_wifi.ssid;
  result->wifi_ip_address = wifi_ip.empty() ?
    node_->declare_parameter("default_wifi_ip", "*************") : wifi_ip;
  result->platform_registered = true;
  result->registration_token = "TOKEN-" + current_session_id_;
  
  // 更新绑定状态
  {
    std::lock_guard<std::mutex> lock(binding_mutex_);
    current_binding_status_.binding_status = 
      gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_SUCCESS;
    current_binding_status_.progress_percentage = 100;
    current_binding_status_.current_step = "绑定完成";
    current_binding_status_.status_message = "设备绑定成功";
    current_binding_status_.device_id = result->device_id;
    current_binding_status_.user_device_id = result->user_device_id;
  }
  
  // 发布最终反馈
  feedback->progress_percentage = 100;
  feedback->status_message = "绑定成功";
  goal_handle->publish_feedback(feedback);
  
  // 完成动作
  goal_handle->succeed(result);
  
  RCLCPP_INFO(logger_, "绑定流程完成");
  binding_in_progress_.store(false);
}

bool BindingManager::start_binding_process(
  uint8_t method, uint32_t timeout, const std::string & device_name)
{
  RCLCPP_INFO(
    logger_, "启动绑定流程: 方式=%d, 超时=%d, 设备名=%s",
    method, timeout, device_name.c_str());
  
  try {
    // 生成会话ID
    current_session_id_ = generate_session_id();
    
    // 更新绑定状态
    {
      std::lock_guard<std::mutex> lock(binding_mutex_);
      current_binding_status_.binding_status = 
        gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_WAITING;
      current_binding_status_.binding_method = method;
      current_binding_status_.progress_percentage = 0;
      current_binding_status_.current_step = "等待设备连接";
      current_binding_status_.status_message = "绑定已启动，等待设备连接";
      current_binding_status_.header.stamp = node_->now();
    }
    
    // 设置绑定进行中标志
    binding_in_progress_.store(true);
    
    // 发布绑定状态
    publish_binding_status();
    
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "启动绑定流程异常: %s", e.what());
    binding_in_progress_.store(false);
    return false;
  }
}

void BindingManager::update_binding_status(
  uint8_t status, uint8_t progress, const std::string & message)
{
  std::lock_guard<std::mutex> lock(binding_mutex_);
  
  current_binding_status_.binding_status = status;
  current_binding_status_.progress_percentage = progress;
  current_binding_status_.status_message = message;
  current_binding_status_.header.stamp = node_->now();
  
  // 根据状态更新当前步骤
  switch (status) {
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_IDLE:
      current_binding_status_.current_step = "空闲";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_WAITING:
      current_binding_status_.current_step = "等待连接";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONNECTING:
      current_binding_status_.current_step = "正在连接";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONFIGURING:
      current_binding_status_.current_step = "正在配置";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_REGISTERING:
      current_binding_status_.current_step = "正在注册";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_SUCCESS:
      current_binding_status_.current_step = "绑定成功";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_FAILED:
      current_binding_status_.current_step = "绑定失败";
      break;
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_TIMEOUT:
      current_binding_status_.current_step = "绑定超时";
      break;
    default:
      current_binding_status_.current_step = "未知状态";
      break;
  }
}

void BindingManager::publish_binding_status()
{
  std::lock_guard<std::mutex> lock(binding_mutex_);
  current_binding_status_.header.stamp = node_->now();
  binding_status_pub_->publish(current_binding_status_);
}

std::string BindingManager::generate_session_id()
{
  // 使用UUID生成会话ID
  uuid_t uuid;
  char uuid_str[37];
  
  uuid_generate(uuid);
  uuid_unparse_lower(uuid, uuid_str);
  
  return std::string(uuid_str);
}

}  // namespace gen3_network_manager_core 