// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/dns_manager.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <vector>
#include <fstream>
#include <iostream>

using namespace std::chrono_literals;

namespace gen3_network_manager_core
{

DNSManager::DNSManager(const rclcpp::Node::SharedPtr & node)
: node_(node), logger_(rclcpp::get_logger("dns_manager")),
  dns_switch_count_(0),
  last_dns_check_time_(std::chrono::steady_clock::now())
{
  RCLCPP_INFO(logger_, "创建智能DNS管理器");

  // 声明参数
  node_->declare_parameter("dns_check_interval", 30.0);
  node_->declare_parameter("dns_test_timeout_ms", 3000);
  node_->declare_parameter("dns_max_consecutive_failures", 3);
  node_->declare_parameter("dns_priority_update_threshold", 0.8);

  // 声明DNS服务器配置参数
  node_->declare_parameter("dns_servers.primary", std::vector<std::string>{"8.8.8.8", "1.1.1.1"});
  node_->declare_parameter("dns_servers.secondary", std::vector<std::string>{"114.114.114.114", "223.5.5.5"});
  node_->declare_parameter("dns_servers.backup", std::vector<std::string>{"208.67.222.222", "9.9.9.9"});

  // 声明中国网络环境相关参数
  node_->declare_parameter("dns_servers.china_primary", std::vector<std::string>{"114.114.114.114", "223.5.5.5"});
  node_->declare_parameter("dns_servers.china_secondary", std::vector<std::string>{"119.29.29.29", "180.76.76.76"});
  node_->declare_parameter("dns_servers.china_backup", std::vector<std::string>{"1.2.4.8", "210.2.4.8"});
  node_->declare_parameter("dns_servers.international", std::vector<std::string>{"8.8.8.8", "1.1.1.1", "208.67.222.222"});

  // 中国网络检测参数
  node_->declare_parameter("china_network.enable_detection", true);
  node_->declare_parameter("china_network.detection_interval", 300.0);  // 5分钟检测一次
  node_->declare_parameter("china_network.prefer_domestic_dns", true);
  node_->declare_parameter("china_network.domestic_test_sites", std::vector<std::string>{"www.baidu.com", "www.qq.com", "www.taobao.com"});
}

DNSManager::~DNSManager()
{
  RCLCPP_INFO(logger_, "销毁DNS管理器");
}

bool DNSManager::init()
{
  RCLCPP_INFO(logger_, "初始化智能DNS管理器");

  try {
    // 检测网络环境（中国网络环境检测）
    detect_network_environment();

    // 根据网络环境加载DNS服务器配置
    if (network_env_.is_in_china) {
      load_china_dns_config();
    } else {
      load_dns_server_config();
    }

    // 加载原有DNS配置
    load_dns_config();

    // 创建定时器
    double dns_check_interval = node_->get_parameter("dns_check_interval").as_double();

    dns_check_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(dns_check_interval),
      std::bind(&DNSManager::dns_check_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建智能DNS检查定时器，间隔: %.1f秒", dns_check_interval);

    // 执行初始智能DNS检查
    RCLCPP_DEBUG(logger_, "[TIMER] 执行初始智能DNS检查");
    intelligent_dns_check();

    RCLCPP_INFO(logger_, "智能DNS管理器初始化完成，配置了 %zu 个DNS服务器",
                configured_dns_servers_.size());
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "DNS管理器初始化失败: %s", e.what());
    return false;
  }
}

void DNSManager::dns_check_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 智能DNS检查定时器触发");

  // 执行智能DNS检查
  intelligent_dns_check();

  RCLCPP_DEBUG(logger_, "[TIMER] 智能DNS检查定时器处理完成");
}

bool DNSManager::set_dns_servers(const std::vector<std::string> & dns_servers)
{
  RCLCPP_INFO(logger_, "[DNS] 设置DNS服务器，共 %zu 个服务器", dns_servers.size());

  // 打印所有DNS服务器
  for (size_t i = 0; i < dns_servers.size(); ++i) {
    RCLCPP_DEBUG(logger_, "[DNS] DNS服务器 %zu: %s", i + 1, dns_servers[i].c_str());
  }

  try {
    // 备份当前DNS配置
    RCLCPP_DEBUG(logger_, "[DNS] 备份当前DNS配置");
    backup_dns_config();

    // 更新DNS配置
    std::string resolv_conf_path = "/etc/resolv.conf";
    RCLCPP_DEBUG(logger_, "[DNS] 写入DNS配置文件: %s", resolv_conf_path.c_str());
    std::ofstream file(resolv_conf_path);

    if (!file.is_open()) {
      RCLCPP_ERROR(logger_, "[DNS] 无法打开DNS配置文件: %s", resolv_conf_path.c_str());
      return false;
    }

    // 写入文件头
    file << "# Generated by Gen3 Network Manager\n";
    file << "# " << node_->now().seconds() << "\n\n";

    // 写入域名搜索配置
    file << "search localdomain\n";

    // 写入DNS服务器
    for (const auto & dns : dns_servers) {
      file << "nameserver " << dns << "\n";
      RCLCPP_DEBUG(logger_, "[DNS] 写入DNS服务器: %s", dns.c_str());
    }

    file.close();
    RCLCPP_DEBUG(logger_, "[DNS] DNS配置文件写入完成");
    
    // 更新当前DNS服务器列表
    current_dns_servers_ = dns_servers;
    
    RCLCPP_INFO(logger_, "DNS服务器设置成功");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "设置DNS服务器异常: %s", e.what());
    restore_dns_config();
    return false;
  }
}

bool DNSManager::add_dns_server(const std::string & dns_server)
{
  RCLCPP_INFO(logger_, "[DNS] 添加DNS服务器: %s", dns_server.c_str());

  try {
    // 检查DNS服务器是否已存在
    auto it = std::find(current_dns_servers_.begin(), current_dns_servers_.end(), dns_server);
    if (it != current_dns_servers_.end()) {
      RCLCPP_WARN(logger_, "[DNS] DNS服务器已存在: %s", dns_server.c_str());
      return true;
    }

    RCLCPP_DEBUG(logger_, "[DNS] 当前DNS服务器数量: %zu", current_dns_servers_.size());

    // 添加DNS服务器
    std::vector<std::string> new_dns_servers = current_dns_servers_;
    new_dns_servers.push_back(dns_server);

    RCLCPP_DEBUG(logger_, "[DNS] 新DNS服务器列表数量: %zu", new_dns_servers.size());

    // 设置DNS服务器
    bool result = set_dns_servers(new_dns_servers);

    if (result) {
      RCLCPP_INFO(logger_, "[DNS] DNS服务器添加成功: %s", dns_server.c_str());
    } else {
      RCLCPP_ERROR(logger_, "[DNS] DNS服务器添加失败: %s", dns_server.c_str());
    }

    return result;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[DNS] 添加DNS服务器异常: %s", e.what());
    return false;
  }
}

bool DNSManager::remove_dns_server(const std::string & dns_server)
{
  RCLCPP_INFO(logger_, "[DNS] 移除DNS服务器: %s", dns_server.c_str());

  try {
    // 检查DNS服务器是否存在
    auto it = std::find(current_dns_servers_.begin(), current_dns_servers_.end(), dns_server);
    if (it == current_dns_servers_.end()) {
      RCLCPP_WARN(logger_, "[DNS] DNS服务器不存在: %s", dns_server.c_str());
      return true;
    }

    RCLCPP_DEBUG(logger_, "[DNS] 当前DNS服务器数量: %zu", current_dns_servers_.size());

    // 移除DNS服务器
    std::vector<std::string> new_dns_servers = current_dns_servers_;
    new_dns_servers.erase(std::remove(new_dns_servers.begin(), new_dns_servers.end(), dns_server),
      new_dns_servers.end());

    RCLCPP_DEBUG(logger_, "[DNS] 移除后DNS服务器数量: %zu", new_dns_servers.size());

    // 设置DNS服务器
    bool result = set_dns_servers(new_dns_servers);

    if (result) {
      RCLCPP_INFO(logger_, "[DNS] DNS服务器移除成功: %s", dns_server.c_str());
    } else {
      RCLCPP_ERROR(logger_, "[DNS] DNS服务器移除失败: %s", dns_server.c_str());
    }

    return result;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[DNS] 移除DNS服务器异常: %s", e.what());
    return false;
  }
}

bool DNSManager::set_default_dns_servers()
{
  RCLCPP_INFO(logger_, "设置默认DNS服务器");
  
  try {
    // 默认DNS服务器
    std::vector<std::string> default_dns = {"8.8.8.8", "114.114.114.114"};
    
    // 设置DNS服务器
    bool result = set_dns_servers(default_dns);
    
    if (result) {
      RCLCPP_INFO(logger_, "默认DNS服务器设置成功");
    }
    
    return result;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "设置默认DNS服务器异常: %s", e.what());
    return false;
  }
}

// 旧的check_dns_config函数已删除，现在使用intelligent_dns_check()替代

std::vector<std::string> DNSManager::get_current_dns_servers()
{
  RCLCPP_DEBUG(logger_, "获取当前DNS服务器");
  
  std::vector<std::string> dns_servers;
  
  try {
    // 读取resolv.conf文件
    std::string resolv_conf_path = "/etc/resolv.conf";
    std::ifstream file(resolv_conf_path);
    
    if (!file.is_open()) {
      RCLCPP_ERROR(logger_, "无法打开DNS配置文件: %s", resolv_conf_path.c_str());
      return dns_servers;
    }
    
    std::string line;
    
    while (std::getline(file, line)) {
      // 跳过空行和注释
      if (line.empty() || line[0] == '#') {
        continue;
      }
      
      // 查找nameserver行
      if (line.find("nameserver") == 0) {
        // 提取DNS服务器地址
        std::string dns_server = line.substr(11);
        
        // 去除首尾空格
        dns_server.erase(0, dns_server.find_first_not_of(" \t"));
        dns_server.erase(dns_server.find_last_not_of(" \t") + 1);
        
        // 添加到列表
        dns_servers.push_back(dns_server);
      }
    }
    
    file.close();
    
    // 更新当前DNS服务器列表
    current_dns_servers_ = dns_servers;
    
    RCLCPP_DEBUG(logger_, "获取到%zu个DNS服务器", dns_servers.size());
    return dns_servers;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "获取DNS服务器异常: %s", e.what());
    return dns_servers;
  }
}

void DNSManager::load_dns_config()
{
  RCLCPP_INFO(logger_, "加载DNS配置");
  
  // 获取当前DNS服务器
  current_dns_servers_ = get_current_dns_servers();
  
  // 如果没有DNS服务器，设置默认DNS
  if (current_dns_servers_.empty()) {
    RCLCPP_WARN(logger_, "未找到DNS服务器，设置默认DNS");
    set_default_dns_servers();
  }
}

bool DNSManager::backup_dns_config()
{
  RCLCPP_INFO(logger_, "备份DNS配置");
  
  try {
    // 源文件路径
    std::string resolv_conf_path = "/etc/resolv.conf";
    
    // 备份文件路径
    std::string backup_path = "/etc/resolv.conf.bak";
    
    // 执行备份命令
    std::string backup_command = "cp " + resolv_conf_path + " " + backup_path;
    int result = system(backup_command.c_str());
    
    if (result == 0) {
      RCLCPP_INFO(logger_, "DNS配置备份成功");
      return true;
    } else {
      RCLCPP_ERROR(logger_, "DNS配置备份失败: 命令返回错误码 %d", result);
      return false;
    }
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "备份DNS配置异常: %s", e.what());
    return false;
  }
}

bool DNSManager::restore_dns_config()
{
  RCLCPP_INFO(logger_, "恢复DNS配置");
  
  try {
    // 备份文件路径
    std::string backup_path = "/etc/resolv.conf.bak";
    
    // 目标文件路径
    std::string resolv_conf_path = "/etc/resolv.conf";
    
    // 检查备份文件是否存在
    std::ifstream backup_file(backup_path);
    if (!backup_file.good()) {
      RCLCPP_ERROR(logger_, "DNS配置备份文件不存在: %s", backup_path.c_str());
      return false;
    }
    backup_file.close();
    
    // 执行恢复命令
    std::string restore_command = "cp " + backup_path + " " + resolv_conf_path;
    int result = system(restore_command.c_str());
    
    if (result == 0) {
      RCLCPP_INFO(logger_, "DNS配置恢复成功");
      
      // 重新加载DNS配置
      load_dns_config();
      
      return true;
    } else {
      RCLCPP_ERROR(logger_, "DNS配置恢复失败: 命令返回错误码 %d", result);
      return false;
    }
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "恢复DNS配置异常: %s", e.what());
    return false;
  }
}

// === 智能DNS管理实现 ===

void DNSManager::load_dns_server_config()
{
  RCLCPP_INFO(logger_, "[SMART_DNS] 加载DNS服务器配置");

  try {
    configured_dns_servers_.clear();

    // 加载主要DNS服务器（优先级1-10）
    auto primary_dns = node_->get_parameter("dns_servers.primary").as_string_array();
    for (size_t i = 0; i < primary_dns.size(); ++i) {
      DNSServerInfo info;
      info.address = primary_dns[i];
      info.priority = static_cast<int>(i + 1);  // 优先级1-10
      info.success_rate = 1.0;  // 初始成功率100%
      info.consecutive_failures = 0;
      info.last_test_time = std::chrono::steady_clock::now();
      info.avg_response_time = std::chrono::milliseconds(50);
      info.is_available = true;
      configured_dns_servers_.push_back(info);
    }

    // 加载备用DNS服务器（优先级11-20）
    auto secondary_dns = node_->get_parameter("dns_servers.secondary").as_string_array();
    for (size_t i = 0; i < secondary_dns.size(); ++i) {
      DNSServerInfo info;
      info.address = secondary_dns[i];
      info.priority = static_cast<int>(i + 11);  // 优先级11-20
      info.success_rate = 0.9;  // 初始成功率90%
      info.consecutive_failures = 0;
      info.last_test_time = std::chrono::steady_clock::now();
      info.avg_response_time = std::chrono::milliseconds(100);
      info.is_available = true;
      configured_dns_servers_.push_back(info);
    }

    // 加载备份DNS服务器（优先级21-30）
    auto backup_dns = node_->get_parameter("dns_servers.backup").as_string_array();
    for (size_t i = 0; i < backup_dns.size(); ++i) {
      DNSServerInfo info;
      info.address = backup_dns[i];
      info.priority = static_cast<int>(i + 21);  // 优先级21-30
      info.success_rate = 0.8;  // 初始成功率80%
      info.consecutive_failures = 0;
      info.last_test_time = std::chrono::steady_clock::now();
      info.avg_response_time = std::chrono::milliseconds(150);
      info.is_available = true;
      configured_dns_servers_.push_back(info);
    }

    RCLCPP_INFO(logger_, "[SMART_DNS] 已加载 %zu 个DNS服务器配置", configured_dns_servers_.size());

    // 显示配置的DNS服务器
    for (const auto& dns : configured_dns_servers_) {
      RCLCPP_DEBUG(logger_, "[SMART_DNS] DNS服务器: %s, 优先级: %d, 成功率: %.1f%%",
                   dns.address.c_str(), dns.priority, dns.success_rate * 100.0);
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 加载DNS服务器配置异常: %s", e.what());
  }
}

void DNSManager::intelligent_dns_check()
{
  // 防止重复执行
  if (dns_check_in_progress_.load()) {
    RCLCPP_DEBUG(logger_, "[SMART_DNS] DNS检查正在进行中，跳过本次检查");
    return;
  }

  dns_check_in_progress_.store(true);

  RCLCPP_DEBUG(logger_, "[SMART_DNS] 开始智能DNS检查");

  try {
    // 检查当前DNS解析是否正常工作
    bool dns_working = is_dns_resolution_working();

    if (dns_working) {
      RCLCPP_DEBUG(logger_, "[SMART_DNS] 当前DNS解析正常工作");

      // 更新当前DNS服务器的统计信息
      auto current_dns = get_current_dns_servers();
      for (const auto& dns : current_dns) {
        std::chrono::milliseconds response_time;
        bool test_result = test_dns_server(dns, response_time);
        update_dns_server_stats(dns, test_result, response_time);
      }

    } else {
      RCLCPP_WARN(logger_, "[SMART_DNS] DNS解析异常，开始切换到最佳DNS服务器");

      // DNS解析异常，切换到最佳DNS服务器
      bool switch_success = switch_to_best_dns_server();

      if (switch_success) {
        RCLCPP_INFO(logger_, "[SMART_DNS] 成功切换到最佳DNS服务器");
        dns_switch_count_++;
      } else {
        RCLCPP_ERROR(logger_, "[SMART_DNS] 切换到最佳DNS服务器失败");
      }
    }

    // 定期更新DNS服务器优先级
    auto now = std::chrono::steady_clock::now();
    auto time_since_last_check = std::chrono::duration_cast<std::chrono::minutes>(
      now - last_dns_check_time_).count();

    if (time_since_last_check >= 10) {  // 每10分钟更新一次优先级
      update_dns_priorities();
      last_dns_check_time_ = now;
    }

    // 保存统计信息
    save_dns_server_stats();

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 智能DNS检查异常: %s", e.what());
  }

  dns_check_in_progress_.store(false);
  RCLCPP_DEBUG(logger_, "[SMART_DNS] 智能DNS检查完成");
}

bool DNSManager::test_dns_server(const std::string & dns_server, std::chrono::milliseconds & response_time)
{
  RCLCPP_DEBUG(logger_, "[SMART_DNS] 测试DNS服务器: %s", dns_server.c_str());

  try {
    auto start_time = std::chrono::steady_clock::now();

    // 使用nslookup测试DNS服务器
    std::string test_command = "timeout 3 nslookup www.google.com " + dns_server + " > /dev/null 2>&1";
    int result = system(test_command.c_str());

    auto end_time = std::chrono::steady_clock::now();
    response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    bool success = (result == 0);

    RCLCPP_DEBUG(logger_, "[SMART_DNS] DNS服务器 %s 测试结果: %s, 响应时间: %ldms",
                 dns_server.c_str(), success ? "成功" : "失败", response_time.count());

    return success;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 测试DNS服务器异常: %s", e.what());
    response_time = std::chrono::milliseconds(5000);  // 超时时间
    return false;
  }
}

void DNSManager::update_dns_server_stats(const std::string & dns_server, bool success, std::chrono::milliseconds response_time)
{
  std::lock_guard<std::mutex> lock(dns_mutex_);

  // 查找对应的DNS服务器信息
  for (auto& dns : configured_dns_servers_) {
    if (dns.address == dns_server) {
      dns.last_test_time = std::chrono::steady_clock::now();

      if (success) {
        // 成功：重置连续失败次数，更新成功率和响应时间
        dns.consecutive_failures = 0;
        dns.is_available = true;

        // 使用指数移动平均更新成功率
        dns.success_rate = dns.success_rate * 0.9 + 0.1;

        // 更新平均响应时间
        dns.avg_response_time = std::chrono::milliseconds(
          (dns.avg_response_time.count() + response_time.count()) / 2);

      } else {
        // 失败：增加连续失败次数，降低成功率
        dns.consecutive_failures++;
        dns.success_rate = dns.success_rate * 0.9;  // 降低成功率

        // 连续失败超过阈值，标记为不可用
        int max_failures = node_->get_parameter("dns_max_consecutive_failures").as_int();
        if (dns.consecutive_failures >= max_failures) {
          dns.is_available = false;
          RCLCPP_WARN(logger_, "[SMART_DNS] DNS服务器 %s 连续失败 %d 次，标记为不可用",
                      dns_server.c_str(), dns.consecutive_failures);
        }
      }

      RCLCPP_DEBUG(logger_, "[SMART_DNS] 更新DNS服务器 %s 统计 - 成功率: %.1f%%, 连续失败: %d, 可用: %s",
                   dns_server.c_str(), dns.success_rate * 100.0, dns.consecutive_failures,
                   dns.is_available ? "是" : "否");
      break;
    }
  }
}

std::vector<DNSManager::DNSServerInfo> DNSManager::get_sorted_dns_servers()
{
  std::lock_guard<std::mutex> lock(dns_mutex_);

  auto sorted_dns = configured_dns_servers_;

  // 按优先级和可用性排序
  std::sort(sorted_dns.begin(), sorted_dns.end(),
    [](const DNSServerInfo& a, const DNSServerInfo& b) {
      // 首先按可用性排序
      if (a.is_available != b.is_available) {
        return a.is_available > b.is_available;
      }

      // 然后按成功率排序
      if (std::abs(a.success_rate - b.success_rate) > 0.1) {
        return a.success_rate > b.success_rate;
      }

      // 最后按优先级排序
      return a.priority < b.priority;
    });

  return sorted_dns;
}

bool DNSManager::switch_to_best_dns_server()
{
  RCLCPP_INFO(logger_, "[SMART_DNS] 开始切换到最佳DNS服务器");

  try {
    // 根据网络环境获取优化的DNS服务器列表
    auto sorted_dns = network_env_.is_in_china ?
                      get_china_optimized_dns_servers() :
                      get_sorted_dns_servers();

    // 逐个测试DNS服务器，直到找到可用的
    for (const auto& dns : sorted_dns) {
      if (!dns.is_available) {
        continue;  // 跳过不可用的服务器
      }

      RCLCPP_INFO(logger_, "[SMART_DNS] 尝试切换到DNS服务器: %s (优先级: %d, 成功率: %.1f%%)",
                  dns.address.c_str(), dns.priority, dns.success_rate * 100.0);

      // 测试DNS服务器
      std::chrono::milliseconds response_time;
      bool test_result = test_dns_server(dns.address, response_time);

      if (test_result) {
        // 测试成功，切换到这个DNS服务器
        std::vector<std::string> new_dns_servers = {dns.address};

        // 添加一个备用DNS服务器
        for (const auto& backup_dns : sorted_dns) {
          if (backup_dns.address != dns.address && backup_dns.is_available) {
            new_dns_servers.push_back(backup_dns.address);
            break;
          }
        }

        bool switch_result = set_dns_servers(new_dns_servers);

        if (switch_result) {
          RCLCPP_INFO(logger_, "[SMART_DNS] 成功切换到DNS服务器: %s", dns.address.c_str());

          // 更新统计信息
          update_dns_server_stats(dns.address, true, response_time);

          return true;
        } else {
          RCLCPP_ERROR(logger_, "[SMART_DNS] 切换到DNS服务器 %s 失败", dns.address.c_str());
        }
      } else {
        RCLCPP_WARN(logger_, "[SMART_DNS] DNS服务器 %s 测试失败，尝试下一个", dns.address.c_str());

        // 更新统计信息
        update_dns_server_stats(dns.address, false, response_time);
      }
    }

    RCLCPP_ERROR(logger_, "[SMART_DNS] 所有DNS服务器都不可用");
    return false;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 切换到最佳DNS服务器异常: %s", e.what());
    return false;
  }
}

bool DNSManager::is_dns_resolution_working()
{
  RCLCPP_DEBUG(logger_, "[SMART_DNS] 检查DNS解析是否正常工作");

  try {
    // 根据网络环境选择测试域名
    std::vector<std::string> test_domains;

    if (network_env_.is_in_china) {
      // 在中国环境下，优先测试国内网站
      test_domains = {"www.baidu.com", "www.qq.com", "www.163.com"};

      // 如果可以访问国际网站，也测试一些国际网站
      if (network_env_.can_access_international) {
        test_domains.push_back("www.google.com");
        test_domains.push_back("github.com");
      }
    } else {
      // 国际环境下，测试国际网站
      test_domains = {"www.google.com", "github.com", "www.cloudflare.com"};
    }

    int success_count = 0;
    for (const auto& domain : test_domains) {
      std::string test_command = "timeout 3 nslookup " + domain + " > /dev/null 2>&1";
      int result = system(test_command.c_str());

      if (result == 0) {
        success_count++;
        RCLCPP_DEBUG(logger_, "[SMART_DNS] 成功解析域名: %s", domain.c_str());
      } else {
        RCLCPP_DEBUG(logger_, "[SMART_DNS] 解析域名失败: %s", domain.c_str());
      }
    }

    // 如果至少有一半的域名解析成功，认为DNS工作正常
    bool working = (success_count >= static_cast<int>(test_domains.size()) / 2);

    RCLCPP_DEBUG(logger_, "[SMART_DNS] DNS解析测试结果: %d/%zu 成功, 状态: %s (环境: %s)",
                 success_count, test_domains.size(), working ? "正常" : "异常",
                 network_env_.detected_region.c_str());

    return working;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 检查DNS解析异常: %s", e.what());
    return false;
  }
}

void DNSManager::update_dns_priorities()
{
  RCLCPP_DEBUG(logger_, "[SMART_DNS] 更新DNS服务器优先级");

  std::lock_guard<std::mutex> lock(dns_mutex_);

  try {
    double threshold = node_->get_parameter("dns_priority_update_threshold").as_double();

    // 根据成功率和响应时间调整优先级
    for (auto& dns : configured_dns_servers_) {
      int old_priority = dns.priority;

      // 成功率高的服务器提升优先级
      if (dns.success_rate > threshold && dns.is_available) {
        dns.priority = std::max(1, dns.priority - 1);  // 提升优先级（数值减小）
      }
      // 成功率低的服务器降低优先级
      else if (dns.success_rate < (threshold - 0.2) || !dns.is_available) {
        dns.priority = std::min(30, dns.priority + 1);  // 降低优先级（数值增大）
      }

      if (old_priority != dns.priority) {
        RCLCPP_INFO(logger_, "[SMART_DNS] DNS服务器 %s 优先级调整: %d -> %d (成功率: %.1f%%)",
                    dns.address.c_str(), old_priority, dns.priority, dns.success_rate * 100.0);
      }
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SMART_DNS] 更新DNS优先级异常: %s", e.what());
  }
}

void DNSManager::save_dns_server_stats()
{
  // 这里可以实现将DNS服务器统计信息保存到文件的逻辑
  // 暂时只记录日志
  RCLCPP_DEBUG(logger_, "[SMART_DNS] DNS服务器统计信息已更新，切换次数: %d", dns_switch_count_);
}

// === 中国网络环境检测实现 ===

void DNSManager::detect_network_environment()
{
  RCLCPP_INFO(logger_, "[CHINA_NET] 开始检测网络环境");

  try {
    // 检查是否启用中国网络检测
    bool enable_detection = node_->get_parameter("china_network.enable_detection").as_bool();
    if (!enable_detection) {
      RCLCPP_INFO(logger_, "[CHINA_NET] 中国网络检测已禁用，使用默认配置");
      network_env_.is_in_china = false;
      network_env_.can_access_international = true;
      network_env_.prefer_domestic_dns = false;
      network_env_.detected_region = "international";
      return;
    }

    // 检测是否在中国大陆
    bool in_china = is_in_china_mainland();

    // 测试国内网络连通性
    bool domestic_ok = test_domestic_connectivity();

    // 更新网络环境信息（简化为仅国内）
    network_env_.is_in_china = in_china;
    network_env_.can_access_international = false;  // 不再测试国际访问
    network_env_.prefer_domestic_dns = true;        // 始终优先国内DNS
    network_env_.detected_region = "china_domestic_only";

    last_env_check_time_ = std::chrono::steady_clock::now();

    RCLCPP_INFO(logger_, "[CHINA_NET] 网络环境检测完成:");
    RCLCPP_INFO(logger_, "[CHINA_NET]   - 在中国境内: %s", in_china ? "是" : "否");
    RCLCPP_INFO(logger_, "[CHINA_NET]   - 国内网络连通: %s", domestic_ok ? "是" : "否");
    RCLCPP_INFO(logger_, "[CHINA_NET]   - 优先国内DNS: %s", network_env_.prefer_domestic_dns ? "是" : "否");
    RCLCPP_INFO(logger_, "[CHINA_NET]   - 检测地区: %s", network_env_.detected_region.c_str());

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[CHINA_NET] 网络环境检测异常: %s", e.what());
    // 异常时使用保守配置
    network_env_.is_in_china = true;
    network_env_.can_access_international = false;
    network_env_.prefer_domestic_dns = true;
    network_env_.detected_region = "unknown_assume_china";
  }
}

bool DNSManager::is_in_china_mainland()
{
  RCLCPP_DEBUG(logger_, "[CHINA_NET] 检测是否在中国大陆");

  try {
    // 方法1: 尝试访问中国大陆特有的网站
    std::vector<std::string> china_sites = {"www.baidu.com", "www.qq.com", "www.163.com"};
    int china_success = 0;

    for (const auto& site : china_sites) {
      std::string test_cmd = "timeout 5 curl -s --connect-timeout 3 " + site + " > /dev/null 2>&1";
      if (system(test_cmd.c_str()) == 0) {
        china_success++;
      }
    }

    // 方法2: 检查时区
    bool timezone_china = false;
    std::string tz_cmd = "timedatectl show --property=Timezone --value 2>/dev/null | grep -i 'asia/shanghai\\|asia/beijing\\|asia/chongqing' > /dev/null";
    if (system(tz_cmd.c_str()) == 0) {
      timezone_china = true;
    }

    // 方法3: 检查本地化设置
    bool locale_china = false;
    std::string locale_cmd = "locale | grep -i 'zh_CN\\|chinese' > /dev/null 2>&1";
    if (system(locale_cmd.c_str()) == 0) {
      locale_china = true;
    }

    // 综合判断
    bool in_china = (china_success >= 2) || timezone_china || locale_china;

    RCLCPP_DEBUG(logger_, "[CHINA_NET] 中国大陆检测结果: 网站访问 %d/3, 时区 %s, 本地化 %s, 结论: %s",
                 china_success, timezone_china ? "中国" : "其他",
                 locale_china ? "中文" : "其他", in_china ? "在中国" : "不在中国");

    return in_china;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[CHINA_NET] 检测中国大陆异常: %s", e.what());
    return true;  // 异常时假设在中国
  }
}

// 移除国际网站访问测试功能 - 仅保留国内网络测试

bool DNSManager::test_domestic_connectivity()
{
  RCLCPP_DEBUG(logger_, "[CHINA_NET] 测试国内网络连通性");

  try {
    auto domestic_sites = node_->get_parameter("china_network.domestic_test_sites").as_string_array();

    int success_count = 0;
    for (const auto& site : domestic_sites) {
      std::string test_cmd = "timeout 5 curl -s --connect-timeout 3 " + site + " > /dev/null 2>&1";
      if (system(test_cmd.c_str()) == 0) {
        success_count++;
        RCLCPP_DEBUG(logger_, "[CHINA_NET] 成功访问国内网站: %s", site.c_str());
      }
    }

    bool domestic_ok = success_count >= static_cast<int>(domestic_sites.size()) / 2;

    RCLCPP_DEBUG(logger_, "[CHINA_NET] 国内网络连通性测试: %d/%zu 成功, 结论: %s",
                 success_count, domestic_sites.size(), domestic_ok ? "正常" : "异常");

    return domestic_ok;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[CHINA_NET] 测试国内网络连通性异常: %s", e.what());
    return false;
  }
}

// 移除国际网络连通性测试功能

void DNSManager::load_china_dns_config()
{
  RCLCPP_INFO(logger_, "[CHINA_DNS] 加载中国优化DNS服务器配置");

  try {
    configured_dns_servers_.clear();

    // 根据网络环境选择DNS配置策略
    if (network_env_.prefer_domestic_dns) {
      RCLCPP_INFO(logger_, "[CHINA_DNS] 使用国内优先DNS配置策略");

      // 加载中国主要DNS服务器（优先级1-10）
      auto china_primary = node_->get_parameter("dns_servers.china_primary").as_string_array();
      for (size_t i = 0; i < china_primary.size(); ++i) {
        DNSServerInfo info;
        info.address = china_primary[i];
        info.priority = static_cast<int>(i + 1);
        info.success_rate = 0.95;  // 国内DNS通常成功率较高
        info.consecutive_failures = 0;
        info.last_test_time = std::chrono::steady_clock::now();
        info.avg_response_time = std::chrono::milliseconds(30);  // 国内DNS响应较快
        info.is_available = true;
        info.region = "china";
        info.is_domestic = true;
        configured_dns_servers_.push_back(info);
      }

      // 加载中国备用DNS服务器（优先级11-20）
      auto china_secondary = node_->get_parameter("dns_servers.china_secondary").as_string_array();
      for (size_t i = 0; i < china_secondary.size(); ++i) {
        DNSServerInfo info;
        info.address = china_secondary[i];
        info.priority = static_cast<int>(i + 11);
        info.success_rate = 0.90;
        info.consecutive_failures = 0;
        info.last_test_time = std::chrono::steady_clock::now();
        info.avg_response_time = std::chrono::milliseconds(50);
        info.is_available = true;
        info.region = "china";
        info.is_domestic = true;
        configured_dns_servers_.push_back(info);
      }

      // 如果可以访问国际网站，添加国际DNS作为备份（优先级21-30）
      if (network_env_.can_access_international) {
        auto international_dns = node_->get_parameter("dns_servers.international").as_string_array();
        for (size_t i = 0; i < international_dns.size(); ++i) {
          DNSServerInfo info;
          info.address = international_dns[i];
          info.priority = static_cast<int>(i + 21);
          info.success_rate = 0.85;  // 国际DNS在中国可能成功率稍低
          info.consecutive_failures = 0;
          info.last_test_time = std::chrono::steady_clock::now();
          info.avg_response_time = std::chrono::milliseconds(100);
          info.is_available = true;
          info.region = "international";
          info.is_domestic = false;
          configured_dns_servers_.push_back(info);
        }
      }

      // 加载中国备份DNS服务器（优先级31-40）
      auto china_backup = node_->get_parameter("dns_servers.china_backup").as_string_array();
      for (size_t i = 0; i < china_backup.size(); ++i) {
        DNSServerInfo info;
        info.address = china_backup[i];
        info.priority = static_cast<int>(i + 31);
        info.success_rate = 0.80;
        info.consecutive_failures = 0;
        info.last_test_time = std::chrono::steady_clock::now();
        info.avg_response_time = std::chrono::milliseconds(80);
        info.is_available = true;
        info.region = "china";
        info.is_domestic = true;
        configured_dns_servers_.push_back(info);
      }

    } else {
      RCLCPP_INFO(logger_, "[CHINA_DNS] 使用国际优先DNS配置策略");

      // 国际优先策略：先加载国际DNS，再加载国内DNS作为备份
      auto international_dns = node_->get_parameter("dns_servers.international").as_string_array();
      for (size_t i = 0; i < international_dns.size(); ++i) {
        DNSServerInfo info;
        info.address = international_dns[i];
        info.priority = static_cast<int>(i + 1);
        info.success_rate = 0.90;
        info.consecutive_failures = 0;
        info.last_test_time = std::chrono::steady_clock::now();
        info.avg_response_time = std::chrono::milliseconds(80);
        info.is_available = true;
        info.region = "international";
        info.is_domestic = false;
        configured_dns_servers_.push_back(info);
      }

      // 添加国内DNS作为备份
      auto china_primary = node_->get_parameter("dns_servers.china_primary").as_string_array();
      for (size_t i = 0; i < china_primary.size(); ++i) {
        DNSServerInfo info;
        info.address = china_primary[i];
        info.priority = static_cast<int>(i + 11);
        info.success_rate = 0.85;
        info.consecutive_failures = 0;
        info.last_test_time = std::chrono::steady_clock::now();
        info.avg_response_time = std::chrono::milliseconds(40);
        info.is_available = true;
        info.region = "china";
        info.is_domestic = true;
        configured_dns_servers_.push_back(info);
      }
    }

    RCLCPP_INFO(logger_, "[CHINA_DNS] 已加载 %zu 个中国优化DNS服务器配置", configured_dns_servers_.size());

    // 显示配置的DNS服务器
    for (const auto& dns : configured_dns_servers_) {
      RCLCPP_INFO(logger_, "[CHINA_DNS] DNS服务器: %s, 优先级: %d, 地区: %s, 国内: %s",
                   dns.address.c_str(), dns.priority, dns.region.c_str(),
                   dns.is_domestic ? "是" : "否");
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[CHINA_DNS] 加载中国DNS配置异常: %s", e.what());
    // 异常时回退到标准配置
    load_dns_server_config();
  }
}

std::vector<DNSManager::DNSServerInfo> DNSManager::get_china_optimized_dns_servers()
{
  std::lock_guard<std::mutex> lock(dns_mutex_);

  auto sorted_dns = configured_dns_servers_;

  // 中国网络环境下的特殊排序逻辑
  std::sort(sorted_dns.begin(), sorted_dns.end(),
    [this](const DNSServerInfo& a, const DNSServerInfo& b) {
      // 首先按可用性排序
      if (a.is_available != b.is_available) {
        return a.is_available > b.is_available;
      }

      // 在中国环境下，优先考虑国内DNS
      if (network_env_.prefer_domestic_dns) {
        if (a.is_domestic != b.is_domestic) {
          return a.is_domestic > b.is_domestic;
        }
      }

      // 然后按成功率排序
      if (std::abs(a.success_rate - b.success_rate) > 0.1) {
        return a.success_rate > b.success_rate;
      }

      // 最后按优先级排序
      return a.priority < b.priority;
    });

  return sorted_dns;
}

}  // namespace gen3_network_manager_core