// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/network_manager.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"
#include "gen3_network_manager_core/utils/enum_utils.hpp"
#include "gen3_network_manager_core/utils/iptables_utils.hpp"
#include "gen3_network_manager_core/cellular_manager.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <thread>
#include <sstream>
#include <vector>
#include <fstream>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace gen3_network_manager_core
{

NetworkManager::NetworkManager(const rclcpp::Node::SharedPtr & node)
: node_(node), logger_(rclcpp::get_logger("network_manager")),
  last_switch_time_(std::chrono::steady_clock::now() - std::chrono::minutes(1))  // 初始化为1分钟前，避免启动时的切换限制
{
  RCLCPP_INFO(logger_, "创建网络管理器");
  
  // 声明参数
  node_->declare_parameter("status_update_interval", 5.0);
  node_->declare_parameter("wifi_interface", "wlan0");
  node_->declare_parameter("ethernet_interface", "eth0");
  // 声明5G网卡候选列表参数
  node_->declare_parameter("5g_interfaces", std::vector<std::string>{"eth2", "usb0", "wwan0", "enp0s20f0u3", "usb5g0"});
}

NetworkManager::~NetworkManager()
{
  RCLCPP_INFO(logger_, "销毁网络管理器");
}

bool NetworkManager::init()
{
  RCLCPP_INFO(logger_, "初始化网络管理器");

  try {
    // 声明配置参数
    node_->declare_parameter("enable_auto_switch", true);
    node_->declare_parameter("preferred_network_type", 1);
    node_->declare_parameter("min_signal_strength", -70);
    node_->declare_parameter("min_connectivity_score", 50.0);

    // 声明内网卡配置参数
    node_->declare_parameter("lan_interfaces", std::vector<std::string>{"eth0", "eth1"});
    node_->declare_parameter("enable_nat", true);

    // 声明高优先级WiFi网络配置参数
    node_->declare_parameter("priority_wifi_networks.ssids", std::vector<std::string>{});
    node_->declare_parameter("priority_wifi_networks.passwords", std::vector<std::string>{});
    node_->declare_parameter("priority_wifi_networks.priorities", std::vector<int64_t>{});
    node_->declare_parameter("priority_wifi_networks.security_types", std::vector<std::string>{});

    // 获取配置参数
    enable_auto_switch_ = node_->get_parameter("enable_auto_switch").as_bool();
    preferred_network_type_ = node_->get_parameter("preferred_network_type").as_int();
    min_signal_strength_ = node_->get_parameter("min_signal_strength").as_int();
    min_connectivity_score_ = node_->get_parameter("min_connectivity_score").as_double();

    // 获取内网卡配置参数
    lan_interfaces_ = node_->get_parameter("lan_interfaces").as_string_array();
    enable_nat_ = node_->get_parameter("enable_nat").as_bool();

    // 获取5G网卡候选列表（用于日志记录）
    configured_5g_interfaces_ = node_->get_parameter("5g_interfaces").as_string_array();

    // 直接从NetworkUtils获取已选择的5G接口
    current_5g_interface_ = utils::NetworkUtils::get_current_5g_interface();

    RCLCPP_INFO(logger_, "[5G_DETECT] 使用选择的5G网卡: %s", current_5g_interface_.c_str());

    RCLCPP_INFO(logger_, "[CONFIG] 自动切换: %s", enable_auto_switch_ ? "启用" : "禁用");
    RCLCPP_INFO(logger_, "[CONFIG] 首选网络类型: %d", preferred_network_type_);
    RCLCPP_INFO(logger_, "[CONFIG] 最低信号强度: %ddBm", min_signal_strength_);
    RCLCPP_INFO(logger_, "[CONFIG] 最低连接质量评分: %.1f", min_connectivity_score_);
    RCLCPP_INFO(logger_, "[CONFIG] NAT功能: %s", enable_nat_ ? "启用" : "禁用");

    // 输出内网卡配置
    std::string lan_list;
    for (size_t i = 0; i < lan_interfaces_.size(); ++i) {
      if (i > 0) lan_list += ", ";
      lan_list += lan_interfaces_[i];
    }
    RCLCPP_INFO(logger_, "[CONFIG] 内网卡列表: [%s]", lan_list.c_str());

    // 加载高优先级WiFi网络配置
    load_priority_wifi_networks();

    // 设置NetworkUtils的节点指针，用于读取配置参数
    utils::NetworkUtils::set_node(node_);
    RCLCPP_INFO(logger_, "[CONFIG] 设置NetworkUtils节点指针");
    // 创建发布者
    network_status_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkStatus>(
      "network_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建网络状态发布者: network_status");

    status_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "network_manager_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建管理器状态发布者: network_manager_status");

    // 创建订阅者
    //TODO  暂时未使用
    network_quality_sub_ = node_->create_subscription<gen3_network_interfaces::msg::NetworkQuality>(
      "network_quality", 10, std::bind(&NetworkManager::network_quality_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建网络质量订阅者: network_quality");

    // 订阅5G质量信息
    fiveg_quality_sub_ = node_->create_subscription<gen3_network_interfaces::msg::NetworkQuality>(
      "fiveg_quality", 10, std::bind(&NetworkManager::fiveg_quality_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建5G质量订阅者: fiveg_quality");

    // 创建服务
    switch_network_srv_ = node_->create_service<gen3_network_interfaces::srv::SwitchNetwork>(
      "switch_network", std::bind(&NetworkManager::handle_switch_network, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建网络切换服务: switch_network");

    refresh_network_srv_ = node_->create_service<std_srvs::srv::Trigger>(
      "refresh_network", std::bind(&NetworkManager::handle_refresh_network, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建网络刷新服务: refresh_network");

    // 创建定时器
    double status_update_interval = node_->get_parameter("status_update_interval").as_double();

    status_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(status_update_interval),
      std::bind(&NetworkManager::status_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建状态更新定时器，间隔: %.1f秒", status_update_interval);

    // 创建一次性初始化定时器，1秒后执行初始化逻辑
    init_timer_ = node_->create_wall_timer(
      std::chrono::seconds(1),
      std::bind(&NetworkManager::init_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建一次性初始化定时器，1秒后执行初始化逻辑");

    // 注意：不在初始化阶段立即执行定时器回调
    // 所有需要立即执行的逻辑都移到一次性初始化定时器中
    RCLCPP_INFO(logger_, "[TIMER] 首次状态更新将在一次性初始化定时器中执行");

    // 创建和初始化 CellularManager
    RCLCPP_INFO(logger_, "[CELLULAR] 创建蜂窝网络管理器");
    cellular_manager_ = std::make_unique<CellularManager>(node_);

    RCLCPP_INFO(logger_, "[CELLULAR] 初始化蜂窝网络管理器");
    if (!cellular_manager_->init()) {
      RCLCPP_ERROR(logger_, "[CELLULAR] 蜂窝网络管理器初始化失败");
      // 不返回失败，因为5G模块可能不存在，继续运行其他网络功能
      RCLCPP_WARN(logger_, "[CELLULAR] 继续运行，但5G功能将不可用");
      cellular_manager_.reset();  // 释放资源
    } else {
      RCLCPP_INFO(logger_, "[CELLULAR] 蜂窝网络管理器初始化成功");
    }

    RCLCPP_INFO(logger_, "网络管理器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "网络管理器初始化失败: %s", e.what());
    return false;
  }
}

void NetworkManager::network_quality_callback(
  const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg)
{
  RCLCPP_DEBUG(logger_,
    "[SUB] 收到网络质量信息（异步模式） - 接口: %s, 类型: %d, 信号强度: %ddBm, 延迟: %.2fms, 综合评分: %.1f",
    msg->interface_name.c_str(), msg->network_type, msg->signal_strength,
    msg->latency_ms, msg->overall_score);

  // === 1. 快速更新网络质量信息（短时间持有锁） ===
  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    std::string key = msg->interface_name;
    network_quality_map_[key] = *msg;
  }

  // === 2. 检查上一次异步处理的结果 ===
  check_async_quality_processing();

  // === 3. 启动异步处理（避免阻塞回调线程） ===
  if (enable_auto_switch_) {
    process_quality_async(*msg);
  }

  RCLCPP_DEBUG(logger_, "[SUB] 网络质量信息已快速更新，异步处理已启动 - 接口: %s",
               msg->interface_name.c_str());
}

void NetworkManager::init_timer_callback()
{
  RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化定时器触发");

  // 取消定时器，确保只执行一次
  if (init_timer_) {
    init_timer_->cancel();
    init_timer_.reset();
    RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化定时器已取消");
  }

  // 执行原本在初始化后立即执行的逻辑
  RCLCPP_INFO(logger_, "[INIT_TIMER] 执行首次状态更新（原本在初始化后立即执行）");

  // 调用状态更新定时器回调，执行完整的状态更新逻辑
  status_timer_callback();

  // 初始化NAT规则 - 根据当前网络状态配置
  RCLCPP_INFO(logger_, "[INIT_TIMER] 开始初始化NAT规则");
  initialize_nat_rules();

  RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化逻辑执行完成");
}

void NetworkManager::status_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 状态更新定时器触发");

  // 检查异步质量处理结果
  check_async_quality_processing();

  // 先更新状态，再发布（避免在同一个调用链中重复获取锁）
  bool update_success = update_network_status();
  if (update_success) {
    publish_network_status();
  }

  RCLCPP_DEBUG(logger_, "[TIMER] 状态更新定时器处理完成");
}

void NetworkManager::fiveg_quality_callback(
  const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg)
{
  RCLCPP_DEBUG(logger_,
    "[SUB] 收到5G质量信息 - 接口: %s, 信号强度: %ddBm",
    msg->interface_name.c_str(), msg->signal_strength);

  // 更新最新的5G质量信息
  {
    std::lock_guard<std::mutex> lock(fiveg_quality_mutex_);
    latest_fiveg_quality_ = *msg;
  }

  RCLCPP_DEBUG(logger_, "[SUB] 5G质量信息已更新");
}

void NetworkManager::handle_switch_network(
  const std::shared_ptr<gen3_network_interfaces::srv::SwitchNetwork::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::SwitchNetwork::Response> response)
{
  RCLCPP_INFO(
    logger_, "[SRV] 收到网络切换请求: 类型=%d, WiFi=%s, 强制=%s",
    request->target_network_type, request->target_wifi_ssid.c_str(),
    request->force_switch ? "是" : "否");

  // 保存当前网络信息
  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    response->previous_network_type = current_status_.network_type;
    response->previous_interface = current_status_.interface_name;
    response->previous_ip_address = current_status_.ip_address;

    RCLCPP_INFO(logger_,
      "[SRV] 当前网络信息 - 类型: %d, 接口: %s, IP: %s",
      current_status_.network_type, current_status_.interface_name.c_str(),
      current_status_.ip_address.c_str());
  }

  // 执行网络切换
  RCLCPP_INFO(logger_, "[SRV] 开始执行网络切换");
  bool result = switch_to_network(
    request->target_network_type, request->target_wifi_ssid, request->force_switch);

  if (result) {
    response->success = true;
    response->message = "网络切换成功";
    response->error_code = "";
    RCLCPP_INFO(logger_, "[SRV] 网络切换成功");
  } else {
    response->success = false;
    response->message = "网络切换失败";
    response->error_code = "SWITCH_FAILED";
    RCLCPP_ERROR(logger_, "[SRV] 网络切换失败");
  }
}

void NetworkManager::handle_refresh_network(
  const std::shared_ptr<std_srvs::srv::Trigger::Request> request,
  std::shared_ptr<std_srvs::srv::Trigger::Response> response)
{
  (void)request;  // 未使用的参数

  RCLCPP_INFO(logger_, "[SRV] 收到刷新网络请求");

  bool result = update_network_status();

  if (result) {
    response->success = true;
    response->message = "网络状态刷新成功";
    RCLCPP_INFO(logger_, "[SRV] 网络状态刷新成功");
  } else {
    response->success = false;
    response->message = "网络状态刷新失败";
    RCLCPP_ERROR(logger_, "[SRV] 网络状态刷新失败");
  }
}

bool NetworkManager::update_network_status()
{
  RCLCPP_INFO(logger_, "[STATUS] 开始更新网络状态");

  try {
    // 保存旧的接口信息用于变化检测
    std::string old_interface_name;
    uint8_t old_network_type;
    {
      std::lock_guard<std::mutex> lock(status_mutex_);
      old_interface_name = current_status_.interface_name;
      old_network_type = current_status_.network_type;
    }

    std::lock_guard<std::mutex> lock(status_mutex_);

    // 获取所有网络接口信息
    auto all_interfaces = utils::NetworkUtils::get_network_interfaces();
    RCLCPP_DEBUG(logger_, "[STATUS] 检测到 %zu 个网络接口", all_interfaces.size());

    // 打印所有接口状态
    for (const auto & iface : all_interfaces) {
      RCLCPP_DEBUG(logger_, "[STATUS] 接口: %s, 状态: %s, IP: %s",
        iface.name.c_str(), iface.is_up ? "UP" : "DOWN", iface.ip_address.c_str());
    }

    // 获取默认路由接口
    std::string default_interface = utils::NetworkUtils::get_default_route_interface();

    if (default_interface.empty()) {
      RCLCPP_WARN(logger_, "[STATUS] 无法获取默认路由接口，检查所有活跃接口");

      // 如果没有默认路由，尝试找到第一个有IP地址的接口
      for (const auto & iface : all_interfaces) {
        if (iface.is_up && !iface.ip_address.empty() && iface.ip_address != "127.0.0.1") {
          default_interface = iface.name;
          RCLCPP_INFO(logger_, "[STATUS] 使用活跃接口: %s", default_interface.c_str());
          break;
        }
      }

      if (default_interface.empty()) {
        RCLCPP_ERROR(logger_, "[STATUS] 没有找到任何可用的网络接口");
        current_status_.connection_status =
          gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_DISCONNECTED;
        return false;
      }
    }

    RCLCPP_INFO(logger_, "[STATUS] 使用默认接口: %s", default_interface.c_str());
    
    // 获取接口信息
    auto interface_info = utils::NetworkUtils::get_interface_info(default_interface);
    
    // 更新状态信息
    current_status_.header.stamp = node_->now();

    // 使用配置的接口名称获取网络类型
    // 获取配置的接口名称
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();

    current_status_.network_type = utils::NetworkUtils::get_network_type(
      default_interface, wifi_interface, ethernet_interface, fiveg_interface);
    current_status_.connection_status = 
      interface_info.is_up ? 
      gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED : 
      gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_DISCONNECTED;
    current_status_.interface_name = default_interface;
    current_status_.ip_address = interface_info.ip_address;
    current_status_.gateway = interface_info.gateway;
    
    // 获取DNS服务器
    auto dns_servers = utils::NetworkUtils::get_dns_servers();
    current_status_.dns_servers.clear();
    for (const auto & dns : dns_servers) {
      current_status_.dns_servers.push_back(dns);
    }
    
    // 如果是WiFi，获取WiFi信息
    if (current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      RCLCPP_DEBUG(logger_, "[STATUS] 检测到WiFi网络，获取WiFi信息");
      auto wifi_info = utils::NetworkUtils::get_current_wifi_info();
      current_status_.wifi_ssid = wifi_info.ssid;
      current_status_.wifi_signal_strength = wifi_info.signal_strength;
      current_status_.wifi_security_type = wifi_info.security_type;

      RCLCPP_INFO(logger_,
        "[STATUS] WiFi信息 - SSID: %s, 信号强度: %ddBm, 安全类型: %s",
        wifi_info.ssid.c_str(), wifi_info.signal_strength, wifi_info.security_type.c_str());
    } else {
      current_status_.wifi_ssid = "";
      current_status_.wifi_signal_strength = 0;
      current_status_.wifi_security_type = "";
      RCLCPP_DEBUG(logger_, "[STATUS] 非WiFi网络，清空WiFi信息");
    }

    // 如果是5G网络，从订阅的质量信息中获取5G信息
    if (current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
      RCLCPP_DEBUG(logger_, "[STATUS] 检测到5G网络，从质量信息中获取5G信息");

      // 从订阅的5G质量信息中获取数据
      {
        std::lock_guard<std::mutex> lock(fiveg_quality_mutex_);
        if (!latest_fiveg_quality_.interface_name.empty()) {
          // 填充5G特定信息到状态中
          current_status_.fiveg_operator_name = latest_fiveg_quality_.operator_name;
          current_status_.fiveg_signal_strength = latest_fiveg_quality_.signal_strength;
          current_status_.fiveg_network_type = latest_fiveg_quality_.network_mode;

          RCLCPP_DEBUG(logger_,
            "[STATUS] 5G信息已更新 - 运营商: %s, 信号强度: %ddBm, 网络制式: %s",
            current_status_.fiveg_operator_name.c_str(),
            current_status_.fiveg_signal_strength,
            current_status_.fiveg_network_type.c_str());
        } else {
          RCLCPP_DEBUG(logger_, "[STATUS] 尚未收到5G质量信息，使用默认值");
          current_status_.fiveg_operator_name = "未知运营商";
          current_status_.fiveg_signal_strength = 0;
          current_status_.fiveg_network_type = "未知";
        }
      }
    } else {
      // 清空5G信息
      current_status_.fiveg_operator_name = "";
      current_status_.fiveg_signal_strength = 0;
      current_status_.fiveg_network_type = "";
      RCLCPP_DEBUG(logger_, "[STATUS] 非5G网络，清空5G信息");
    }

    // 使用统一的枚举转换函数
    std::string network_type_str = utils::EnumUtils::network_type_to_string(current_status_.network_type);
    std::string connection_status_str = utils::EnumUtils::connection_status_to_string(current_status_.connection_status);

    RCLCPP_INFO(logger_,
      "[STATUS] 网络状态更新完成 - 接口: %s, 类型: %s, 状态: %s, IP: %s",
      current_status_.interface_name.c_str(), network_type_str.c_str(),
      connection_status_str.c_str(), current_status_.ip_address.c_str());

    // 检测接口变化并更新NAT规则
    bool interface_changed = (old_interface_name != current_status_.interface_name);
    bool network_type_changed = (old_network_type != current_status_.network_type);

    if (interface_changed || network_type_changed) {
      RCLCPP_INFO(logger_,
        "[STATUS] 检测到网络变化 - 接口: %s -> %s, 类型: %d -> %d",
        old_interface_name.c_str(), current_status_.interface_name.c_str(),
        old_network_type, current_status_.network_type);

      // 检查新接口是否为外网卡（WiFi或5G）
      std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
      std::string fiveg_interface = get_current_5g_interface();

      bool is_wan_interface = false;
      std::string wan_type = "未知";

      if (current_status_.interface_name == wifi_interface &&
          current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
        is_wan_interface = true;
        wan_type = "WiFi";
      } else if (current_status_.interface_name == fiveg_interface &&
                 current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
        is_wan_interface = true;
        wan_type = "5G";
      }

      if (is_wan_interface) {
        RCLCPP_INFO(logger_, "[STATUS] 检测到外网卡变化，更新NAT规则 - %s接口: %s",
                    wan_type.c_str(), current_status_.interface_name.c_str());

        // 释放锁后更新NAT规则（避免在持有锁时调用可能耗时的操作）
        std::string new_interface = current_status_.interface_name;
        lock.~lock_guard();  // 显式释放锁

        bool nat_result = update_nat_rules(new_interface);
        if (nat_result) {
          RCLCPP_INFO(logger_, "[STATUS] NAT规则更新成功 - 外网接口: %s (%s)",
                      new_interface.c_str(), wan_type.c_str());
        } else {
          RCLCPP_WARN(logger_, "[STATUS] NAT规则更新失败 - 外网接口: %s (%s)",
                      new_interface.c_str(), wan_type.c_str());
        }
      } else {
        RCLCPP_INFO(logger_, "[STATUS] 当前接口不是外网卡，清理NAT规则 - 接口: %s",
                    current_status_.interface_name.c_str());

        // 释放锁后清理NAT规则
        lock.~lock_guard();  // 显式释放锁

        utils::IptablesUtils::cleanup_nat_rules();
      }
    }

    return true;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[STATUS] 更新网络状态失败: %s", e.what());
    return false;
  }
}

bool NetworkManager::switch_to_network(
  uint8_t network_type, const std::string & wifi_ssid, bool force_switch)
{
  RCLCPP_INFO(logger_,
    "[SWITCH] 开始网络切换 - 类型: %d, WiFi: %s, 强制: %s",
    network_type, wifi_ssid.c_str(), force_switch ? "是" : "否");

  try {
    // 1. 检查当前网络状态
    std::lock_guard<std::mutex> lock(status_mutex_);

    // 如果不是强制切换，检查是否已经连接到目标网络
    if (!force_switch) {
      if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
        if (current_status_.network_type == network_type &&
            current_status_.wifi_ssid == wifi_ssid &&
            current_status_.connection_status == gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED) {
          RCLCPP_INFO(logger_, "[SWITCH] 已连接到目标WiFi网络: %s", wifi_ssid.c_str());
          return true;
        }
      } else if (current_status_.network_type == network_type &&
                 current_status_.connection_status == gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED) {
        RCLCPP_INFO(logger_, "[SWITCH] 已连接到目标网络类型: %d", network_type);
        return true;
      }
    }

    // 2. 记录切换前的状态（用于失败回滚）
    auto previous_type = current_status_.network_type;
    auto previous_ssid = current_status_.wifi_ssid;
    auto previous_interface = current_status_.interface_name;
    auto previous_ip = current_status_.ip_address;
    auto previous_gateway = current_status_.gateway;

    RCLCPP_INFO(logger_, "[SWITCH] 从网络 %d (%s) 切换到 %d (%s)",
      previous_type, previous_ssid.c_str(), network_type, wifi_ssid.c_str());
    RCLCPP_INFO(logger_, "[SWITCH] 备份当前状态 - 接口: %s, IP: %s",
      previous_interface.c_str(), previous_ip.c_str());

    // // 2.5. 保存全局静态路由表（保护所有接口的静态路由）
    // RCLCPP_INFO(logger_, "[SWITCH] 保存全局静态路由表");
    // std::vector<std::string> global_static_routes = save_static_routes();

    // 3. 执行实际的网络切换
    bool switch_success = false;

    switch (network_type) {
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
        switch_success = switch_to_wifi(wifi_ssid);
        break;

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
        switch_success = switch_to_ethernet();
        break;

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
        switch_success = switch_to_5g();
        break;

      default:
        RCLCPP_ERROR(logger_, "[SWITCH] 不支持的网络类型: %d", network_type);
        return false;
    }

    if (!switch_success) {
      RCLCPP_ERROR(logger_, "[SWITCH] 网络切换失败，开始回滚到之前状态");

      // 回滚到之前的网络状态
      bool rollback_success = rollback_to_previous_network(previous_type, previous_ssid, previous_interface);

      if (rollback_success) {
        RCLCPP_INFO(logger_, "[SWITCH] 成功回滚到之前的网络状态");
      } else {
        RCLCPP_ERROR(logger_, "[SWITCH] 回滚失败，系统可能处于无网络状态");
      }

      // 发布切换失败消息
      std_msgs::msg::String msg;
      msg.data = "SWITCH_FAILED_ROLLBACK";
      status_pub_->publish(msg);

      return false;
    }

    // 4. 验证切换结果
    RCLCPP_INFO(logger_, "[SWITCH] 验证网络切换结果...");
    bool verification_success = verify_network_switch(network_type, wifi_ssid);

    if (verification_success) {
      RCLCPP_INFO(logger_, "[SWITCH] 网络切换成功完成");

      // // 恢复全局静态路由表
      // RCLCPP_INFO(logger_, "[SWITCH] 恢复全局静态路由表");
      // restore_static_routes(global_static_routes);

      // 发布切换成功消息
      std_msgs::msg::String msg;
      msg.data = "SWITCH_SUCCESS";
      status_pub_->publish(msg);

      return true;
    } else {
      RCLCPP_ERROR(logger_, "[SWITCH] 网络切换验证失败，开始回滚");

      // 验证失败也需要回滚
      bool rollback_success = rollback_to_previous_network(previous_type, previous_ssid, previous_interface);

      if (rollback_success) {
        RCLCPP_INFO(logger_, "[SWITCH] 验证失败后成功回滚到之前状态");

        // 发布回滚成功消息
        std_msgs::msg::String msg;
        msg.data = "SWITCH_FAILED_ROLLBACK_SUCCESS";
        status_pub_->publish(msg);
      } else {
        RCLCPP_ERROR(logger_, "[SWITCH] 验证失败且回滚失败，系统网络状态异常");

        // 发布回滚失败消息
        std_msgs::msg::String msg;
        msg.data = "SWITCH_FAILED_ROLLBACK_FAILED";
        status_pub_->publish(msg);
      }

      return false;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SWITCH] 网络切换异常: %s", e.what());
    return false;
  }
}

void NetworkManager::publish_network_status()
{
  // 创建状态副本以避免长时间持有锁
  gen3_network_interfaces::msg::NetworkStatus status_copy;
  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    status_copy = current_status_;
  }

  // 更新时间戳
  status_copy.header.stamp = node_->now();

  // 发布网络状态（不持有锁）
  network_status_pub_->publish(status_copy);

  // 详细的状态日志 - 使用统一的枚举转换函数
  std::string network_type_str = utils::EnumUtils::network_type_to_string(current_status_.network_type);
  std::string connection_status_str = utils::EnumUtils::connection_status_to_string(current_status_.connection_status);

  RCLCPP_DEBUG(logger_,
    "[PUB] 发布网络状态 - 类型: %s, 接口: %s, 状态: %s, IP: %s, 网关: %s",
    network_type_str.c_str(),
    current_status_.interface_name.c_str(),
    connection_status_str.c_str(),
    current_status_.ip_address.c_str(),
    current_status_.gateway.c_str());

  // 如果是WiFi，显示WiFi特定信息
  if (current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
    RCLCPP_DEBUG(logger_,
      "[PUB] WiFi详情 - SSID: %s, 信号强度: %ddBm, 安全类型: %s",
      current_status_.wifi_ssid.c_str(),
      current_status_.wifi_signal_strength,
      current_status_.wifi_security_type.c_str());
  }

  // 如果是5G，显示5G特定信息
  if (current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
    RCLCPP_DEBUG(logger_,
      "[PUB] 5G详情 - 运营商: %s, 信号强度: %ddBm, 网络制式: %s",
      current_status_.fiveg_operator_name.c_str(),
      current_status_.fiveg_signal_strength,
      current_status_.fiveg_network_type.c_str());
  }

  // 显示DNS服务器信息
  if (!current_status_.dns_servers.empty()) {
    std::string dns_list;
    for (size_t i = 0; i < current_status_.dns_servers.size(); ++i) {
      if (i > 0) dns_list += ", ";
      dns_list += current_status_.dns_servers[i];
    }
    RCLCPP_DEBUG(logger_, "[PUB] DNS服务器: %s", dns_list.c_str());
  }
}

// 切换到WiFi网络的具体实现
bool NetworkManager::switch_to_wifi(const std::string & ssid)
{
  RCLCPP_INFO(logger_, "[SWITCH] 开始切换到WiFi: %s", ssid.c_str());

  try {
    // 获取配置的WiFi接口名称
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    // 获取配置的5G接口名称
    std::string fiveg_interface = get_current_5g_interface();

    // 1. 确保WiFi接口处于活跃状态
    RCLCPP_INFO(logger_, "[SWITCH] 确保WiFi接口活跃: %s", wifi_interface.c_str());
    utils::NetworkUtils::set_interface_state(wifi_interface, true);

    // // 2. 保存当前静态路由表
    // RCLCPP_INFO(logger_, "[SWITCH] 保存当前静态路由表");
    // std::vector<std::string> saved_routes = save_static_routes();

    // 3. 连接到指定WiFi网络（如果尚未连接）
    auto current_wifi = utils::NetworkUtils::get_current_wifi_info();
    if (!current_wifi.is_connected || current_wifi.ssid != ssid) {
      RCLCPP_INFO(logger_, "[SWITCH] 连接到WiFi网络: %s", ssid.c_str());
      bool connect_result = utils::NetworkUtils::connect_to_wifi(ssid, "", "WPA2");

      if (!connect_result) {
        RCLCPP_ERROR(logger_, "[SWITCH] WiFi连接失败: %s", ssid.c_str());
        return false;
      }

      // 等待连接建立
      std::this_thread::sleep_for(std::chrono::seconds(3));

      // // 4. 恢复静态路由表
      // RCLCPP_INFO(logger_, "[SWITCH] 恢复静态路由表");
      // restore_static_routes(saved_routes);

    } else {
      RCLCPP_INFO(logger_, "[SWITCH] 已连接到目标WiFi: %s", ssid.c_str());
    }

    // 5. 更新路由优先级 - 设置WiFi为最高优先级
    RCLCPP_INFO(logger_, "[SWITCH] 更新路由优先级，WiFi优先");
    bool route_success1 = update_route_priority(fiveg_interface, 60);  // 低优先级
    bool route_success2 = update_route_priority(wifi_interface, 40);  // 最高优先级

    if (!route_success2) {
      RCLCPP_ERROR(logger_, "[SWITCH] 更新WiFi路由优先级失败");
      return false;
    }

    // 6. 降低其他接口的路由优先级
    // RCLCPP_INFO(logger_, "[SWITCH] 降低其他接口路由优先级");
    // update_route_priority("eth0", 200);   // 较低优先级
    // update_route_priority("usb0", 300);   // 最低优先级

    // 7. 更新NAT规则，确保内网设备能通过WiFi上网
    RCLCPP_INFO(logger_, "[SWITCH] 更新NAT规则 - WiFi接口");
    bool nat_result = update_nat_rules(wifi_interface);
    if (!nat_result) {
      RCLCPP_WARN(logger_, "[SWITCH] NAT规则更新失败，但不影响WiFi切换");
    }

    RCLCPP_INFO(logger_, "[SWITCH] WiFi路由优先级切换完成");
    return true;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SWITCH] WiFi切换异常: %s", e.what());
    return false;
  }
}

// 切换到以太网的具体实现
bool NetworkManager::switch_to_ethernet()
{
  RCLCPP_INFO(logger_, "[SWITCH] 开始切换到以太网");

  try {
    // 获取配置的以太网接口名称
    std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();

    // 1. 确保以太网接口处于活跃状态
    RCLCPP_INFO(logger_, "[SWITCH] 确保以太网接口活跃: %s", ethernet_interface.c_str());
    utils::NetworkUtils::set_interface_state(ethernet_interface, true);

    // 2. 等待接口稳定
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 3. 检查以太网连接状态
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    bool eth_connected = false;
    for (const auto & iface : interfaces) {
      if (iface.name == ethernet_interface && !iface.ip_address.empty()) {
        eth_connected = true;
        RCLCPP_INFO(logger_, "[SWITCH] 以太网已连接，IP: %s", iface.ip_address.c_str());
        break;
      }
    }

    if (!eth_connected) {
      RCLCPP_ERROR(logger_, "[SWITCH] 以太网未连接或无IP地址");
      return false;
    }

    // 4. 安全更新路由优先级 - 设置以太网为最高优先级
    RCLCPP_INFO(logger_, "[SWITCH] 安全更新路由优先级，以太网优先");
    bool route_success = safe_update_default_route(ethernet_interface, 100);  // 最高优先级

    if (!route_success) {
      RCLCPP_ERROR(logger_, "[SWITCH] 更新以太网路由优先级失败");
      return false;
    }

    // 5. 安全降低其他接口的路由优先级
    // RCLCPP_INFO(logger_, "[SWITCH] 安全降低其他接口路由优先级");
    // 获取WiFi和5G接口名称
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();
    safe_update_default_route(wifi_interface, 200);  // 较低优先级
    safe_update_default_route(fiveg_interface, 300);   // 最低优先级

    RCLCPP_INFO(logger_, "[SWITCH] 以太网路由优先级切换完成");
    return true;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SWITCH] 以太网切换异常: %s", e.what());
    return false;
  }
}

// 切换到5G网络的具体实现
bool NetworkManager::switch_to_5g()
{
  RCLCPP_INFO(logger_, "[SWITCH] 开始切换到5G网络");

  try {
    // 获取配置的5G接口名称
    std::string fiveg_interface = get_current_5g_interface();
    // 获取配置的WiFi接口名称
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();

    // 1. 确保5G接口处于活跃状态
    RCLCPP_INFO(logger_, "[SWITCH] 确保5G接口活跃: %s", fiveg_interface.c_str());
    utils::NetworkUtils::set_interface_state(fiveg_interface, true);

    // 2. 等待5G连接建立
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // 3. 检查5G连接状态
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    bool fiveg_connected = false;
    for (const auto & iface : interfaces) {
      if (iface.name == fiveg_interface && !iface.ip_address.empty()) {
        fiveg_connected = true;
        RCLCPP_INFO(logger_, "[SWITCH] 5G已连接，IP: %s", iface.ip_address.c_str());
        break;
      }
    }

    if (!fiveg_connected) {
      RCLCPP_ERROR(logger_, "[SWITCH] 5G未连接或无IP地址");
      return false;
    }

    // 4. 更新路由优先级 - 设置5G为最高优先级
    RCLCPP_INFO(logger_, "[SWITCH] 更新路由优先级，5G优先");
    bool route_success1 = update_route_priority(wifi_interface, 70);  // 低优先级
    bool route_success2 = update_route_priority(fiveg_interface, 50);  // 最高优先级

    if (!route_success2) {
      RCLCPP_ERROR(logger_, "[SWITCH] 更新5G路由优先级失败");
      return false;
    }

    // 5. 降低其他接口的路由优先级
    // RCLCPP_INFO(logger_, "[SWITCH] 降低其他接口路由优先级");
    // 获取WiFi和以太网接口名称
    // std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    // std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    // update_route_priority(wifi_interface, 200);  // 较低优先级
    // update_route_priority(ethernet_interface, 300);   // 最低优先级

    // 6. 更新NAT规则，确保内网设备能通过5G上网
    RCLCPP_INFO(logger_, "[SWITCH] 更新NAT规则 - 5G接口");
    bool nat_result = update_nat_rules(fiveg_interface);
    if (!nat_result) {
      RCLCPP_WARN(logger_, "[SWITCH] NAT规则更新失败，但不影响5G切换");
    }

    RCLCPP_INFO(logger_, "[SWITCH] 5G路由优先级切换完成");
    return true;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SWITCH] 5G切换异常: %s", e.what());
    return false;
  }
}

// 验证网络切换结果
bool NetworkManager::verify_network_switch(uint8_t network_type, const std::string & wifi_ssid)
{
  RCLCPP_INFO(logger_, "[SWITCH] 开始验证网络切换结果");

  try {
    // 等待网络稳定
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 1. 检查网络接口状态
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    bool target_interface_active = false;
    std::string target_interface;

    // 获取配置的接口名称
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();

    switch (network_type) {
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
        target_interface = wifi_interface;
        break;
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
        target_interface = ethernet_interface;
        break;
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
        target_interface = fiveg_interface;
        break;
      default:
        RCLCPP_ERROR(logger_, "[SWITCH] 未知的网络类型: %d", network_type);
        return false;
    }

    // 检查目标接口是否活跃且有IP地址
    for (const auto & iface : interfaces) {
      if (iface.name == target_interface && !iface.ip_address.empty()) {
        target_interface_active = true;
        RCLCPP_INFO(logger_, "[SWITCH] 目标接口 %s 已激活，IP: %s",
          target_interface.c_str(), iface.ip_address.c_str());
        break;
      }
    }

    if (!target_interface_active) {
      RCLCPP_ERROR(logger_, "[SWITCH] 目标接口 %s 未激活或无IP地址", target_interface.c_str());
      return false;
    }

    // 2. 对于WiFi，验证是否连接到正确的SSID
    if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      auto current_wifi = utils::NetworkUtils::get_current_wifi_info();
      if (!current_wifi.is_connected || current_wifi.ssid != wifi_ssid) {
        RCLCPP_ERROR(logger_, "[SWITCH] WiFi验证失败 - 期望: %s, 实际: %s",
          wifi_ssid.c_str(), current_wifi.ssid.c_str());
        return false;
      }
      RCLCPP_INFO(logger_, "[SWITCH] WiFi连接验证成功: %s", wifi_ssid.c_str());
    }

    // 3. 测试网络连通性   //TODO   会死锁
    RCLCPP_INFO(logger_, "[SWITCH] 测试网络连通性");
    bool connectivity_test = utils::NetworkUtils::test_connectivity("*******", 3000);

    if (!connectivity_test) {
      RCLCPP_ERROR(logger_, "[SWITCH] 网络连通性测试失败");
      return false;
    }

    // 4. 更新内部状态（安全地获取锁）
    {
      //TODO 有问题
      // std::lock_guard<std::mutex> lock(status_mutex_);
      current_status_.network_type = network_type;
      current_status_.interface_name = target_interface;
      current_status_.connection_status = gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED;

      if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
        current_status_.wifi_ssid = wifi_ssid;
      }

      // 更新IP地址和其他信息
      for (const auto & iface : interfaces) {
        if (iface.name == target_interface) {
          current_status_.ip_address = iface.ip_address;
          current_status_.gateway = iface.gateway;
          break;
        }
      }
    }

    RCLCPP_INFO(logger_, "[SWITCH] 网络切换验证成功");
    return true;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[SWITCH] 网络切换验证异常: %s", e.what());
    return false;
  }
}

// 网络切换失败回滚函数
bool NetworkManager::rollback_to_previous_network(
  uint8_t previous_type, const std::string & previous_ssid, const std::string & previous_interface)
{
  RCLCPP_INFO(logger_, "[ROLLBACK] 开始回滚到之前的网络 - 类型: %d, 接口: %s",
    previous_type, previous_interface.c_str());

  bool rollback_success = false;

  try {
    // 获取接口配置
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();

    // 根据之前的网络类型执行回滚
    switch (previous_type) {
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
        RCLCPP_INFO(logger_, "[ROLLBACK] 回滚到WiFi网络: %s", previous_ssid.c_str());
        
        if (!previous_ssid.empty()) {
          // 尝试重新连接之前的WiFi
          rollback_success = switch_to_wifi(previous_ssid);
        } else {
          // 直接更新路由优先级，不重新连接WiFi
          rollback_success = update_route_priority(fiveg_interface, 60);
          rollback_success = update_route_priority(wifi_interface, 40);
          // if (rollback_success) {
          //   update_route_priority("eth0", 200);
          //   update_route_priority("usb0", 300);
          // }
        }
        break;

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
        RCLCPP_INFO(logger_, "[ROLLBACK] 回滚到以太网");
        rollback_success = true;
        // rollback_success = update_route_priority(ethernet_interface, 40);
        // if (rollback_success) {
        //   update_route_priority("wlan0", 200);
        //   update_route_priority("usb0", 300);
        // }
        break;

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
        RCLCPP_INFO(logger_, "[ROLLBACK] 回滚到5G网络");
        rollback_success = update_route_priority(wifi_interface, 70);
        rollback_success = update_route_priority(fiveg_interface, 50);
        // if (rollback_success) {
        //   update_route_priority("wlan0", 200);
        //   update_route_priority("eth0", 300);
        // }
        break;

      default:
        RCLCPP_ERROR(logger_, "[ROLLBACK] 未知的网络类型: %d", previous_type);
        return false;
    }

    if (rollback_success) {
      RCLCPP_INFO(logger_, "[ROLLBACK] 回滚成功");
    } else {
      RCLCPP_ERROR(logger_, "[ROLLBACK] 回滚失败");
    }

    return rollback_success;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROLLBACK] 回滚异常: %s", e.what());
    return false;
  }
}

// 基本网络恢复函数（当回滚也失败时的最后手段）
bool NetworkManager::attempt_basic_network_recovery(const std::string & target_interface)
{
  RCLCPP_INFO(logger_, "[RECOVERY] 尝试基本网络恢复: %s", target_interface.c_str());

  try {
    // 1. 重启目标网络接口
    RCLCPP_INFO(logger_, "[RECOVERY] 重启网络接口: %s", target_interface.c_str());
    utils::NetworkUtils::set_interface_state(target_interface, false);
    std::this_thread::sleep_for(std::chrono::seconds(2));
    utils::NetworkUtils::set_interface_state(target_interface, true);
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // 2. 检查接口是否获得IP地址
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    for (const auto & iface : interfaces) {
      if (iface.name == target_interface && !iface.ip_address.empty()) {
        RCLCPP_INFO(logger_, "[RECOVERY] 接口 %s 已获得IP: %s",
          target_interface.c_str(), iface.ip_address.c_str());

        // 3. 设置默认路由
        utils::NetworkUtils::set_default_route(target_interface);

        // 4. 测试基本连通性
        bool connectivity = utils::NetworkUtils::test_connectivity("*******", 5000);
        if (connectivity) {
          RCLCPP_INFO(logger_, "[RECOVERY] 基本网络恢复成功");

          // 更新状态为基本连接状态
          std::lock_guard<std::mutex> lock(status_mutex_);
          current_status_.interface_name = target_interface;
          current_status_.ip_address = iface.ip_address;
          current_status_.gateway = iface.gateway;
          current_status_.connection_status = gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED;

          return true;
        }
      }
    }

    RCLCPP_ERROR(logger_, "[RECOVERY] 基本网络恢复失败");
    return false;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[RECOVERY] 基本网络恢复异常: %s", e.what());
    return false;
  }
}

// 更新路由优先级函数
bool NetworkManager::update_route_priority(const std::string & interface_name, int priority)
{
  RCLCPP_INFO(logger_, "[ROUTE] 更新接口 %s 路由优先级为 %d",
    interface_name.c_str(), priority);

  try {
    // 直接调用 NetworkUtils 的实现，它已经包含了WiFi重连的逻辑
    bool result = utils::NetworkUtils::update_route_priority(interface_name, priority);
    
    if (result) {
      RCLCPP_INFO(logger_, "[ROUTE] 成功更新接口 %s 的路由优先级", interface_name.c_str());
    } else {
      RCLCPP_ERROR(logger_, "[ROUTE] 更新接口 %s 的路由优先级失败", interface_name.c_str());
    }
    
    return result;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE] 更新路由优先级异常: %s", e.what());
    return false;
  }
}

// 保存当前静态路由表
std::vector<std::string> NetworkManager::save_static_routes()
{
  RCLCPP_INFO(logger_, "[ROUTE] 保存当前静态路由表");

  std::vector<std::string> saved_routes;

  try {
    // 获取接口配置
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();

    // 获取当前所有路由
    std::string route_cmd = "ip route show";
    std::string route_output = utils::NetworkUtils::exec_command(route_cmd);

    // 解析路由输出，保存非默认路由
    std::istringstream iss(route_output);
    std::string line;

    while (std::getline(iss, line)) {
      // 跳过不需要保存的路由
      if (line.find("default") != std::string::npos ||     // 默认路由
          line.find("127.0.0.1") != std::string::npos ||   // 本地回环
          line.find("169.254.") != std::string::npos ||    // 链路本地地址
          line.find("fe80:") != std::string::npos ||       // IPv6链路本地
          line.find("::1") != std::string::npos ||         // IPv6回环
          line.find("scope link") != std::string::npos ||  // 链路范围路由
          line.find("proto kernel") != std::string::npos || // 内核自动路由
          line.empty()) {
        continue;
      }

      // 保存所有重要的路由（包括静态路由和特殊网络路由）
      if (line.find("proto static") != std::string::npos ||  // 明确的静态路由
          line.find("/32") != std::string::npos ||           // 主机路由
          line.find("/31") != std::string::npos ||           // 点对点路由
          line.find("/30") != std::string::npos ||           // 小网段路由
          line.find("/29") != std::string::npos ||           // 小网段路由
          line.find("/28") != std::string::npos ||           // 小网段路由
          line.find("/27") != std::string::npos ||           // 小网段路由
          line.find("/26") != std::string::npos ||           // 小网段路由
          line.find("/25") != std::string::npos ||           // 小网段路由
          line.find("/24") != std::string::npos ||           // 标准网段路由
          line.find("/23") != std::string::npos ||           // 大网段路由
          line.find("/22") != std::string::npos ||           // 大网段路由
          line.find("/21") != std::string::npos ||           // 大网段路由
          line.find("/20") != std::string::npos ||           // 大网段路由
          line.find("/19") != std::string::npos ||           // 大网段路由
          line.find("/18") != std::string::npos ||           // 大网段路由
          line.find("/17") != std::string::npos ||           // 大网段路由
          line.find("/16") != std::string::npos ||           // 大网段路由
          line.find("/15") != std::string::npos ||           // 超大网段路由
          line.find("/14") != std::string::npos ||           // 超大网段路由
          line.find("/13") != std::string::npos ||           // 超大网段路由
          line.find("/12") != std::string::npos ||           // 超大网段路由
          line.find("/11") != std::string::npos ||           // 超大网段路由
          line.find("/10") != std::string::npos ||           // 超大网段路由
          line.find("/9") != std::string::npos ||            // 超大网段路由
          line.find("/8") != std::string::npos ||            // 超大网段路由
          (line.find("via") != std::string::npos &&          // 通过网关的路由
           line.find("dev " + ethernet_interface) != std::string::npos) ||    // 特别保护以太网路由
          (line.find("via") != std::string::npos &&          // 通过网关的路由
           line.find("dev " + wifi_interface) != std::string::npos) ||   // 特别保护WiFi路由
          (line.find("via") != std::string::npos &&          // 通过网关的路由
           line.find("dev " + fiveg_interface) != std::string::npos)) {    // 特别保护5G路由

        saved_routes.push_back(line);
        RCLCPP_DEBUG(logger_, "[ROUTE] 保存路由: %s", line.c_str());
      }
    }

    RCLCPP_INFO(logger_, "[ROUTE] 已保存 %zu 条静态路由", saved_routes.size());

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE] 保存静态路由异常: %s", e.what());
  }

  return saved_routes;
}

// 恢复静态路由表
bool NetworkManager::restore_static_routes(const std::vector<std::string> & routes)
{
  RCLCPP_INFO(logger_, "[ROUTE] 恢复静态路由表，共 %zu 条路由", routes.size());

  bool success = true;

  try {
    for (const auto & route : routes) {
      if (route.empty()) continue;

      // 构造添加路由的命令
      std::string add_cmd = "ip route add " + route + " 2>/dev/null || true";

      RCLCPP_DEBUG(logger_, "[ROUTE] 恢复路由: %s", route.c_str());

      std::string result = utils::NetworkUtils::exec_command(add_cmd);

      // 检查是否有错误（忽略已存在的路由）
      if (!result.empty() &&
          result.find("File exists") == std::string::npos &&
          result.find("RTNETLINK answers") != std::string::npos) {
        RCLCPP_WARN(logger_, "[ROUTE] 恢复路由失败: %s, 错误: %s",
          route.c_str(), result.c_str());
        success = false;
      }
    }

    if (success) {
      RCLCPP_INFO(logger_, "[ROUTE] 静态路由表恢复完成");
    } else {
      RCLCPP_WARN(logger_, "[ROUTE] 部分静态路由恢复失败");
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE] 恢复静态路由异常: %s", e.what());
    success = false;
  }

  return success;
}

// 安全的默认路由更新函数
bool NetworkManager::safe_update_default_route(const std::string & interface_name, int priority)
{
  RCLCPP_INFO(logger_, "[ROUTE] 安全更新接口 %s 默认路由，优先级: %d",
    interface_name.c_str(), priority);

  try {
    // 1. 获取接口信息
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    std::string interface_ip;
    std::string interface_gateway;

    for (const auto & iface : interfaces) {
      if (iface.name == interface_name) {
        interface_ip = iface.ip_address;
        interface_gateway = iface.gateway;
        break;
      }
    }

    if (interface_ip.empty()) {
      RCLCPP_WARN(logger_, "[ROUTE] 接口 %s 没有IP地址，跳过路由更新",
        interface_name.c_str());
      return false;
    }

    if (interface_gateway.empty()) {
      RCLCPP_WARN(logger_, "[ROUTE] 接口 %s 没有网关信息，跳过路由更新",
        interface_name.c_str());
      return false;
    }

    // 2. 检查当前是否已有该接口的默认路由
    std::string check_cmd = "ip route show default dev " + interface_name;
    std::string existing_route = utils::NetworkUtils::exec_command(check_cmd);

    // 3. 构建路由命令
    std::string route_cmd;
    if (!existing_route.empty()) {
      // 已存在路由，使用replace更新优先级
      route_cmd = "ip route replace default via " + interface_gateway +
                 " dev " + interface_name + " metric " + std::to_string(priority);
      RCLCPP_INFO(logger_, "[ROUTE] 替换现有默认路由");
    } else {
      // 不存在路由，添加新路由
      route_cmd = "ip route add default via " + interface_gateway +
                 " dev " + interface_name + " metric " + std::to_string(priority);
      RCLCPP_INFO(logger_, "[ROUTE] 添加新默认路由");
    }

    // 4. 执行路由命令
    std::string result = utils::NetworkUtils::exec_command(route_cmd);

    // 5. 检查执行结果
    if (!result.empty()) {
      if (result.find("RTNETLINK answers: File exists") != std::string::npos) {
        RCLCPP_DEBUG(logger_, "[ROUTE] 路由已存在，这是正常情况");
      } else if (result.find("RTNETLINK answers") != std::string::npos) {
        RCLCPP_WARN(logger_, "[ROUTE] 路由命令执行警告: %s", result.c_str());
        return false;
      }
    }

    // 6. 验证路由设置
    std::string verify_cmd = "ip route show default dev " + interface_name;
    std::string verify_result = utils::NetworkUtils::exec_command(verify_cmd);

    if (!verify_result.empty()) {
      RCLCPP_INFO(logger_, "[ROUTE] 默认路由设置成功: %s", verify_result.c_str());
      return true;
    } else {
      RCLCPP_ERROR(logger_, "[ROUTE] 默认路由设置失败，验证无结果");
      return false;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE] 安全路由更新异常: %s", e.what());
    return false;
  }
}

void NetworkManager::check_auto_switch_conditions(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  RCLCPP_DEBUG(logger_, "[AUTO] 开始科学化自动切换条件检查");

  try {
    // === 1. 科学化网络质量评估 ===
    auto switch_decision = evaluate_switch_necessity(quality);

    bool should_switch = switch_decision.should_switch;
    std::string reason = switch_decision.reason;
    double confidence = switch_decision.confidence;

    RCLCPP_DEBUG(logger_, "[AUTO] 切换决策 - 需要切换: %s, 置信度: %.2f, 原因: %s",
                 should_switch ? "是" : "否", confidence, reason.c_str());

    // === 2. 防止频繁切换检查 ===
    if (should_switch && should_prevent_frequent_switching()) {
      RCLCPP_INFO(logger_, "[AUTO] 检测到切换需求，但为防止频繁切换暂时跳过");
      return;
    }

    if (should_switch) {
      RCLCPP_WARN(logger_, "[AUTO] 科学评估检测到网络质量问题 (置信度: %.2f): %s",
                  confidence, reason.c_str());

      // === 3. 智能目标网络选择 ===
      uint8_t current_type = quality.network_type;
      uint8_t target_type = switch_decision.recommended_network_type;

      // 如果决策系统没有推荐，使用传统方法
      if (target_type == 0) {
        target_type = get_preferred_network_type(current_type);
      }

      if (target_type != current_type) {
        RCLCPP_INFO(node_->get_logger(), "[AUTO] 科学决策推荐切换到网络类型: %d (置信度: %.2f)",
                    target_type, confidence);

        // 根据目标网络类型选择合适的参数
        std::string target_wifi_ssid = "";
        if (target_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
          target_wifi_ssid = get_best_available_wifi();
          if (target_wifi_ssid.empty()) {
            RCLCPP_WARN(node_->get_logger(), "[AUTO] 没有找到可用的WiFi网络，取消自动切换");
            return;
          }
          RCLCPP_INFO(node_->get_logger(), "[AUTO] 选择WiFi网络: %s", target_wifi_ssid.c_str());
        }

        // === 4. 执行科学化自动切换 ===
        bool switch_result = switch_to_network(target_type, target_wifi_ssid, false);

        if (switch_result) {
          RCLCPP_INFO(node_->get_logger(), "[AUTO] 科学化自动网络切换成功");
          // 记录切换时间，用于防止频繁切换
          last_switch_time_ = std::chrono::steady_clock::now();
        } else {
          RCLCPP_ERROR(node_->get_logger(), "[AUTO] 科学化自动网络切换失败");
        }
      } else {
        RCLCPP_WARN(logger_, "[AUTO] 当前已是推荐的网络类型，无法切换");
      }
    } else {
      RCLCPP_DEBUG(logger_, "[AUTO] 科学评估显示网络质量良好，无需切换 (置信度: %.2f)", confidence);
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[AUTO] 检查自动切换条件异常: %s", e.what());
  }
}

uint8_t NetworkManager::get_preferred_network_type(uint8_t current_type)
{
  // 根据配置的首选网络类型返回目标类型
  // 1 = WiFi, 2 = Ethernet, 3 = 5G

  switch (preferred_network_type_) {
    case 1: // WiFi优先
      if (current_type != gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
        return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;
      }
      // 如果当前就是WiFi，尝试切换到备用网络
      return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;

    case 2: // 以太网优先
      if (current_type != gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET) {
        return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET;
      }
      // 如果当前就是以太网，尝试切换到备用网络
      return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;

    case 3: // 5G优先
      if (current_type != gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
        return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;
      }
      // 如果当前就是5G，尝试切换到备用网络
      return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;

    default:
      // 默认切换到WiFi
      return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;
  }
}

std::string NetworkManager::get_best_available_wifi()
{
  RCLCPP_DEBUG(node_->get_logger(), "[AUTO] 开始搜索最佳可用WiFi网络");

  try {
    // 1. 获取当前可用的WiFi网络列表
    auto available_networks = utils::NetworkUtils::scan_wifi_networks();

    if (available_networks.empty()) {
      RCLCPP_WARN(node_->get_logger(), "[AUTO] 没有扫描到任何WiFi网络");
      return "";
    }

    RCLCPP_DEBUG(node_->get_logger(), "[AUTO] 扫描到 %zu 个WiFi网络", available_networks.size());

    // 2. 智能选择策略：优先选择开放网络，然后考虑信号强度
    std::string best_ssid = "";
    int best_signal = -100;  // 初始化为很弱的信号
    bool best_is_open = false;  // 是否是开放网络

    // 获取配置文件中的高优先级网络
    auto priority_networks = get_priority_wifi_networks();

    for (const auto & network : available_networks) {
      // 检查信号强度是否满足最低要求
      if (network.signal_strength >= min_signal_strength_) {

        // 检查是否是开放网络（无需密码）
        bool is_open = network.security_type.empty() ||
                       network.security_type == "NONE" ||
                       network.security_type == "--";

        // 检查是否是已知的高优先级网络
        int network_priority = 0;  // 默认优先级
        std::string network_password = "";  // 网络密码
        for (const auto & priority_net : priority_networks) {
          if (network.ssid == priority_net.ssid) {
            network_priority = priority_net.priority;
            network_password = priority_net.password;
            break;
          }
        }

        // 检查是否有已保存的密码
        bool has_saved_password = !get_saved_wifi_password(network.ssid).empty();

        // 选择策略：
        // 1. 优先选择有已保存密码的网络
        // 2. 其次选择开放网络（无需密码）
        // 3. 再次选择已知高优先级网络
        // 4. 最后考虑信号强度
        bool should_select = false;

        if (has_saved_password && !get_saved_wifi_password(best_ssid).empty()) {
          // 都有保存密码，比较优先级和信号强度
          if (network_priority > 0 && network_priority > best_signal) {
            should_select = true;
          } else if (network_priority == 0 && network.signal_strength > best_signal) {
            should_select = true;
          }
        } else if (has_saved_password && get_saved_wifi_password(best_ssid).empty()) {
          // 当前网络有保存密码，之前选择的没有
          should_select = true;
        } else if (!has_saved_password && get_saved_wifi_password(best_ssid).empty()) {
          // 都没有保存密码，按原来的逻辑选择
          if (is_open && !best_is_open) {
            // 当前是开放网络，之前选择的不是开放网络
            should_select = true;
          } else if (is_open == best_is_open) {
            // 同样的网络类型（都是开放或都需要密码）
            if (network_priority > 0 && network_priority > best_signal) {
              // 有更高的优先级
              should_select = true;
            } else if (network_priority == 0 && network.signal_strength > best_signal) {
              // 没有特殊优先级，但信号更强
              should_select = true;
            }
          }
        }

        if (should_select) {
          best_signal = network.signal_strength;
          best_ssid = network.ssid;
          best_is_open = is_open;
        }

        RCLCPP_DEBUG(node_->get_logger(), "[AUTO] 候选WiFi: %s, 信号强度: %ddBm, 优先级: %d, 开放网络: %s",
          network.ssid.c_str(), network.signal_strength, network_priority, is_open ? "是" : "否");
      } else {
        RCLCPP_DEBUG(node_->get_logger(), "[AUTO] 跳过信号弱的WiFi: %s, 信号强度: %ddBm (< %ddBm)",
          network.ssid.c_str(), network.signal_strength, min_signal_strength_);
      }
    }

    if (!best_ssid.empty()) {
      RCLCPP_INFO(node_->get_logger(), "[AUTO] 选择最佳WiFi网络: %s, 信号强度: %ddBm, 开放网络: %s",
        best_ssid.c_str(), best_signal, best_is_open ? "是" : "否");
      return best_ssid;
    } else {
      RCLCPP_WARN(node_->get_logger(), "[AUTO] 没有找到信号强度满足要求的WiFi网络 (最低要求: %ddBm)",
        min_signal_strength_);
      return "";
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(node_->get_logger(), "[AUTO] 搜索最佳WiFi网络异常: %s", e.what());
    return "";
  }
}

// === 科学化自动切换算法实现 ===

NetworkManager::SwitchDecision NetworkManager::evaluate_switch_necessity(
  const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 开始科学化切换必要性评估");

  SwitchDecision decision;
  decision.should_switch = false;
  decision.confidence = 0.0;
  decision.recommended_network_type = 0;

  std::vector<std::string> issues;
  double total_urgency = 0.0;

  // === 1. 关键故障检查（最高优先级） ===
  if (is_critical_failure(quality)) {
    decision.should_switch = true;
    decision.confidence = 0.95;
    decision.reason = "检测到关键网络故障";
    issues.push_back("关键故障");
    RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 检测到关键故障，强制切换");
    return decision;
  }

  // === 2. 计算切换紧急度 ===
  double urgency = calculate_switch_urgency(quality);
  total_urgency += urgency;

  RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 切换紧急度: %.3f", urgency);

  // === 3. 网络稳定性因子 ===
  double stability_factor = get_network_stability_factor(quality);

  RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 网络稳定性因子: %.3f", stability_factor);

  // === 4. 综合评估 ===
  // 基础切换阈值
  double base_threshold = 0.6;

  // 根据稳定性调整阈值
  double adjusted_threshold = base_threshold * stability_factor;

  // 根据网络类型调整阈值
  switch (quality.network_type) {
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
      adjusted_threshold *= 0.9;  // WiFi更容易切换
      break;
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
      adjusted_threshold *= 1.1;  // 5G切换更谨慎
      break;
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
      adjusted_threshold *= 1.2;  // 以太网切换最谨慎
      break;
  }

  // === 5. 做出决策 ===
  if (total_urgency > adjusted_threshold) {
    decision.should_switch = true;
    decision.confidence = std::min(0.95, total_urgency);

    // 构建详细原因
    std::string detailed_reason = "科学评估显示需要切换 (紧急度: " +
                                 std::to_string(total_urgency) +
                                 " > 阈值: " + std::to_string(adjusted_threshold) + ")";

    if (quality.overall_score < min_connectivity_score_) {
      issues.push_back("总评分过低(" + std::to_string(quality.overall_score) + ")");
    }

    if (quality.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI &&
        quality.signal_strength < min_signal_strength_) {
      issues.push_back("WiFi信号弱(" + std::to_string(quality.signal_strength) + "dBm)");
    }

    if (!quality.dns_working) {
      issues.push_back("DNS解析异常");
    }

    decision.reason = detailed_reason;
    decision.detailed_reasons = issues;

    // 推荐目标网络类型
    decision.recommended_network_type = get_preferred_network_type(quality.network_type);
  } else {
    decision.reason = "网络质量在可接受范围内 (紧急度: " +
                     std::to_string(total_urgency) +
                     " <= 阈值: " + std::to_string(adjusted_threshold) + ")";
  }

  RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 科学评估完成 - 切换: %s, 置信度: %.3f",
               decision.should_switch ? "是" : "否", decision.confidence);

  return decision;
}

double NetworkManager::calculate_switch_urgency(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  double urgency = 0.0;

  // === 1. 基于总评分的紧急度 ===
  if (quality.overall_score < min_connectivity_score_) {
    double score_deficit = (min_connectivity_score_ - quality.overall_score) / min_connectivity_score_;
    urgency += score_deficit * 0.4;  // 最大贡献40%
  }

  // === 2. 基于信号强度的紧急度（仅WiFi） ===
  if (quality.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
    if (quality.signal_strength < min_signal_strength_) {
      double signal_deficit = (min_signal_strength_ - quality.signal_strength) /
                             std::abs(min_signal_strength_);
      urgency += signal_deficit * 0.3;  // 最大贡献30%
    }
  }

  // === 3. 基于连通性的紧急度 ===
  double connectivity_urgency = 0.0;
  if (!quality.dns_working) connectivity_urgency += 0.25;  // DNS异常时增加紧急度
  urgency += connectivity_urgency;

  // === 4. 基于延迟和丢包的紧急度 ===
  if (quality.latency_ms > 200.0) {
    urgency += std::min(0.1, (quality.latency_ms - 200.0) / 1000.0);
  }

  if (quality.packet_loss_rate > 1.0) {
    urgency += std::min(0.1, quality.packet_loss_rate / 10.0);
  }

  return std::min(1.0, urgency);  // 限制在[0,1]范围内
}

bool NetworkManager::is_critical_failure(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  // 关键故障定义：完全无法连接或极度不稳定

  // 1. DNS解析完全失败
  if (!quality.dns_working) {
    return true;
  }

  // 2. 极高丢包率
  if (quality.packet_loss_rate > 50.0) {
    return true;
  }

  // 3. 极高延迟
  if (quality.latency_ms > 5000.0) {
    return true;
  }

  // 4. WiFi信号极弱
  if (quality.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI &&
      quality.signal_strength < -90) {
    return true;
  }

  return false;
}

double NetworkManager::get_network_stability_factor(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  double factor = 1.0;

  // 基于抖动的稳定性评估
  if (quality.jitter_ms > 0) {
    if (quality.jitter_ms < 10.0) {
      factor += 0.1;  // 低抖动奖励
    } else if (quality.jitter_ms > 50.0) {
      factor -= 0.2;  // 高抖动惩罚
    }
  }

  // 基于网络类型的稳定性
  switch (quality.network_type) {
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
      factor += 0.1;  // 以太网稳定性奖励
      break;
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
      factor -= 0.05; // WiFi稳定性略低
      break;
  }

  return std::max(0.5, std::min(1.5, factor));  // 限制在合理范围内
}

bool NetworkManager::should_prevent_frequent_switching()
{
  // 防止频繁切换：最少间隔30秒
  auto now = std::chrono::steady_clock::now();
  auto time_since_last_switch = std::chrono::duration_cast<std::chrono::seconds>(
    now - last_switch_time_).count();

  const int min_switch_interval = 30;  // 秒

  if (time_since_last_switch < min_switch_interval) {
    RCLCPP_DEBUG(logger_, "[AUTO_SCIENCE] 距离上次切换仅 %ld 秒，防止频繁切换",
                 time_since_last_switch);
    return true;
  }

  return false;
}

// === 异步网络质量处理实现 ===

void NetworkManager::process_quality_async(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  // 如果上一个异步任务还在进行中，跳过这次处理
  if (quality_processing_in_progress_.load()) {
    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 网络质量处理仍在进行中，跳过本次处理");
    return;
  }

  // 标记处理开始
  quality_processing_in_progress_.store(true);

  // 启动异步任务
  quality_processing_future_ = std::async(std::launch::async, [this, quality]() {
    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 开始异步网络质量处理");

    try {
      // 执行耗时的自动切换条件检查
      check_auto_switch_conditions(quality);

      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 异步网络质量处理完成");
    } catch (const std::exception& e) {
      RCLCPP_ERROR(logger_, "[ASYNC_QUALITY] 异步网络质量处理异常: %s", e.what());
    }

    // 标记处理完成
    quality_processing_in_progress_.store(false);
  });
}

void NetworkManager::check_async_quality_processing()
{
  // 检查异步质量处理结果
  if (quality_processing_future_.valid()) {
    auto status = quality_processing_future_.wait_for(std::chrono::milliseconds(0));
    if (status == std::future_status::ready) {
      try {
        quality_processing_future_.get();  // 获取结果，处理可能的异常
        RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 异步质量处理任务已完成");
      } catch (const std::exception& e) {
        RCLCPP_ERROR(logger_, "[ASYNC_QUALITY] 获取异步质量处理结果异常: %s", e.what());
      }
    }
  }
}

// NAT规则管理函数
bool NetworkManager::update_nat_rules(const std::string & wan_interface) {
  RCLCPP_INFO(logger_, "[NAT] 更新NAT规则 - 外网接口: %s", wan_interface.c_str());

  // 检查是否启用NAT功能
  if (!enable_nat_) {
    RCLCPP_DEBUG(logger_, "[NAT] NAT功能已禁用，跳过规则更新");
    return true;
  }

  // 检查是否有配置的内网卡
  if (lan_interfaces_.empty()) {
    RCLCPP_WARN(logger_, "[NAT] 未配置内网卡，跳过NAT规则设置");
    return true;
  }

  try {
    // 使用iptables工具设置NAT规则
    bool result = utils::IptablesUtils::setup_nat_for_interface(wan_interface, lan_interfaces_);

    if (result) {
      RCLCPP_INFO(logger_, "[NAT] NAT规则更新成功 - 外网接口: %s", wan_interface.c_str());

      // 输出配置的内网卡信息
      std::string lan_list;
      for (size_t i = 0; i < lan_interfaces_.size(); ++i) {
        if (i > 0) lan_list += ", ";
        lan_list += lan_interfaces_[i];
      }
      RCLCPP_INFO(logger_, "[NAT] 内网卡转发已配置: [%s] -> %s", lan_list.c_str(), wan_interface.c_str());
    } else {
      RCLCPP_ERROR(logger_, "[NAT] NAT规则更新失败 - 外网接口: %s", wan_interface.c_str());
    }

    return result;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[NAT] 更新NAT规则异常: %s", e.what());
    return false;
  }
}

// 初始化NAT规则函数
void NetworkManager::initialize_nat_rules() {
  RCLCPP_INFO(logger_, "[NAT_INIT] 开始初始化NAT规则");

  // 检查是否启用NAT功能
  if (!enable_nat_) {
    RCLCPP_INFO(logger_, "[NAT_INIT] NAT功能已禁用，跳过初始化");
    return;
  }

  // 检查是否有配置的内网卡
  if (lan_interfaces_.empty()) {
    RCLCPP_WARN(logger_, "[NAT_INIT] 未配置内网卡，跳过NAT规则初始化");
    return;
  }

  try {
    // 获取当前网络状态
    std::string current_interface;
    uint8_t current_network_type;

    {
      std::lock_guard<std::mutex> lock(status_mutex_);
      current_interface = current_status_.interface_name;
      current_network_type = current_status_.network_type;
    }

    // 检查当前接口是否为外网卡（WiFi或5G）
    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    std::string fiveg_interface = get_current_5g_interface();

    bool is_wan_interface = false;
    std::string wan_type = "未知";

    if (current_interface == wifi_interface &&
        current_network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      is_wan_interface = true;
      wan_type = "WiFi";
    } else if (current_interface == fiveg_interface &&
               current_network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
      is_wan_interface = true;
      wan_type = "5G";
    }

    if (is_wan_interface) {
      RCLCPP_INFO(logger_, "[NAT_INIT] 检测到当前外网接口: %s (%s)",
                  current_interface.c_str(), wan_type.c_str());

      // 为当前外网接口配置NAT规则
      bool result = update_nat_rules(current_interface);

      if (result) {
        RCLCPP_INFO(logger_, "[NAT_INIT] NAT规则初始化成功 - 外网接口: %s (%s)",
                    current_interface.c_str(), wan_type.c_str());
      } else {
        RCLCPP_ERROR(logger_, "[NAT_INIT] NAT规则初始化失败 - 外网接口: %s (%s)",
                     current_interface.c_str(), wan_type.c_str());
      }
    } else {
      RCLCPP_WARN(logger_, "[NAT_INIT] 当前接口不是外网卡或网络类型不匹配");
      RCLCPP_WARN(logger_, "[NAT_INIT] 当前接口: %s, 网络类型: %d",
                  current_interface.c_str(), current_network_type);
      RCLCPP_WARN(logger_, "[NAT_INIT] WiFi接口: %s, 5G接口: %s",
                  wifi_interface.c_str(), fiveg_interface.c_str());

      // 即使当前不是外网卡，也尝试清理可能存在的旧NAT规则
      RCLCPP_INFO(logger_, "[NAT_INIT] 清理可能存在的旧NAT规则");
      utils::IptablesUtils::cleanup_nat_rules();
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[NAT_INIT] 初始化NAT规则异常: %s", e.what());
  }

  RCLCPP_INFO(logger_, "[NAT_INIT] NAT规则初始化完成");
}

// 检测并设置5G网卡接口
void NetworkManager::detect_and_set_5g_interface()
{
  std::lock_guard<std::mutex> lock(fiveg_interface_mutex_);

  RCLCPP_INFO(logger_, "[5G_DETECT] 开始检测可用的5G网卡");

  // 使用NetworkUtils选择可用的5G网卡
  current_5g_interface_ = utils::NetworkUtils::select_available_5g_interface(configured_5g_interfaces_);

  if (current_5g_interface_.empty()) {
    // 如果没有找到可用的5G网卡，使用第一个候选作为默认值
    if (!configured_5g_interfaces_.empty()) {
      current_5g_interface_ = configured_5g_interfaces_[0];
      RCLCPP_WARN(logger_, "[5G_DETECT] 未找到可用的5G网卡，使用默认值: %s", current_5g_interface_.c_str());
    } else {
      current_5g_interface_ = "eth2";  // 传统默认值
      RCLCPP_ERROR(logger_, "[5G_DETECT] 5G网卡候选列表为空，使用传统默认值: %s", current_5g_interface_.c_str());
    }
  } else {
    RCLCPP_INFO(logger_, "[5G_DETECT] 选择的5G网卡: %s", current_5g_interface_.c_str());
  }

  // 记录配置的候选列表
  std::string candidates_str;
  for (size_t i = 0; i < configured_5g_interfaces_.size(); ++i) {
    if (i > 0) candidates_str += ", ";
    candidates_str += configured_5g_interfaces_[i];
  }
  RCLCPP_INFO(logger_, "[5G_DETECT] 配置的5G网卡候选列表: [%s]", candidates_str.c_str());
}

// 获取当前5G网卡接口
std::string NetworkManager::get_current_5g_interface() const
{
  std::lock_guard<std::mutex> lock(fiveg_interface_mutex_);
  return current_5g_interface_;
}

std::string NetworkManager::get_saved_wifi_password(const std::string & ssid)
{
  try {
    // 获取配置文件路径
    std::string config_file = "/tmp/wifi_networks.conf";

    // 打开配置文件
    std::ifstream file(config_file);
    if (!file.is_open()) {
      RCLCPP_DEBUG(logger_, "[WIFI] 无法打开WiFi配置文件: %s", config_file.c_str());
      return "";
    }

    // 读取配置
    std::string line;
    std::string current_ssid = "";
    std::string current_password = "";
    bool in_network_section = false;

    while (std::getline(file, line)) {
      // 跳过空行和注释
      if (line.empty() || line[0] == '#') {
        continue;
      }

      // 检查是否是新的网络节
      if (line == "[network]") {
        // 如果找到了目标网络，返回密码
        if (in_network_section && current_ssid == ssid) {
          file.close();
          RCLCPP_DEBUG(logger_, "[WIFI] 找到已保存的WiFi密码: %s", ssid.c_str());
          return current_password;
        }

        // 开始新的网络
        current_ssid = "";
        current_password = "";
        in_network_section = true;
        continue;
      }

      // 解析键值对
      size_t pos = line.find('=');
      if (pos != std::string::npos) {
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        if (key == "ssid") {
          current_ssid = value;
        } else if (key == "password") {
          current_password = value;
        }
      }
    }

    // 检查最后一个网络
    if (in_network_section && current_ssid == ssid) {
      file.close();
      RCLCPP_DEBUG(logger_, "[WIFI] 找到已保存的WiFi密码: %s", ssid.c_str());
      return current_password;
    }

    file.close();
    RCLCPP_DEBUG(logger_, "[WIFI] 未找到已保存的WiFi密码: %s", ssid.c_str());
    return "";

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[WIFI] 读取WiFi配置异常: %s", e.what());
    return "";
  }
}

void NetworkManager::load_priority_wifi_networks()
{
  std::lock_guard<std::mutex> lock(priority_networks_mutex_);

  try {
    // 清空现有配置
    priority_wifi_networks_.clear();

    // 从参数系统读取高优先级WiFi网络配置
    auto ssids = node_->get_parameter("priority_wifi_networks.ssids").as_string_array();
    auto passwords = node_->get_parameter("priority_wifi_networks.passwords").as_string_array();
    auto priorities = node_->get_parameter("priority_wifi_networks.priorities").as_integer_array();
    auto security_types = node_->get_parameter("priority_wifi_networks.security_types").as_string_array();

    // 检查数组长度是否一致
    if (ssids.size() != passwords.size() ||
        ssids.size() != priorities.size() ||
        ssids.size() != security_types.size()) {
      RCLCPP_WARN(logger_, "[CONFIG] 高优先级WiFi网络配置数组长度不一致，使用默认配置");

      // 使用默认配置
      priority_wifi_networks_.push_back({"K3_5g", "your_password_here", 100, "WPA2"});
      priority_wifi_networks_.push_back({"Office_WiFi", "office_password", 90, "WPA2"});
      priority_wifi_networks_.push_back({"ZYHY-Guest", "", 80, "OPEN"});
      priority_wifi_networks_.push_back({"CMCC", "cmcc_password", 70, "WPA2"});
      priority_wifi_networks_.push_back({"Guest_WiFi", "", 50, "OPEN"});
    } else {
      // 从配置文件加载
      for (size_t i = 0; i < ssids.size(); ++i) {
        PriorityWiFiNetwork network;
        network.ssid = ssids[i];
        network.password = passwords[i];
        network.priority = static_cast<int>(priorities[i]);
        network.security_type = security_types[i];

        priority_wifi_networks_.push_back(network);
      }
    }

    RCLCPP_INFO(logger_, "[CONFIG] 加载了 %zu 个高优先级WiFi网络配置",
                priority_wifi_networks_.size());

    // 输出加载的网络信息
    for (const auto & network : priority_wifi_networks_) {
      RCLCPP_INFO(logger_, "[CONFIG] 高优先级WiFi: %s (优先级: %d, 安全类型: %s)",
                  network.ssid.c_str(), network.priority, network.security_type.c_str());
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[CONFIG] 加载高优先级WiFi网络配置异常: %s，使用默认配置", e.what());

    // 异常时使用默认配置
    priority_wifi_networks_.clear();
    priority_wifi_networks_.push_back({"K3_5g", "your_password_here", 100, "WPA2"});
    priority_wifi_networks_.push_back({"Office_WiFi", "office_password", 90, "WPA2"});
    priority_wifi_networks_.push_back({"ZYHY-Guest", "", 80, "OPEN"});
    priority_wifi_networks_.push_back({"CMCC", "cmcc_password", 70, "WPA2"});
    priority_wifi_networks_.push_back({"Guest_WiFi", "", 50, "OPEN"});
  }
}

std::vector<NetworkManager::PriorityWiFiNetwork> NetworkManager::get_priority_wifi_networks() const
{
  std::lock_guard<std::mutex> lock(priority_networks_mutex_);
  return priority_wifi_networks_;
}

}  // namespace gen3_network_manager_core