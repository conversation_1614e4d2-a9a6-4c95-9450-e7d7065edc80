// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/nr90_serial_comm.hpp"
#include "gen3_network_manager_core/usb_at_comm.hpp"

#include <fcntl.h>
#include <unistd.h>
#include <sys/select.h>
#include <sys/time.h>
#include <errno.h>
#include <cstring>
#include <regex>
#include <algorithm>
#include <sstream>
#include <thread>

namespace gen3_network_manager_core
{

// 预定义的AT命令列表
const std::vector<Nr90AtCommand> Nr90SerialComm::at_commands_ = {
  Nr90AtCommand("AT", "基本AT命令测试", 3, true),
  Nr90AtCommand("ATI", "查询设备信息", 3, true),
  Nr90AtCommand("AT+CIMI", "查询IMSI", 3, false),
  Nr90AtCommand("AT+COPS?", "查询当前网络运营商", 5, true),
  Nr90AtCommand("AT+CREG?", "查询网络注册状态", 3, true),
  Nr90AtCommand("AT+CGREG?", "查询GPRS网络注册状态", 3, false),
  Nr90AtCommand("AT+CEREG?", "查询EPS网络注册状态", 3, true),
  Nr90AtCommand("AT+C5GREG?", "查询5G网络注册状态", 5, true),
  Nr90AtCommand("AT+CPSI?", "查询系统信息", 5, true),
  Nr90AtCommand("AT+QNWINFO", "查询网络信息", 5, false),
  Nr90AtCommand("AT+QENG=\"servingcell\"", "查询服务小区信息", 8, false),
  Nr90AtCommand("AT+CSQ", "查询信号强度", 3, true),
  Nr90AtCommand("AT+LUSBNETDIAL?", "查询USB网卡拨号状态", 5, true),
  Nr90AtCommand("AT+LUSBNETDIAL=1", "启用USB网卡拨号模式", 10, true),
  Nr90AtCommand("AT+LUSBNETDIAL=0", "禁用USB网卡拨号模式", 10, true),
  Nr90AtCommand("AT+LNWINFO", "查询网络信息", 5, true),
  Nr90AtCommand("AT+LSERVCELINFO?", "查询服务小区信息", 5, true),
  Nr90AtCommand("AT+LNWSCFG?", "查询网络搜索偏好", 3, true),
  Nr90AtCommand("AT+LNWSCFG=0", "设置网络搜索为AUTO模式", 3, true),
  Nr90AtCommand("AT+LNWSCFG=2", "设置网络搜索为LTE模式", 3, true),
  Nr90AtCommand("AT+LNWSCFG=4", "设置网络搜索为5G SA模式", 3, true),
  Nr90AtCommand("AT+LNWSCFG=6", "设置网络搜索为5G SA+LTE模式", 3, true)
};

Nr90SerialComm::Nr90SerialComm(const rclcpp::Node::SharedPtr & node)
: node_(node), fd_(-1), baud_rate_(115200), is_configured_(false),
  usb_at_comm_(nullptr), use_usb_fallback_(false)
{
  log_info("Nr90SerialComm 初始化");

  // 初始化 USB AT 通信备用方案
  usb_at_comm_ = std::make_unique<UsbAtComm>(node);
}

Nr90SerialComm::~Nr90SerialComm()
{
  cleanup();
  log_info("Nr90SerialComm 析构");
}

Nr90Error Nr90SerialComm::init(const std::string & device_path, int baud_rate)
{
  device_path_ = device_path;
  baud_rate_ = baud_rate;

  log_info("初始化Nr90串口通信: " + device_path + ", 波特率: " + std::to_string(baud_rate));

  // 首先尝试串口通信
  Nr90Error result = open_device();
  if (result == Nr90Error::SUCCESS) {
    // 配置串口参数
    result = configure_device();
    if (result == Nr90Error::SUCCESS) {
      log_info("✓ 串口通信初始化成功");
      use_usb_fallback_ = false;
      return Nr90Error::SUCCESS;
    } else {
      log_warn("串口配置失败: " + error_to_string(result));
      close_device(); // 关闭已打开的设备
    }
  }

  // 串口失败，尝试 USB AT 通信备用方案
  log_warn("串口通信失败，尝试 USB AT 通信备用方案");
  if (usb_at_comm_ && usb_at_comm_->init()) {
    log_info("✓ USB AT 通信备用方案初始化成功");
    use_usb_fallback_ = true;
    return Nr90Error::SUCCESS;
  }

  log_error("❌ 所有通信方式都失败");
  return Nr90Error::DEVICE_OPEN_FAILED;
}

Nr90Error Nr90SerialComm::open_device()
{
  if (fd_ >= 0) {
    log_warn("设备已经打开");
    return Nr90Error::SUCCESS;
  }
  
  log_info("正在打开设备: " + device_path_);
  
  // 打开串口设备
  fd_ = open(device_path_.c_str(), O_RDWR | O_NOCTTY | O_NONBLOCK);
  if (fd_ < 0) {
    log_error("无法打开设备 " + device_path_ + ": " + std::string(strerror(errno)));
    return Nr90Error::DEVICE_OPEN_FAILED;
  }
  
  // 保存原始串口设置
  if (tcgetattr(fd_, &original_termios_) != 0) {
    log_warn("无法获取原始串口设置: " + std::string(strerror(errno)));
  }
  
  log_info("设备打开成功，文件描述符: " + std::to_string(fd_));
  return Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::configure_device()
{
  if (fd_ < 0) {
    return Nr90Error::DEVICE_OPEN_FAILED;
  }
  
  log_info("正在配置串口参数...");
  
  struct termios tty;
  memset(&tty, 0, sizeof(tty));
  
  // 获取当前设置
  if (tcgetattr(fd_, &tty) != 0) {
    log_error("获取串口属性失败: " + std::string(strerror(errno)));
    return Nr90Error::DEVICE_CONFIG_FAILED;
  }
  
  // 设置波特率
  speed_t speed;
  switch (baud_rate_) {
    case 9600: speed = B9600; break;
    case 19200: speed = B19200; break;
    case 38400: speed = B38400; break;
    case 57600: speed = B57600; break;
    case 115200: speed = B115200; break;
    default:
      log_error("不支持的波特率: " + std::to_string(baud_rate_));
      return Nr90Error::DEVICE_CONFIG_FAILED;
  }
  
  cfsetospeed(&tty, speed);
  cfsetispeed(&tty, speed);
  
  // 设置数据位、停止位、校验位
  tty.c_cflag &= ~PARENB;        // 无校验位
  tty.c_cflag &= ~CSTOPB;        // 1个停止位
  tty.c_cflag &= ~CSIZE;         // 清除数据位设置
  tty.c_cflag |= CS8;            // 8个数据位
  tty.c_cflag &= ~CRTSCTS;       // 禁用硬件流控制
  tty.c_cflag |= CREAD | CLOCAL; // 启用接收，忽略调制解调器控制线
  
  // 设置输入模式
  tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 禁用软件流控制
  tty.c_iflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始输入
  
  // 设置输出模式
  tty.c_oflag &= ~OPOST; // 原始输出
  
  // 设置本地模式
  tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始模式
  
  // 设置超时
  tty.c_cc[VMIN] = 0;   // 最小字符数
  tty.c_cc[VTIME] = 10; // 超时时间(0.1秒单位)
  
  // 应用设置
  if (tcsetattr(fd_, TCSANOW, &tty) != 0) {
    log_error("设置串口属性失败: " + std::string(strerror(errno)));
    return Nr90Error::DEVICE_CONFIG_FAILED;
  }
  
  // 清空缓冲区
  tcflush(fd_, TCIOFLUSH);
  
  is_configured_ = true;
  log_info("串口配置完成 (" + std::to_string(baud_rate_) + "-8-N-1)");
  
  return Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::close_device()
{
  if (fd_ >= 0) {
    log_info("正在关闭设备...");
    
    // 恢复原始串口设置
    if (is_configured_) {
      tcsetattr(fd_, TCSANOW, &original_termios_);
    }
    
    close(fd_);
    fd_ = -1;
    is_configured_ = false;
    
    log_info("设备已关闭");
  }
  
  return Nr90Error::SUCCESS;
}

void Nr90SerialComm::cleanup()
{
  close_device();
}

Nr90Error Nr90SerialComm::send_command(const std::string & command)
{
  if (fd_ < 0) {
    return Nr90Error::DEVICE_OPEN_FAILED;
  }
  
  std::string cmd_with_crlf = command + "\r\n";
  log_debug("发送命令: " + command);
  
  // 清空输入缓冲区
  tcflush(fd_, TCIFLUSH);
  
  // 发送命令
  ssize_t bytes_written = write(fd_, cmd_with_crlf.c_str(), cmd_with_crlf.length());
  if (bytes_written != static_cast<ssize_t>(cmd_with_crlf.length())) {
    log_error("命令发送失败: " + std::string(strerror(errno)) + 
              " (写入 " + std::to_string(bytes_written) + "/" + 
              std::to_string(cmd_with_crlf.length()) + " 字节)");
    return Nr90Error::COMMAND_SEND_FAILED;
  }
  
  // 确保数据发送完成
  tcdrain(fd_);
  
  log_debug("命令发送成功: " + std::to_string(bytes_written) + " 字节");
  return Nr90Error::SUCCESS;
}

// 工具函数实现
void Nr90SerialComm::log_debug(const std::string & message)
{
  RCLCPP_DEBUG(node_->get_logger(), "[NR90] %s", message.c_str());
}

void Nr90SerialComm::log_info(const std::string & message)
{
  RCLCPP_INFO(node_->get_logger(), "[NR90] %s", message.c_str());
}

void Nr90SerialComm::log_warn(const std::string & message)
{
  RCLCPP_WARN(node_->get_logger(), "[NR90] %s", message.c_str());
}

void Nr90SerialComm::log_error(const std::string & message)
{
  RCLCPP_ERROR(node_->get_logger(), "[NR90] %s", message.c_str());
}

std::string Nr90SerialComm::trim(const std::string & str)
{
  size_t start = str.find_first_not_of(" \t\r\n");
  if (start == std::string::npos) return "";
  
  size_t end = str.find_last_not_of(" \t\r\n");
  return str.substr(start, end - start + 1);
}

std::vector<std::string> Nr90SerialComm::split(const std::string & str, char delimiter)
{
  std::vector<std::string> tokens;
  std::stringstream ss(str);
  std::string token;
  
  while (std::getline(ss, token, delimiter)) {
    tokens.push_back(trim(token));
  }
  
  return tokens;
}

bool Nr90SerialComm::contains_ignore_case(const std::string & haystack, const std::string & needle)
{
  std::string haystack_lower = haystack;
  std::string needle_lower = needle;
  
  std::transform(haystack_lower.begin(), haystack_lower.end(), haystack_lower.begin(), ::tolower);
  std::transform(needle_lower.begin(), needle_lower.end(), needle_lower.begin(), ::tolower);
  
  return haystack_lower.find(needle_lower) != std::string::npos;
}

std::string Nr90SerialComm::error_to_string(Nr90Error error)
{
  switch (error) {
    case Nr90Error::SUCCESS:
      return "Success";
    case Nr90Error::DEVICE_OPEN_FAILED:
      return "Device open failed";
    case Nr90Error::DEVICE_CONFIG_FAILED:
      return "Device configuration failed";
    case Nr90Error::COMMAND_SEND_FAILED:
      return "Command send failed";
    case Nr90Error::RESPONSE_TIMEOUT:
      return "Response timeout";
    case Nr90Error::RESPONSE_INVALID:
      return "Invalid response";
    case Nr90Error::DEVICE_NOT_READY:
      return "Device not ready";
    case Nr90Error::MEMORY_ALLOC_FAILED:
      return "Memory allocation failed";
    case Nr90Error::INVALID_PARAM:
      return "Invalid parameter";
    default:
      return "Unknown error";
  }
}

bool Nr90SerialComm::is_response_ok(const std::string & response)
{
  return response.find("OK") != std::string::npos;
}

bool Nr90SerialComm::is_response_error(const std::string & response)
{
  return (response.find("ERROR") != std::string::npos ||
          response.find("+CME ERROR") != std::string::npos ||
          response.find("+CMS ERROR") != std::string::npos);
}

Nr90Error Nr90SerialComm::read_response(Nr90Response & response, int timeout_seconds)
{
  if (fd_ < 0) {
    return Nr90Error::DEVICE_OPEN_FAILED;
  }

  response = Nr90Response(); // 重置响应

  fd_set read_fds;
  struct timeval tv;
  char buffer[256];
  auto start_time = std::chrono::steady_clock::now();

  log_debug("等待响应 (超时: " + std::to_string(timeout_seconds) + "秒)...");

  while (response.response.length() < 2048) { // 最大响应长度
    // 检查超时
    auto elapsed = std::chrono::steady_clock::now() - start_time;
    if (elapsed >= std::chrono::seconds(timeout_seconds)) {
      log_warn("读取响应超时");
      return Nr90Error::RESPONSE_TIMEOUT;
    }

    FD_ZERO(&read_fds);
    FD_SET(fd_, &read_fds);

    tv.tv_sec = 1;
    tv.tv_usec = 0;

    int select_result = select(fd_ + 1, &read_fds, nullptr, nullptr, &tv);

    if (select_result < 0) {
      if (errno == EINTR) continue; // 被信号中断，继续
      log_error("select失败: " + std::string(strerror(errno)));
      return Nr90Error::RESPONSE_TIMEOUT;
    }

    if (select_result == 0) {
      continue; // 超时，继续等待
    }

    if (FD_ISSET(fd_, &read_fds)) {
      ssize_t bytes_read = read(fd_, buffer, sizeof(buffer) - 1);

      if (bytes_read < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
          continue; // 非阻塞读取，没有数据
        }
        log_error("读取失败: " + std::string(strerror(errno)));
        return Nr90Error::RESPONSE_TIMEOUT;
      }

      if (bytes_read == 0) {
        continue; // 没有数据
      }

      buffer[bytes_read] = '\0';
      response.response += std::string(buffer);
      response.length = response.response.length();

      // 检查是否收到完整响应
      if (response.response.find("OK") != std::string::npos ||
          response.response.find("ERROR") != std::string::npos ||
          response.response.find("+CME ERROR") != std::string::npos ||
          response.response.find("+CMS ERROR") != std::string::npos) {
        break;
      }
    }
  }

  // 分析响应
  response.is_ok = is_response_ok(response.response);
  response.is_error = is_response_error(response.response);

  if (response.length > 0) {
    log_debug("收到响应: " + std::to_string(response.length) + " 字节");
    return Nr90Error::SUCCESS;
  } else {
    log_warn("未收到有效响应");
    return Nr90Error::RESPONSE_INVALID;
  }
}

Nr90Error Nr90SerialComm::send_at_command(const std::string & command, Nr90Response & response,
                                         int timeout_seconds)
{
  log_info("执行AT命令: " + command);

  // 检查是否使用 USB AT 通信备用方案
  if (use_usb_fallback_ && usb_at_comm_) {
    log_debug("使用 USB AT 通信发送命令");

    std::string usb_response;
    bool success = usb_at_comm_->send_at_command(command, usb_response, timeout_seconds * 1000);

    if (success) {
      response.response = usb_response;
      response.is_ok = (usb_response.find("OK") != std::string::npos);
      response.is_error = (usb_response.find("ERROR") != std::string::npos);

      // 输出响应信息
      if (response.is_ok) {
        log_info("USB AT 命令执行成功");
      } else if (response.is_error) {
        log_warn("USB AT 命令执行失败");
      } else {
        log_info("USB AT 收到响应");
      }

      return Nr90Error::SUCCESS;
    } else {
      log_error("USB AT 命令发送失败");
      return Nr90Error::RESPONSE_INVALID;
    }
  }

  // 使用串口通信
  // 发送命令
  Nr90Error result = send_command(command);
  if (result != Nr90Error::SUCCESS) {
    return result;
  }

  // 等待一小段时间让设备处理命令
  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  // 读取响应
  result = read_response(response, timeout_seconds);
  if (result != Nr90Error::SUCCESS) {
    return result;
  }

  // 输出响应信息
  if (response.is_ok) {
    log_info("命令执行成功");
  } else if (response.is_error) {
    log_warn("命令执行失败");
  } else {
    log_info("收到响应");
  }

  return Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::test_basic_communication()
{
  log_info("=== 开始基本通信测试 ===");

  Nr90Response response;
  Nr90Error result = send_at_command("AT", response, 3);

  if (result != Nr90Error::SUCCESS) {
    log_error("基本AT命令测试失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("设备未响应OK");
    return Nr90Error::DEVICE_NOT_READY;
  }

  log_info("✓ 基本通信测试通过");
  return Nr90Error::SUCCESS;
}

bool Nr90SerialComm::is_device_ready()
{
  return test_basic_communication() == Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::get_signal_strength(int32_t & rssi, int32_t & rsrp, int32_t & rsrq)
{
  Nr90Response response;
  Nr90Error result = send_at_command("AT+CSQ", response, 3);

  if (result != Nr90Error::SUCCESS) {
    return result;
  }

  if (!response.is_ok) {
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析信号强度响应
  if (parse_signal_response(response.response, rssi, rsrp, rsrq)) {
    return Nr90Error::SUCCESS;
  } else {
    return Nr90Error::RESPONSE_INVALID;
  }
}

bool Nr90SerialComm::parse_signal_response(const std::string & response, int32_t & rssi,
                                          int32_t & rsrp, int32_t & rsrq)
{
  // 解析 +CSQ: <rssi>,<ber> 格式的响应
  std::regex csq_regex(R"(\+CSQ:\s*(\d+),(\d+))");
  std::smatch match;

  if (std::regex_search(response, match, csq_regex)) {
    int raw_rssi = std::stoi(match[1].str());

    // 转换RSSI值 (0-31 -> dBm)
    if (raw_rssi == 99) {
      rssi = -113; // 未知或无法检测
    } else if (raw_rssi >= 0 && raw_rssi <= 31) {
      rssi = -113 + raw_rssi * 2; // 转换为dBm
    } else {
      rssi = -113;
    }

    // 对于5G模块，可能需要额外的命令获取RSRP和RSRQ
    rsrp = rssi; // 简化处理
    rsrq = 0;    // 简化处理

    return true;
  }

  return false;
}

Nr90Error Nr90SerialComm::set_usb_dial_mode(int mode)
{
  log_info("设置USB网卡拨号模式: " + std::to_string(mode));

  if (mode != 0 && mode != 1) {
    log_error("无效的USB拨号模式: " + std::to_string(mode) + " (只支持0或1)");
    return Nr90Error::INVALID_PARAM;
  }

  std::string command = "AT+LUSBNETDIAL=" + std::to_string(mode);
  Nr90Response response;
  Nr90Error result = send_at_command(command, response, 10);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送USB拨号设置命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("USB拨号设置命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  log_info("✓ USB网卡拨号模式设置成功: " + std::to_string(mode));
  return Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::get_usb_dial_status(int & mode, std::string & status)
{
  log_info("查询USB网卡拨号状态");

  Nr90Response response;
  Nr90Error result = send_at_command("AT+LUSBNETDIAL?", response, 5);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送USB拨号查询命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("USB拨号查询命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析USB拨号状态响应
  if (parse_usb_dial_response(response.response, mode, status)) {
    log_info("✓ USB网卡拨号状态查询成功 - 模式: " + std::to_string(mode) + ", 状态: " + status);
    return Nr90Error::SUCCESS;
  } else {
    log_error("解析USB拨号状态响应失败: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }
}

bool Nr90SerialComm::parse_usb_dial_response(const std::string & response, int & mode, std::string & status)
{
  log_debug("解析USB拨号响应: " + response);

  // 解析 +LUSBNETDIAL: <mode>,<status> 格式的响应
  // 例如: +LUSBNETDIAL: 1,"CONNECTED" 或 +LUSBNETDIAL: 0,"DISCONNECTED"
  std::regex dial_regex(R"(\+LUSBNETDIAL:\s*(\d+),\"?([^\"]+)\"?)");
  std::smatch match;

  if (std::regex_search(response, match, dial_regex)) {
    mode = std::stoi(match[1].str());
    status = match[2].str();

    // 清理状态字符串
    status = trim(status);

    log_debug("解析成功 - 模式: " + std::to_string(mode) + ", 状态: " + status);
    return true;
  }

  // 尝试解析简化格式 +LUSBNETDIAL: <mode>
  std::regex simple_regex(R"(\+LUSBNETDIAL:\s*(\d+))");
  if (std::regex_search(response, match, simple_regex)) {
    mode = std::stoi(match[1].str());
    status = (mode == 1) ? "ENABLED" : "DISABLED";

    log_debug("解析成功(简化格式) - 模式: " + std::to_string(mode) + ", 状态: " + status);
    return true;
  }

  log_warn("无法解析USB拨号响应格式: " + response);
  return false;
}

Nr90Error Nr90SerialComm::get_network_info(std::string & access_tech, std::string & operator_code, int & band)
{
  log_info("查询网络信息");

  Nr90Response response;
  Nr90Error result = send_at_command("AT+LNWINFO", response, 5);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送网络信息查询命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("网络信息查询命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析网络信息响应
  if (parse_network_info_response(response.response, access_tech, operator_code, band)) {
    log_info("✓ 网络信息查询成功 - 技术: " + access_tech +
             ", 运营商: " + operator_code + ", 频段: " + std::to_string(band));
    return Nr90Error::SUCCESS;
  } else {
    log_error("解析网络信息响应失败: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }
}

bool Nr90SerialComm::parse_network_info_response(const std::string & response, std::string & access_tech,
                                                std::string & operator_code, int & band)
{
  log_debug("解析网络信息响应: " + response);

  // 解析 +LNWINFO: "<access_tech>","<operator_code>",<band> 格式的响应
  // 例如: +LNWINFO: "FDD LTE","46011",3
  std::regex nwinfo_regex(R"(\+LNWINFO:\s*\"([^\"]+)\",\"([^\"]+)\",(\d+))");
  std::smatch match;

  if (std::regex_search(response, match, nwinfo_regex)) {
    access_tech = match[1].str();
    operator_code = match[2].str();
    band = std::stoi(match[3].str());

    // 清理字符串
    access_tech = trim(access_tech);
    operator_code = trim(operator_code);

    log_debug("解析成功 - 技术: " + access_tech +
              ", 运营商: " + operator_code + ", 频段: " + std::to_string(band));
    return true;
  }

  // 尝试解析无引号格式 +LNWINFO: <access_tech>,<operator_code>,<band>
  std::regex simple_regex(R"(\+LNWINFO:\s*([^,]+),([^,]+),(\d+))");
  if (std::regex_search(response, match, simple_regex)) {
    access_tech = match[1].str();
    operator_code = match[2].str();
    band = std::stoi(match[3].str());

    // 清理字符串
    access_tech = trim(access_tech);
    operator_code = trim(operator_code);

    log_debug("解析成功(简化格式) - 技术: " + access_tech +
              ", 运营商: " + operator_code + ", 频段: " + std::to_string(band));
    return true;
  }

  log_warn("无法解析网络信息响应格式: " + response);
  return false;
}

Nr90Error Nr90SerialComm::get_serving_cell_info(Nr90ServingCellInfo & cell_info)
{
  log_info("查询服务小区信息");

  Nr90Response response;
  Nr90Error result = send_at_command("AT+LSERVCELINFO?", response, 5);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送服务小区信息查询命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("服务小区信息查询命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析服务小区信息响应
  if (parse_serving_cell_info_response(response.response, cell_info)) {
    log_info("✓ 服务小区信息查询成功 - 制式: " + cell_info.act_type +
             ", 状态: " + cell_info.state + ", RSSI: " + std::to_string(cell_info.rssi) + "dBm");
    return Nr90Error::SUCCESS;
  } else {
    log_error("解析服务小区信息响应失败: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }
}

bool Nr90SerialComm::parse_serving_cell_info_response(const std::string & response, Nr90ServingCellInfo & cell_info)
{
  log_debug("解析服务小区信息响应: " + response);

  // 查找 +LSERVCELINFO: 行
  std::string line;
  std::istringstream iss(response);
  bool found_info_line = false;

  while (std::getline(iss, line)) {
    if (line.find("+LSERVCELINFO:") != std::string::npos) {
      found_info_line = true;
      break;
    }
  }

  if (!found_info_line) {
    log_warn("未找到 +LSERVCELINFO: 响应行");
    return false;
  }

  // 移除 +LSERVCELINFO: 前缀
  size_t colon_pos = line.find(':');
  if (colon_pos == std::string::npos) {
    log_warn("响应格式错误，未找到冒号");
    return false;
  }

  std::string data = line.substr(colon_pos + 1);
  data = trim(data);

  // 解析逗号分隔的值
  std::vector<std::string> values;
  std::stringstream ss(data);
  std::string item;

  while (std::getline(ss, item, ',')) {
    // 移除引号和空格
    item = trim(item);
    if (item.front() == '"' && item.back() == '"') {
      item = item.substr(1, item.length() - 2);
    }
    values.push_back(item);
  }

  if (values.empty()) {
    log_warn("解析失败，未找到有效数据");
    return false;
  }

  try {
    // 第一个参数总是制式
    cell_info.act_type = values[0];

    if (cell_info.act_type == "LTE") {
      // LTE 模式标准格式（14个参数）:
      // <act_type>,<PCID>,<state>,<MCC>,<MNC>,<TAC>,<arfcn>,<band>,
      // <UL_bandwidth>,<DL_bandwidth>,<RSRP>,<RSRQ>,<RSSI>,<SINR>
      if (values.size() >= 12) {
        cell_info.pcid = std::stoi(values[1]);
        cell_info.state = values[2];
        cell_info.mcc = std::stoi(values[3]);
        cell_info.mnc = std::stoi(values[4]);

        // TAC可能是十六进制格式，需要特殊处理
        try {
          if (values[5].find_first_of("ABCDEFabcdef") != std::string::npos) {
            // 包含十六进制字符，按十六进制解析
            cell_info.tac = std::stoi(values[5], nullptr, 16);
            log_debug("TAC十六进制解析: " + values[5] + " -> " + std::to_string(cell_info.tac));
          } else {
            // 十进制解析
            cell_info.tac = std::stoi(values[5]);
          }
        } catch (const std::exception& e) {
          log_warn("TAC解析失败: " + values[5] + ", 错误: " + e.what());
          cell_info.tac = 0;
        }

        cell_info.arfcn = std::stoi(values[6]);
        cell_info.band = std::stoi(values[7]);
        cell_info.ul_bandwidth = std::stoi(values[8]);
        cell_info.dl_bandwidth = std::stoi(values[9]);
        cell_info.rsrp = std::stoi(values[10]);
        cell_info.rsrq = std::stoi(values[11]);

        // 处理可选的RSSI和SINR参数
        // 根据文档，标准格式应该有14个参数，但实际可能有差异
        if (values.size() == 13) {
          // 13个参数：可能是缺少RSSI或SINR中的一个
          // 根据数值范围判断第13个参数是RSSI还是SINR
          int param13 = std::stoi(values[12]);
          if (param13 >= -120 && param13 <= -30) {
            // 范围像RSSI (-120 ~ -30 dBm)
            cell_info.rssi = param13;
            log_debug("第13个参数识别为RSSI: " + std::to_string(param13) + "dBm");
          } else if (param13 >= -30 && param13 <= 50) {
            // 范围像SINR (-30 ~ 50 dB)
            cell_info.sinr = param13;
            log_debug("第13个参数识别为SINR: " + std::to_string(param13) + "dB");
          } else {
            // 无法确定，默认作为RSSI
            cell_info.rssi = param13;
            log_debug("第13个参数无法确定类型，默认作为RSSI: " + std::to_string(param13));
          }
        } else if (values.size() >= 14) {
          // 14个参数：标准格式，RSSI和SINR都存在
          cell_info.rssi = std::stoi(values[12]);
          cell_info.sinr = std::stoi(values[13]);
          log_debug("标准14参数格式 - RSSI: " + std::to_string(cell_info.rssi) +
                   "dBm, SINR: " + std::to_string(cell_info.sinr) + "dB");
        }

        // 构建详细的解析结果日志
        std::string debug_msg = "解析成功(" + cell_info.act_type + ") - ";
        debug_msg += "PCID: " + std::to_string(cell_info.pcid);
        debug_msg += ", 状态: " + cell_info.state;
        debug_msg += ", 运营商: " + std::to_string(cell_info.mcc) + "-" +
                    (cell_info.mnc < 10 ? "0" : "") + std::to_string(cell_info.mnc);
        debug_msg += ", TAC: " + std::to_string(cell_info.tac);
        debug_msg += ", ARFCN: " + std::to_string(cell_info.arfcn);
        debug_msg += ", 频段: " + std::to_string(cell_info.band);
        debug_msg += ", 带宽: UL" + std::to_string(cell_info.ul_bandwidth) +
                    "/DL" + std::to_string(cell_info.dl_bandwidth);
        debug_msg += ", RSRP: " + std::to_string(cell_info.rsrp) + "dBm";
        debug_msg += ", RSRQ: " + std::to_string(cell_info.rsrq) + "dB";

        if (cell_info.rssi != -999) {
          debug_msg += ", RSSI: " + std::to_string(cell_info.rssi) + "dBm";
        }
        if (cell_info.sinr != -999) {
          debug_msg += ", SINR: " + std::to_string(cell_info.sinr) + "dB";
        }

        debug_msg += " [参数数量: " + std::to_string(values.size()) + "]";
        log_debug(debug_msg);
        return true;
      }
    } else if (cell_info.act_type == "NR" || cell_info.act_type == "SA") {
      // SA/NR 模式标准格式（13个参数，无RSSI）:
      // <act_type>,<PCID>,<state>,<MCC>,<MNC>,<TAC>,<arfcn>,<band>,
      // <UL_bandwidth>,<DL_bandwidth>,<RSRP>,<RSRQ>,<SINR>
      if (values.size() >= 13) {
        cell_info.pcid = std::stoi(values[1]);
        cell_info.state = values[2];
        cell_info.mcc = std::stoi(values[3]);
        cell_info.mnc = std::stoi(values[4]);

        // TAC可能是十六进制格式，需要特殊处理
        try {
          if (values[5].find_first_of("ABCDEFabcdef") != std::string::npos) {
            // 包含十六进制字符，按十六进制解析
            cell_info.tac = std::stoi(values[5], nullptr, 16);
            log_debug("TAC十六进制解析: " + values[5] + " -> " + std::to_string(cell_info.tac));
          } else {
            // 十进制解析
            cell_info.tac = std::stoi(values[5]);
          }
        } catch (const std::exception& e) {
          log_warn("TAC解析失败: " + values[5] + ", 错误: " + e.what());
          cell_info.tac = 0;
        }

        cell_info.arfcn = std::stoi(values[6]);
        cell_info.band = std::stoi(values[7]);
        cell_info.ul_bandwidth = std::stoi(values[8]);
        cell_info.dl_bandwidth = std::stoi(values[9]);
        cell_info.rsrp = std::stoi(values[10]);
        cell_info.rsrq = std::stoi(values[11]);
        cell_info.sinr = std::stoi(values[12]);  // SA/NR模式第13个参数是SINR

        // SA/NR模式没有RSSI参数，保持默认值-999

        // 构建详细的解析结果日志
        std::string debug_msg = "解析成功(" + cell_info.act_type + ") - ";
        debug_msg += "PCID: " + std::to_string(cell_info.pcid);
        debug_msg += ", 状态: " + cell_info.state;
        debug_msg += ", 运营商: " + std::to_string(cell_info.mcc) + "-" +
                    (cell_info.mnc < 10 ? "0" : "") + std::to_string(cell_info.mnc);
        debug_msg += ", TAC: " + std::to_string(cell_info.tac);
        debug_msg += ", ARFCN: " + std::to_string(cell_info.arfcn);
        debug_msg += ", 频段: " + std::to_string(cell_info.band);
        debug_msg += ", 带宽: UL" + std::to_string(cell_info.ul_bandwidth) +
                    "/DL" + std::to_string(cell_info.dl_bandwidth);
        debug_msg += ", RSRP: " + std::to_string(cell_info.rsrp) + "dBm";
        debug_msg += ", RSRQ: " + std::to_string(cell_info.rsrq) + "dB";
        debug_msg += ", SINR: " + std::to_string(cell_info.sinr) + "dB";
        debug_msg += " [SA/NR标准13参数格式]";

        log_debug(debug_msg);
        return true;
      }
    } else if (cell_info.act_type == "WCDMA") {
      // WCDMA 模式: <act_type>,<state>,<MCC>,<MNC>,<LAC>,<arfcn>,<band>,<RSCP>,<RSSI>
      if (values.size() >= 9) {
        cell_info.state = values[1];
        cell_info.mcc = std::stoi(values[2]);
        cell_info.mnc = std::stoi(values[3]);
        cell_info.lac = std::stoi(values[4]);
        cell_info.arfcn = std::stoi(values[5]);
        cell_info.band = std::stoi(values[6]);
        cell_info.rscp = std::stoi(values[7]);
        cell_info.rssi = std::stoi(values[8]);

        log_debug("解析成功(WCDMA) - 制式: " + cell_info.act_type +
                  ", 状态: " + cell_info.state +
                  ", RSCP: " + std::to_string(cell_info.rscp) + "dBm" +
                  ", RSSI: " + std::to_string(cell_info.rssi) + "dBm");
        return true;
      }
    }

    log_warn("解析失败，参数数量不足或制式不支持: " + cell_info.act_type +
             ", 参数数量: " + std::to_string(values.size()));
    return false;

  } catch (const std::exception & e) {
    log_error("解析服务小区信息时发生异常: " + std::string(e.what()));
    return false;
  }
}

Nr90Error Nr90SerialComm::set_network_search_config(int mode_pref)
{
  log_info("设置网络搜索偏好: " + std::to_string(mode_pref));

  if (mode_pref < 0 || mode_pref > 7) {
    log_error("无效的网络搜索偏好值: " + std::to_string(mode_pref) + " (支持范围: 0-7)");
    return Nr90Error::INVALID_PARAM;
  }

  std::string command = "AT+LNWSCFG=" + std::to_string(mode_pref);
  Nr90Response response;
  Nr90Error result = send_at_command(command, response, 3);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送网络搜索偏好设置命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("网络搜索偏好设置命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析模式名称用于日志
  std::string mode_name = get_network_search_mode_name(mode_pref);
  log_info("✓ 网络搜索偏好设置成功: " + std::to_string(mode_pref) + " (" + mode_name + ")");
  return Nr90Error::SUCCESS;
}

Nr90Error Nr90SerialComm::get_network_search_config(int & mode_pref)
{
  log_info("查询网络搜索偏好");

  Nr90Response response;
  Nr90Error result = send_at_command("AT+LNWSCFG?", response, 3);

  if (result != Nr90Error::SUCCESS) {
    log_error("发送网络搜索偏好查询命令失败: " + error_to_string(result));
    return result;
  }

  if (!response.is_ok) {
    log_error("网络搜索偏好查询命令响应错误: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }

  // 解析网络搜索偏好响应
  if (parse_network_search_config_response(response.response, mode_pref)) {
    std::string mode_name = get_network_search_mode_name(mode_pref);
    log_info("✓ 网络搜索偏好查询成功: " + std::to_string(mode_pref) + " (" + mode_name + ")");
    return Nr90Error::SUCCESS;
  } else {
    log_error("解析网络搜索偏好响应失败: " + response.response);
    return Nr90Error::RESPONSE_INVALID;
  }
}

bool Nr90SerialComm::parse_network_search_config_response(const std::string & response, int & mode_pref)
{
  log_debug("解析网络搜索偏好响应: " + response);

  // 解析 +LNWSCFG: <mode_pref> 格式的响应
  // 例如: +LNWSCFG: 6
  std::regex nwscfg_regex(R"(\+LNWSCFG:\s*(\d+))");
  std::smatch match;

  if (std::regex_search(response, match, nwscfg_regex)) {
    mode_pref = std::stoi(match[1].str());

    log_debug("解析成功 - 网络搜索偏好: " + std::to_string(mode_pref));
    return true;
  }

  log_warn("无法解析网络搜索偏好响应格式: " + response);
  return false;
}

std::string Nr90SerialComm::get_network_search_mode_name(int mode_pref)
{
  switch (mode_pref) {
    case 0: return "AUTO";
    case 1: return "WCDMA";
    case 2: return "LTE";
    case 3: return "LTE+WCDMA";
    case 4: return "5G SA";
    case 5: return "5G SA+WCDMA";
    case 6: return "5G SA+LTE";
    case 7: return "5G SA+LTE+WCDMA";
    default: return "UNKNOWN(" + std::to_string(mode_pref) + ")";
  }
}

}  // namespace gen3_network_manager_core
