# NAT规则自动管理技术实现文档

## 概述

本文档详细描述了网络管理器中NAT规则自动管理功能的技术实现，包括架构设计、核心算法、接口定义和使用方法。

## 系统架构

### 核心组件

```mermaid
graph TB
    A[NetworkManager] --> B[IptablesUtils]
    A --> C[NetworkUtils]
    A --> D[配置管理]
    
    B --> E[NAT规则配置]
    B --> F[IP转发管理]
    B --> G[规则清理]
    
    C --> H[网络状态检测]
    C --> I[接口信息获取]
    
    D --> J[内网卡配置]
    D --> K[NAT功能开关]
```

### 数据流

```mermaid
sequenceDiagram
    participant T as Timer
    participant NM as NetworkManager
    participant IU as IptablesUtils
    participant S as System
    
    T->>NM: 定时检查网络状态
    NM->>NM: 检测接口变化
    alt 接口变化且为外网卡
        NM->>IU: setup_nat_for_interface()
        IU->>S: 清理旧NAT规则
        IU->>S: 启用IP转发
        IU->>S: 配置MASQUERADE规则
        IU->>S: 配置FORWARD规则
        IU-->>NM: 配置完成
    else 接口变化且为内网卡
        NM->>IU: cleanup_nat_rules()
        IU->>S: 清理所有NAT规则
        IU-->>NM: 清理完成
    end
```

## 核心类设计

### IptablesUtils类

```cpp
class IptablesUtils {
public:
    // 主要接口
    static bool setup_nat_for_interface(
        const std::string& wan_interface, 
        const std::vector<std::string>& lan_interfaces);
    
    static bool cleanup_nat_rules();
    static bool enable_ip_forwarding();
    static bool save_iptables_rules();
    
    // 查询接口
    static std::vector<std::string> get_current_nat_rules();
    static bool check_nat_rule_exists(const std::string& wan_interface);
    
private:
    static std::string exec_command(const std::string& cmd);
};
```

### NetworkManager扩展

```cpp
class NetworkManager {
private:
    // NAT配置参数
    std::vector<std::string> lan_interfaces_;
    bool enable_nat_;
    
    // NAT管理函数
    bool update_nat_rules(const std::string& wan_interface);
    void initialize_nat_rules();
    
public:
    // 在网络切换时调用
    bool switch_to_wifi(const std::string& ssid);
    bool switch_to_5g();
    
    // 在状态更新时调用
    bool update_network_status();
};
```

## 技术实现细节

### 1. NAT规则配置算法

```cpp
bool IptablesUtils::setup_nat_for_interface(
    const std::string& wan_interface, 
    const std::vector<std::string>& lan_interfaces) {
    
    // 步骤1: 清理旧规则
    cleanup_nat_rules();
    
    // 步骤2: 启用IP转发
    if (!enable_ip_forwarding()) {
        return false;
    }
    
    // 步骤3: 配置MASQUERADE规则（源NAT）
    std::string masquerade_cmd = 
        "iptables -t nat -A POSTROUTING -o " + wan_interface + " -j MASQUERADE";
    exec_command(masquerade_cmd);
    
    // 步骤4: 配置FORWARD规则
    for (const auto& lan_interface : lan_interfaces) {
        // 允许内网到外网
        std::string forward_out = 
            "iptables -A FORWARD -i " + lan_interface + 
            " -o " + wan_interface + " -j ACCEPT";
        exec_command(forward_out);
        
        // 允许已建立连接的返回流量
        std::string forward_in = 
            "iptables -A FORWARD -i " + wan_interface + 
            " -o " + lan_interface + 
            " -m state --state RELATED,ESTABLISHED -j ACCEPT";
        exec_command(forward_in);
    }
    
    // 步骤5: 保存规则
    save_iptables_rules();
    
    return true;
}
```

### 2. 网络变化检测算法

```cpp
bool NetworkManager::update_network_status() {
    // 保存旧状态
    std::string old_interface;
    uint8_t old_type;
    {
        std::lock_guard<std::mutex> lock(status_mutex_);
        old_interface = current_status_.interface_name;
        old_type = current_status_.network_type;
    }
    
    // 更新网络状态
    // ... 状态更新逻辑 ...
    
    // 检测变化
    bool interface_changed = (old_interface != current_status_.interface_name);
    bool type_changed = (old_type != current_status_.network_type);
    
    if (interface_changed || type_changed) {
        // 判断是否为外网卡
        if (is_wan_interface(current_status_.interface_name, current_status_.network_type)) {
            // 更新NAT规则
            update_nat_rules(current_status_.interface_name);
        } else {
            // 清理NAT规则
            IptablesUtils::cleanup_nat_rules();
        }
    }
    
    return true;
}
```

### 3. 启动时初始化算法

```cpp
void NetworkManager::initialize_nat_rules() {
    if (!enable_nat_ || lan_interfaces_.empty()) {
        return;
    }
    
    // 获取当前网络状态
    std::string current_interface;
    uint8_t current_type;
    {
        std::lock_guard<std::mutex> lock(status_mutex_);
        current_interface = current_status_.interface_name;
        current_type = current_status_.network_type;
    }
    
    // 检查是否为外网卡
    if (is_wan_interface(current_interface, current_type)) {
        // 配置NAT规则
        update_nat_rules(current_interface);
    } else {
        // 清理可能存在的旧规则
        IptablesUtils::cleanup_nat_rules();
    }
}
```

## 配置管理

### 配置参数定义

```yaml
network_manager_node:
  ros__parameters:
    # 基本网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "eth2"
    
    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
```

### 配置加载逻辑

```cpp
// 在NetworkManager构造函数中
node_->declare_parameter("enable_nat", true);
node_->declare_parameter("lan_interfaces", std::vector<std::string>{"eth0", "eth1"});

enable_nat_ = node_->get_parameter("enable_nat").as_bool();
lan_interfaces_ = node_->get_parameter("lan_interfaces").as_string_array();
```

## 错误处理和日志

### 错误处理策略

1. **配置错误**：NAT功能禁用或内网卡未配置时跳过处理
2. **权限错误**：iptables命令执行失败时记录错误但不中断流程
3. **网络错误**：NAT规则配置失败不影响网络切换主流程

### 日志级别定义

```cpp
// 信息级别：正常的NAT操作
RCLCPP_INFO(logger_, "[NAT] NAT规则更新成功 - 外网接口: %s", interface.c_str());

// 调试级别：详细的iptables命令执行
RCLCPP_DEBUG(logger_, "[IPTABLES] 执行命令: %s", cmd.c_str());

// 警告级别：非致命错误
RCLCPP_WARN(logger_, "[NAT] NAT规则更新失败，但不影响网络切换");

// 错误级别：严重错误
RCLCPP_ERROR(logger_, "[IPTABLES] 无法执行命令: %s", cmd.c_str());
```

## 性能优化

### 1. 锁管理优化

```cpp
// 避免在持有锁时执行耗时操作
{
    std::lock_guard<std::mutex> lock(status_mutex_);
    // 快速获取必要信息
    std::string interface = current_status_.interface_name;
}
// 释放锁后执行NAT操作
update_nat_rules(interface);
```

### 2. 命令执行优化

```cpp
// 批量执行iptables命令，减少系统调用次数
std::vector<std::string> commands = {
    "iptables -t nat -A POSTROUTING -o " + wan_interface + " -j MASQUERADE",
    "iptables -A FORWARD -i " + lan_interface + " -o " + wan_interface + " -j ACCEPT"
};

for (const auto& cmd : commands) {
    exec_command(cmd);
}
```

### 3. 规则检查优化

```cpp
// 避免重复配置相同的NAT规则
bool IptablesUtils::check_nat_rule_exists(const std::string& wan_interface) {
    std::string check_cmd = 
        "iptables -t nat -C POSTROUTING -o " + wan_interface + " -j MASQUERADE 2>/dev/null";
    std::string result = exec_command(check_cmd);
    return result.empty();  // 命令成功执行表示规则存在
}
```

## 测试和验证

### 单元测试

```cpp
// 测试NAT规则配置
TEST(IptablesUtilsTest, SetupNatRules) {
    std::vector<std::string> lan_interfaces = {"eth0", "eth1"};
    bool result = IptablesUtils::setup_nat_for_interface("wlan0", lan_interfaces);
    EXPECT_TRUE(result);
    
    // 验证规则是否正确配置
    EXPECT_TRUE(IptablesUtils::check_nat_rule_exists("wlan0"));
}

// 测试规则清理
TEST(IptablesUtilsTest, CleanupNatRules) {
    IptablesUtils::cleanup_nat_rules();
    auto rules = IptablesUtils::get_current_nat_rules();
    EXPECT_TRUE(rules.empty());
}
```

### 集成测试

```bash
#!/bin/bash
# 测试脚本：验证NAT功能

# 1. 启动网络管理器
ros2 launch gen3_network_manager_core network_manager.launch.py &

# 2. 等待初始化完成
sleep 5

# 3. 检查NAT规则是否正确配置
sudo iptables -t nat -L POSTROUTING -n | grep MASQUERADE

# 4. 测试内网设备连通性
ping -I eth0 -c 3 *******

# 5. 触发网络切换
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork \
    "{network_type: 1, wifi_ssid: 'TestWiFi', force_switch: true}"

# 6. 验证NAT规则更新
sleep 2
sudo iptables -t nat -L POSTROUTING -n | grep MASQUERADE
```

## 故障排除

### 常见问题

1. **权限不足**
   - 症状：iptables命令执行失败
   - 解决：确保程序以root权限运行

2. **内网卡配置错误**
   - 症状：NAT规则配置但内网设备无法上网
   - 解决：检查lan_interfaces配置是否正确

3. **IP转发未启用**
   - 症状：NAT规则存在但数据包不转发
   - 解决：检查/proc/sys/net/ipv4/ip_forward值

### 调试命令

```bash
# 检查NAT规则
sudo iptables -t nat -L -n --line-numbers

# 检查FORWARD规则
sudo iptables -L FORWARD -n --line-numbers

# 检查IP转发状态
cat /proc/sys/net/ipv4/ip_forward

# 查看网络接口状态
ip addr show
ip route show

# 测试内网连通性
ping -I eth0 *******
traceroute -i eth0 *******
```

## 扩展和定制

### 支持IPv6

```cpp
// 扩展支持IPv6 NAT规则
bool setup_ipv6_nat_for_interface(
    const std::string& wan_interface,
    const std::vector<std::string>& lan_interfaces) {
    
    // 配置IPv6 MASQUERADE规则
    std::string masquerade_cmd = 
        "ip6tables -t nat -A POSTROUTING -o " + wan_interface + " -j MASQUERADE";
    exec_command(masquerade_cmd);
    
    // 配置IPv6 FORWARD规则
    for (const auto& lan_interface : lan_interfaces) {
        std::string forward_cmd = 
            "ip6tables -A FORWARD -i " + lan_interface + 
            " -o " + wan_interface + " -j ACCEPT";
        exec_command(forward_cmd);
    }
    
    return true;
}
```

### 流量统计

```cpp
// 添加流量统计功能
struct TrafficStats {
    uint64_t bytes_in;
    uint64_t bytes_out;
    uint64_t packets_in;
    uint64_t packets_out;
};

TrafficStats get_interface_traffic_stats(const std::string& interface) {
    // 从/proc/net/dev读取流量统计
    // 或使用iptables -L -n -v获取规则统计
}
```

## 总结

NAT规则自动管理系统通过以下技术实现了完整的功能：

1. **自动化配置**：根据网络状态变化自动配置和清理NAT规则
2. **多层检测**：启动检测、切换检测、状态变化检测
3. **错误处理**：完善的错误处理和日志记录机制
4. **性能优化**：锁管理、命令批处理、规则检查优化
5. **可扩展性**：支持IPv6、流量统计等扩展功能

该系统为内网设备提供了稳定可靠的外网访问能力，适用于各种网络环境和部署场景。
