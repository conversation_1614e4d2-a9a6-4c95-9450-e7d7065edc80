# 文档索引

本目录包含 ROS2 网络管理器项目的所有文档，按功能分类组织。

## 📖 主要文档

### 🔧 调试和配置
- **[调试配置指南.md](调试配置指南.md)** - 完整的调试配置指南
  - VSCode 调试配置详解
  - 环境变量设置
  - 故障排除指南
  - 最佳实践

- **[架构配置说明.md](架构配置说明.md)** - 架构自适应配置
  - 多架构支持说明
  - 自动检测脚本使用
  - 配置更新方法

### 📋 配置说明
- **[Markdown配置说明.md](Markdown配置说明.md)** - Markdown 配置说明
- **[launch配置备份管理.md](launch配置备份管理.md)** - launch.json 备份管理系统

## 📁 分类文档

### 🚀 功能特性 (features/)
- **[自动切换优化.md](features/自动切换优化.md)** - 自动切换优化
- **[配置参数使用说明.md](features/配置参数使用说明.md)** - 配置参数使用说明
- **[死锁预防机制.md](features/死锁预防机制.md)** - 死锁预防机制

### 🐛 故障排除 (troubleshooting/)
- **[调试日志说明.md](troubleshooting/调试日志说明.md)** - 调试日志说明
- **[日志级别优化.md](troubleshooting/日志级别优化.md)** - 日志级别优化

### 📡 API 接口 (api/)
- **[ROS接口文档.md](api/ROS接口文档.md)** - ROS2 接口完整文档

### 📋 需求分析 (requirements/)
- **[网络需求分析.md](requirements/网络需求分析.md)** - 网络需求详细分析

### 💻 开发文档 (development/)
- **[Claude开发笔记.md](development/Claude开发笔记.md)** - Claude 开发笔记

## 🚀 快速导航

### 新用户入门
1. 阅读 [主 README](../README.md) 了解项目概述
2. 查看 [DEBUG_CONFIGURATION.md](DEBUG_CONFIGURATION.md) 设置调试环境
3. 运行 `../test_debug_config.sh` 验证配置

### 开发者指南
1. [DEBUG_CONFIGURATION.md](DEBUG_CONFIGURATION.md) - 调试配置
2. [ARCHITECTURE_CONFIG.md](ARCHITECTURE_CONFIG.md) - 架构支持
3. 项目根目录的各种 `.md` 文件 - 具体功能说明

### 故障排除
1. [DEBUG_CONFIGURATION.md](DEBUG_CONFIGURATION.md#故障排除) - 常见问题解决
2. [ARCHITECTURE_CONFIG.md](ARCHITECTURE_CONFIG.md#故障排除) - 架构相关问题
3. [DEBUG_LOG_EXPLANATION.md](../DEBUG_LOG_EXPLANATION.md) - 日志分析

## 📁 文档分类

### 🔧 技术配置
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| DEBUG_CONFIGURATION.md | 完整调试配置指南 | 所有开发者 |
| ARCHITECTURE_CONFIG.md | 架构自适应配置 | 多平台开发者 |
| MARKDOWN_CONFIG.md | Markdown 配置 | 文档维护者 |

### 📋 功能说明
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| features/配置参数使用说明.md | 配置参数说明 | 系统配置者 |
| features/死锁预防机制.md | 死锁预防机制 | 系统架构师 |
| features/自动切换优化.md | 自动切换优化 | 网络工程师 |

### 🐛 调试和日志
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| troubleshooting/调试日志说明.md | 调试日志说明 | 调试人员 |
| troubleshooting/日志级别优化.md | 日志级别优化 | 运维人员 |

### 📡 API 和接口
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| api/ROS接口文档.md | ROS2 接口文档 | 开发者 |

### 📋 需求和分析
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| requirements/网络需求分析.md | 网络需求分析 | 产品经理 |

### 💻 开发相关
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| development/Claude开发笔记.md | 开发笔记 | 开发者 |

## 🔄 文档更新

### 最近更新
- **2025-07-19**: 完成调试配置整合和文档重构
- **2025-07-19**: 添加架构自适应支持
- **2025-07-19**: 解决共享库加载问题

### 维护说明
- 所有文档都应保持与实际配置同步
- 新增功能时需要更新相应文档
- 定期检查文档链接的有效性

## 🛠️ 相关工具

### 验证脚本
```bash
# 验证调试配置
../test_debug_config.sh

# 更新架构配置
../scripts/update_launch_config.sh

# 检测当前架构
../scripts/get_arch.sh
```

### VSCode 配置
- `.vscode/launch.json` - 调试配置
- `.vscode/tasks.json` - 任务配置

## 📞 获取帮助

1. **查看相关文档** - 根据问题类型选择对应文档
2. **运行验证脚本** - 使用 `test_debug_config.sh` 检查配置
3. **检查日志输出** - 参考 DEBUG_LOG_EXPLANATION.md
4. **更新配置** - 使用 `update_launch_config.sh` 重新配置

---

**文档持续更新中，如有问题请参考具体的技术文档或运行相关验证脚本。**
