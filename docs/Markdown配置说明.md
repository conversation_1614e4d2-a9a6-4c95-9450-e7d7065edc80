# Markdown 默认打开配置

本项目提供了灵活的Markdown文件默认打开方式配置，可以根据您的使用习惯选择最适合的模式。

## 当前配置

当前设置为 **预览模式**，打开.md文件时会自动显示预览界面。

## 可用模式

### 1. 预览模式 (推荐用于阅读)
- 打开.md文件时自动显示预览
- 适合阅读文档和查看格式化效果
- 可以双击预览区域切换到编辑模式

### 2. 编辑模式 (推荐用于编写)
- 打开.md文件时直接进入编辑模式
- 适合编写和修改文档
- 可以使用 `Ctrl+Shift+V` 打开预览

### 3. 默认模式
- 使用VSCode的默认行为
- 通常会询问用户选择打开方式

## 切换方式

### 方法1: 使用脚本 (推荐)

```bash
# 设置为预览模式
./scripts/set_markdown_mode.sh preview

# 设置为编辑模式
./scripts/set_markdown_mode.sh editor

# 移除自定义设置
./scripts/set_markdown_mode.sh remove
```

### 方法2: 使用VSCode任务

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择以下任务之一：
   - `set markdown preview mode` - 设置为预览模式
   - `set markdown editor mode` - 设置为编辑模式

### 方法3: 手动编辑设置

编辑 `.vscode/settings.json` 文件：

```json
{
    // 预览模式
    "workbench.editorAssociations": {
        "*.md": "vscode.markdown.preview.editor"
    }
}
```

或者删除 `workbench.editorAssociations` 配置来使用编辑模式。

## 其他Markdown设置

项目还包含以下Markdown增强设置：

- `markdown.preview.markEditorSelection`: 在预览中标记编辑器选择的内容
- `markdown.preview.scrollPreviewWithEditor`: 预览跟随编辑器滚动
- `markdown.preview.scrollEditorWithPreview`: 编辑器跟随预览滚动
- `markdown.preview.fontSize`: 预览字体大小
- `markdown.preview.lineHeight`: 预览行高

## 快捷键

- `Ctrl+Shift+V`: 打开Markdown预览
- `Ctrl+K V`: 在侧边打开Markdown预览
- `Ctrl+Shift+P` → "Markdown: Open Preview to the Side": 并排显示编辑和预览

## 故障排除

如果设置没有生效：

1. 重启VSCode
2. 检查 `.vscode/settings.json` 文件语法是否正确
3. 确保没有全局设置覆盖项目设置

## 恢复默认设置

如果需要恢复到原始设置：

```bash
# 查看备份文件
ls .vscode/settings.json.backup.*

# 恢复最新备份
cp .vscode/settings.json.backup.YYYYMMDD_HHMMSS .vscode/settings.json
```
