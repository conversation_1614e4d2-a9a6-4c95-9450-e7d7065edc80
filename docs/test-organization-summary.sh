#!/bin/bash

# 测试脚本整理总结验证
# 验证测试脚本是否已正确整理

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}=== 测试脚本整理总结 ===${NC}"
echo

# 检查测试目录
echo -e "${BLUE}📁 检查测试目录结构:${NC}"
if [ -d "tests" ]; then
    echo "✅ tests/ 目录存在"
    
    # 列出测试文件
    echo
    echo -e "${BLUE}📋 测试文件列表:${NC}"
    ls -la tests/ | grep -E '\.(sh|cpp|md)$' | while read line; do
        echo "  $line"
    done
    
    echo
    echo -e "${BLUE}🔧 测试脚本功能:${NC}"
    
    # 检查各个测试文件
    if [ -f "tests/config-validation-test.sh" ]; then
        echo "✅ config-validation-test.sh - 配置验证测试"
    else
        echo "❌ config-validation-test.sh - 缺失"
    fi
    
    if [ -f "tests/debug-config-test.sh" ]; then
        echo "✅ debug-config-test.sh - 调试配置测试"
    else
        echo "❌ debug-config-test.sh - 缺失"
    fi
    
    if [ -f "tests/network-interface-test.cpp" ]; then
        echo "✅ network-interface-test.cpp - 网络接口测试"
    else
        echo "❌ network-interface-test.cpp - 缺失"
    fi
    
    if [ -f "tests/run-all-tests.sh" ]; then
        echo "✅ run-all-tests.sh - 完整测试套件"
    else
        echo "❌ run-all-tests.sh - 缺失"
    fi
    
    if [ -f "tests/run-tests.sh" ]; then
        echo "✅ run-tests.sh - 简化测试套件"
    else
        echo "❌ run-tests.sh - 缺失"
    fi
    
    if [ -f "tests/README.md" ]; then
        echo "✅ README.md - 测试文档"
    else
        echo "❌ README.md - 缺失"
    fi
    
else
    echo "❌ tests/ 目录不存在"
    exit 1
fi

echo
echo -e "${BLUE}🧪 快速功能测试:${NC}"

# 测试配置验证脚本
if [ -x "tests/config-validation-test.sh" ]; then
    echo -n "  配置验证测试: "
    if ./tests/config-validation-test.sh > /dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
    else
        echo -e "${RED}失败${NC}"
    fi
else
    echo "  配置验证测试: 脚本不可执行"
fi

# 测试调试配置脚本
if [ -x "tests/debug-config-test.sh" ]; then
    echo -n "  调试配置测试: "
    if ./tests/debug-config-test.sh > /dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
    else
        echo -e "${RED}失败${NC}"
    fi
else
    echo "  调试配置测试: 脚本不可执行"
fi

# 测试网络接口程序
if [ -f "tests/network-interface-test.cpp" ]; then
    echo -n "  网络接口测试: "
    if cd tests && g++ -o network-interface-test network-interface-test.cpp 2>/dev/null && ./network-interface-test > /dev/null 2>&1; then
        echo -e "${GREEN}通过${NC}"
        rm -f network-interface-test
    else
        echo -e "${RED}失败${NC}"
    fi
    cd ..
else
    echo "  网络接口测试: 源文件不存在"
fi

echo
echo -e "${BLUE}📖 使用方法:${NC}"
echo "  ./tests/run-tests.sh          # 运行简化测试套件"
echo "  ./tests/run-all-tests.sh      # 运行完整测试套件"
echo "  ./tests/config-validation-test.sh  # 单独运行配置测试"
echo "  ./tests/debug-config-test.sh       # 单独运行调试测试"

echo
echo -e "${BLUE}📁 整理前后对比:${NC}"
echo "  整理前: 测试脚本分散在根目录和 scripts/ 目录"
echo "  整理后: 所有测试脚本统一在 tests/ 目录"
echo
echo "  移动的文件:"
echo "    test_config.sh → tests/config-validation-test.sh"
echo "    scripts/test_debug_config.sh → tests/debug-config-test.sh"
echo "    test_network_config.cpp → tests/network-interface-test.cpp"
echo
echo "  新增的文件:"
echo "    tests/run-all-tests.sh - 完整测试套件"
echo "    tests/run-tests.sh - 简化测试套件"
echo "    tests/README.md - 测试文档"

echo
echo -e "${GREEN}🎉 测试脚本整理完成！${NC}"
echo "所有测试相关文件已统一整理到 tests/ 目录中，便于管理和使用。"
