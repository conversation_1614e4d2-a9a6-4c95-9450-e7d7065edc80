# ROS2 网络管理器调试配置完整指南

## 概述

本文档整合了 ROS2 网络管理器项目的所有调试配置信息，包括 VSCode 调试设置、架构支持、环境配置和使用方法。

## 问题解决

### 原始问题
- ❌ `librclcpp_action.so: cannot open shared object file: No such file or directory`
- ❌ 架构路径硬编码问题
- ❌ ROS 环境变量缺失

### 解决方案
- ✅ 添加 ROS Humble 库路径到 `LD_LIBRARY_PATH`
- ✅ 设置完整的 ROS 环境变量
- ✅ 提供架构自适应脚本
- ✅ 创建便捷启动脚本

## VSCode 调试配置

### 配置列表

| 配置名称 | 用途 | 架构支持 | 配置文件 |
|---------|------|----------|----------|
| ROS2 C++ Debug (Auto-Arch) | 基础调试 | 自适应 | 无 |
| ROS2 C++ Debug (x86_64 Fixed) | 基础调试 | 固定x86_64 | 无 |
| ROS2 C++ Debug with Config (Auto-Arch) | 网络配置调试 | 自适应 | network_config.yaml |
| ROS2 C++ Debug with Config (x86_64 Fixed) | 网络配置调试 | 固定x86_64 | network_config.yaml |
| ROS2 C++ Debug with Dev Config (Auto-Arch) | 开发调试 | 自适应 | development_config.yaml |
| ROS2 C++ Release Debug (Auto-Arch) | Release调试 | 自适应 | 无 |
| ROS2 C++ Attach to Process (Auto-Arch) | 进程附加 | 自适应 | 无 |
| ROS2 Launch File Debug | Launch调试 | 自适应 | launch文件 |
| ROS2 Launch with Custom Config | 自定义Launch | 自适应 | development_config.yaml |

### 环境变量配置

所有调试配置都包含以下环境变量：

```json
"environment": [
    {
        "name": "LD_LIBRARY_PATH",
        "value": "/opt/ros/humble/lib:${workspaceFolder}/install/{arch}/Debug/lib:..."
    },
    {
        "name": "AMENT_PREFIX_PATH", 
        "value": "/opt/ros/humble:${workspaceFolder}/install/{arch}/Debug:..."
    },
    {
        "name": "ROS_DOMAIN_ID",
        "value": "0"
    },
    {
        "name": "ROS_DISTRO",
        "value": "humble"
    },
    {
        "name": "ROS_VERSION",
        "value": "2"
    }
]
```

## 架构支持

### 支持的架构
- **x86_64**: Intel/AMD 64位处理器
- **aarch64**: ARM 64位处理器（树莓派、Jetson等）
- **arm64**: ARM 64位处理器（macOS Apple Silicon）

### 架构检测和配置

#### 自动检测脚本
```bash
./scripts/get_arch.sh          # 获取当前架构
./scripts/update_launch_config.sh  # 更新配置为当前架构
```

#### 手动架构选择
在使用 "Auto-Arch" 配置时，VSCode 会提示选择目标架构。

## 使用方法

### 1. 快速开始

```bash
# 1. 构建项目
./build.sh

# 2. 更新调试配置（可选）
./scripts/update_launch_config.sh

# 3. 验证配置
./test_debug_config.sh

# 4. 启动调试
# 在 VSCode 中按 F5，选择合适的配置
```

### 2. 命令行运行

```bash
# 使用架构自适应启动脚本
./run_network_manager.sh

# 手动设置环境并运行
source /opt/ros/humble/setup.bash
source install/$(uname -m)/Debug/setup.bash
./install/$(uname -m)/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node
```

### 3. VSCode 调试步骤

1. **打开 VSCode**
2. **按 F5 或点击 "Run and Debug"**
3. **选择调试配置**：
   - 日常开发：`ROS2 C++ Debug (Auto-Arch)`
   - 带配置：`ROS2 C++ Debug with Config (Auto-Arch)`
   - 开发调试：`ROS2 C++ Debug with Dev Config (Auto-Arch)`
4. **选择架构**（如果使用 Auto-Arch 配置）
5. **开始调试**

### 4. 推荐配置选择

| 使用场景 | 推荐配置 |
|---------|----------|
| 日常开发调试 | ROS2 C++ Debug (Auto-Arch) |
| 网络功能测试 | ROS2 C++ Debug with Config (Auto-Arch) |
| 详细日志调试 | ROS2 C++ Debug with Dev Config (Auto-Arch) |
| 性能测试 | ROS2 C++ Release Debug (Auto-Arch) |
| 生产环境调试 | ROS2 C++ Attach to Process (Auto-Arch) |
| Launch文件调试 | ROS2 Launch File Debug |

## 配置文件说明

### 网络配置文件
- `network_config.yaml`: 基础网络配置
- `development_config.yaml`: 开发环境配置，包含详细日志
- `production_config.yaml`: 生产环境配置

### Launch 文件
- `network_manager.launch.py`: 主要的 Launch 文件

## 故障排除

### 常见问题及解决方案

#### 1. 共享库加载错误
```
错误: librclcpp_action.so: cannot open shared object file
解决: 
- 确保 ROS Humble 已安装: ls /opt/ros/humble/lib/
- 运行更新脚本: ./scripts/update_launch_config.sh
- 使用启动脚本: ./run_network_manager.sh
```

#### 2. 程序不存在错误
```
错误: program does not exist
解决:
- 重新构建: ./build.sh
- 更新配置: ./scripts/update_launch_config.sh
- 检查文件: ls install/$(uname -m)/Debug/gen3_network_manager_core/lib/
```

#### 3. 架构不匹配
```
错误: 在不同架构机器上调试失败
解决:
- 重新构建: ./build.sh
- 更新配置: ./scripts/update_launch_config.sh
- 验证配置: ./test_debug_config.sh
```

#### 4. 环境变量问题
```
错误: ROS 环境未正确设置
解决:
- 检查 ROS 安装: echo $ROS_DISTRO
- 手动 source: source /opt/ros/humble/setup.bash
- 使用启动脚本: ./run_network_manager.sh
```

## 相关工具和脚本

### 核心脚本
- `build.sh` - 项目构建脚本
- `run_network_manager.sh` - 架构自适应启动脚本
- `test_debug_config.sh` - 配置验证脚本

### 架构相关脚本
- `scripts/get_arch.sh` - 架构检测
- `scripts/update_launch_config.sh` - 配置更新

### 配置文件
- `.vscode/launch.json` - VSCode 调试配置
- `.vscode/tasks.json` - VSCode 任务配置

## 最佳实践

1. **优先使用 Auto-Arch 配置** - 支持多架构，更灵活
2. **定期更新配置** - 在新机器或架构变更后运行更新脚本
3. **使用启动脚本** - `run_network_manager.sh` 自动处理环境设置
4. **验证配置** - 使用 `test_debug_config.sh` 确保配置正确
5. **选择合适的配置文件** - 根据调试需求选择对应的 yaml 配置

## 版本信息

- **ROS 版本**: ROS2 Humble
- **支持架构**: x86_64, aarch64, arm64
- **VSCode 扩展**: C/C++ Extension Pack
- **调试器**: GDB

---

**配置完成！现在可以在任何支持的架构上无缝进行 ROS2 项目的开发和调试。**
