# 配置参数使用说明

## 概述

现在已经实现了配置文件中这些重要参数的功能：

```yaml
enable_auto_switch: true
preferred_network_type: 1
min_signal_strength: -70
min_connectivity_score: 50.0
```

## 参数详细说明

### 1. enable_auto_switch (布尔值)
- **功能**: 控制是否启用自动网络切换
- **默认值**: `true`
- **说明**: 
  - `true`: 启用自动切换，当检测到网络质量问题时自动切换到更好的网络
  - `false`: 禁用自动切换，只能手动切换网络

### 2. preferred_network_type (整数)
- **功能**: 设置首选的网络类型
- **默认值**: `1`
- **可选值**:
  - `1`: WiFi 优先
  - `2`: 以太网优先
  - `3`: 5G 优先
- **说明**: 当需要自动切换时，系统会优先切换到此类型的网络

### 3. min_signal_strength (整数，dBm)
- **功能**: WiFi网络的最低信号强度阈值
- **默认值**: `-70`
- **单位**: dBm (分贝毫瓦)
- **说明**: 
  - 当WiFi信号强度低于此值时，触发自动切换
  - 典型值范围: -30dBm (极强) 到 -90dBm (极弱)
  - 推荐设置: -70dBm (良好连接的最低要求)

### 4. min_connectivity_score (浮点数)
- **功能**: 网络连接质量的最低评分阈值
- **默认值**: `50.0`
- **范围**: 0.0 - 100.0
- **说明**: 
  - 当网络质量评分低于此值时，触发自动切换
  - 评分综合考虑延迟、丢包率、带宽等因素
  - 推荐设置: 50.0 (中等质量)

## 自动切换逻辑

### 触发条件
系统会在以下情况下触发自动切换：

1. **WiFi信号强度过低**
   ```
   当前信号强度 < min_signal_strength
   ```

2. **连接质量评分过低**
   ```
   当前质量评分 < min_connectivity_score
   ```

3. **网络连接异常**
   ```
   网关不可达 OR 互联网不可访问
   ```

### 切换策略

根据 `preferred_network_type` 的设置：

#### WiFi优先 (preferred_network_type: 1)
- 当前非WiFi → 切换到WiFi
- 当前是WiFi且有问题 → 切换到5G

#### 以太网优先 (preferred_network_type: 2)
- 当前非以太网 → 切换到以太网
- 当前是以太网且有问题 → 切换到WiFi

#### 5G优先 (preferred_network_type: 3)
- 当前非5G → 切换到5G
- 当前是5G且有问题 → 切换到WiFi

## 配置示例

### 开发环境配置
```yaml
# development_config.yaml
enable_auto_switch: true
preferred_network_type: 1        # WiFi优先，便于开发
min_signal_strength: -75         # 较宽松的信号要求
min_connectivity_score: 40.0     # 较宽松的质量要求
```

### 生产环境配置
```yaml
# production_config.yaml
enable_auto_switch: true
preferred_network_type: 3        # 5G优先，保证稳定性
min_signal_strength: -65         # 较严格的信号要求
min_connectivity_score: 60.0     # 较严格的质量要求
```

### 测试环境配置
```yaml
# test_config.yaml
enable_auto_switch: false        # 禁用自动切换，避免测试干扰
preferred_network_type: 1
min_signal_strength: -70
min_connectivity_score: 50.0
```

## 日志输出示例

### 正常运行
```
[INFO] [CONFIG] 自动切换: 启用
[INFO] [CONFIG] 首选网络类型: 1
[INFO] [CONFIG] 最低信号强度: -70dBm
[INFO] [CONFIG] 最低连接质量评分: 50.0
[DEBUG] [AUTO] 网络质量良好，无需切换
```

### 触发自动切换
```
[WARN] [AUTO] 检测到网络质量问题: WiFi信号强度过低 (-75dBm < -70dBm), 连接质量评分过低 (45.2 < 50.0)
[INFO] [AUTO] 准备自动切换到网络类型: 3
[INFO] [AUTO] 自动网络切换成功
```

### 切换失败
```
[WARN] [AUTO] 检测到网络质量问题: 网络连接异常 (网关:不可达, 互联网:不可访问)
[INFO] [AUTO] 准备自动切换到网络类型: 1
[ERROR] [AUTO] 自动网络切换失败
```

## 使用建议

1. **信号强度设置**:
   - 室内环境: -70dBm
   - 室外环境: -75dBm
   - 要求较高: -65dBm

2. **质量评分设置**:
   - 一般应用: 50.0
   - 实时应用: 70.0
   - 关键应用: 80.0

3. **首选网络类型**:
   - 移动场景: WiFi优先
   - 固定场景: 以太网优先
   - 高可靠性: 5G优先

4. **自动切换控制**:
   - 开发调试: 可以禁用
   - 生产环境: 建议启用
   - 测试环境: 根据需要设置

## 注意事项

1. 自动切换会有短暂的网络中断
2. 频繁切换可能影响网络稳定性
3. 建议根据实际环境调整阈值
4. 可以通过日志监控切换行为
5. 支持运行时通过ROS参数动态调整
