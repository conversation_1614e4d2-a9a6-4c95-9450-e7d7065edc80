# WiFi连接优化功能说明

## 【细化方案】

### 问题描述

原有系统的WiFi连接限制：
1. **扫描功能正常**：可以扫描到所有周围的WiFi网络
2. **连接限制**：只能连接到预配置的WiFi网络
3. **需要改进**：让系统能够连接到任何扫描到的WiFi网络

### 解决方案

#### 1. **ConnectWiFi服务增强**
- 通过`/connect_wifi`服务可以连接任何WiFi网络
- 连接成功后自动保存WiFi账号密码到配置文件(`/tmp/wifi_networks.conf`)
- 支持开放网络和加密网络的连接

#### 2. **自动WiFi选择优化**
修改`get_best_available_wifi()`函数的选择策略：

**优先级顺序**：
1. **已保存密码的网络**：优先选择有已保存密码的网络
2. **开放网络**：其次选择无需密码的开放网络
3. **已知高优先级网络**：再次选择配置的高优先级网络
4. **信号强度**：最后考虑信号强度

#### 3. **网络切换时密码查找**
- 网络切换时自动从已保存的网络中查找密码
- 如果找到密码，使用密码连接
- 如果是开放网络，直接连接
- 如果需要密码但未找到，记录警告但仍尝试连接

#### 4. **NetworkUtils连接逻辑优化**

**连接命令选择**：
```cpp
if (security_type == "OPEN") {
  // 开放网络
  connect_cmd = "nmcli device wifi connect \"" + ssid + "\"";
} else if (password.empty()) {
  // 加密网络但密码为空，尝试连接已保存的网络配置
  connect_cmd = "nmcli device wifi connect \"" + ssid + "\"";
} else {
  // 加密网络且提供了密码
  connect_cmd = "nmcli device wifi connect \"" + ssid + "\" password \"" + password + "\"";
}
```

### 实现细节

#### 1. **WiFi密码保存格式**
配置文件：`/tmp/wifi_networks.conf`
```ini
# Gen3 WiFi Networks Configuration
[network]
ssid=CMCC-533c
password=12345678
security_type=WPA2
priority=0
auto_connect=true

[network]
ssid=ZYHY-Guest
password=
security_type=OPEN
priority=0
auto_connect=true
```

#### 2. **密码查找函数**
```cpp
std::string get_saved_wifi_password(const std::string & ssid);
```
- 在`NetworkManager`和`NetworkSwitch`中都实现了此方法
- 读取配置文件，查找指定SSID的密码
- 返回密码字符串，如果未找到返回空字符串

#### 3. **ConnectWiFi服务流程**
1. 接收连接请求（SSID、密码、安全类型）
2. 验证参数（加密网络必须提供密码）
3. 调用`NetworkUtils::connect_to_wifi`连接
4. 连接成功后保存网络配置到文件
5. 返回连接结果和IP地址

### 使用示例

#### 1. **通过服务连接新WiFi**
```bash
ros2 service call /connect_wifi gen3_network_interfaces/srv/ConnectWiFi \
  "{ssid: 'MyWiFi', password: 'mypassword', security_type: 'WPA2'}"
```

#### 2. **自动切换到最佳WiFi**
系统会自动：
1. 扫描可用WiFi网络
2. 优先选择有保存密码的网络
3. 其次选择开放网络
4. 使用保存的密码或空密码连接

#### 3. **手动切换到指定WiFi**
```bash
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork \
  "{network_type: 1, wifi_ssid: 'TargetWiFi'}"
```

### 优势

1. **灵活性**：可以连接到任何扫描到的WiFi网络
2. **自动化**：连接成功后自动保存，下次可以自动连接
3. **智能选择**：优先选择已知可连接的网络
4. **向后兼容**：保持原有API接口不变
5. **安全性**：密码保存在本地配置文件中

### 注意事项

1. **密码安全**：配置文件存储在`/tmp`目录，重启后会丢失
2. **权限要求**：需要sudo权限执行nmcli命令
3. **网络状态**：需要确保WiFi接口处于managed状态
4. **连接超时**：连接过程有超时机制，避免长时间等待

这个优化方案解决了原有系统只能连接预配置WiFi的限制，让系统具备了连接任意WiFi网络的能力。
