# 网络管理器配置更新指南

## 概述

本文档提供了网络管理器配置文件的更新指南，包括新增的NAT功能配置、配置参数说明和迁移步骤。

## 新增配置参数

### NAT和内网卡配置

在所有配置文件中新增以下参数：

```yaml
# NAT和内网卡配置
enable_nat: true                    # 启用NAT功能
lan_interfaces: ["eth0", "eth1"]    # 内网卡列表
```

### 参数说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_nat` | boolean | `true` | 是否启用NAT转发功能 |
| `lan_interfaces` | array | `["eth0", "eth1"]` | 内网卡接口名称列表 |

## 配置文件更新

### 1. 默认配置文件 (network_config.yaml)

```yaml
gen3_network_manager:
  ros__parameters:
    # 网络功能配置
    enable_auto_switch: true
    preferred_network_type: 1
    min_signal_strength: -70
    min_connectivity_score: 50.0
    
    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "eth2"
```

### 2. 开发环境配置 (development_config.yaml)

```yaml
gen3_network_manager:
  ros__parameters:
    # 网络功能配置
    enable_auto_switch: "true"
    preferred_network_type: 1        # WiFi优先
    min_signal_strength: -80         # 更宽松的信号要求
    min_connectivity_score: 30.0    # 更宽松的质量要求
    
    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
```

### 3. 生产环境配置 (production_config.yaml)

```yaml
gen3_network_manager:
  ros__parameters:
    # 网络功能配置
    enable_auto_switch: "true"
    preferred_network_type: 1        # WiFi优先
    min_signal_strength: -70         # 标准信号要求
    min_connectivity_score: 60.0    # 较高的质量要求
    
    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
```

## 配置场景示例

### 场景1：单内网卡环境

```yaml
# NAT和内网卡配置
enable_nat: true
lan_interfaces: ["eth0"]  # 只有一个内网卡
```

### 场景2：多内网卡环境

```yaml
# NAT和内网卡配置
enable_nat: true
lan_interfaces: ["eth0", "eth1", "eth3", "eth4"]  # 多个内网卡
```

### 场景3：禁用NAT功能

```yaml
# NAT和内网卡配置
enable_nat: false
lan_interfaces: []  # 空列表
```

### 场景4：测试环境配置

```yaml
# NAT和内网卡配置
enable_nat: true
lan_interfaces: ["enp0s3", "enp0s8"]  # 虚拟机网卡名称
```

## 配置迁移步骤

### 步骤1：备份现有配置

```bash
# 备份配置文件
cp src/gen3_network_manager_core/config/network_config.yaml \
   src/gen3_network_manager_core/config/network_config.yaml.backup

cp src/gen3_network_manager_core/config/development_config.yaml \
   src/gen3_network_manager_core/config/development_config.yaml.backup

cp src/gen3_network_manager_core/config/production_config.yaml \
   src/gen3_network_manager_core/config/production_config.yaml.backup
```

### 步骤2：更新配置文件

在每个配置文件的网络功能配置部分后添加NAT配置：

```yaml
    # 网络功能配置
    enable_auto_switch: true
    preferred_network_type: 1
    min_signal_strength: -70
    min_connectivity_score: 50.0
    
    # NAT和内网卡配置 (新增)
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
```

### 步骤3：验证配置语法

```bash
# 使用yaml工具验证语法
python3 -c "import yaml; yaml.safe_load(open('src/gen3_network_manager_core/config/network_config.yaml'))"
```

### 步骤4：重新编译项目

```bash
colcon build --packages-select gen3_network_manager_core
```

### 步骤5：测试配置加载

```bash
# 启动系统并检查日志
ros2 launch gen3_network_manager_core network_manager.launch.py

# 检查NAT配置是否正确加载
ros2 param get /gen3_network_manager enable_nat
ros2 param get /gen3_network_manager lan_interfaces
```

## 配置验证

### 检查配置参数

```bash
# 列出所有参数
ros2 param list | grep gen3_network_manager

# 检查NAT相关参数
ros2 param get /gen3_network_manager enable_nat
ros2 param get /gen3_network_manager lan_interfaces
```

### 验证NAT功能

```bash
# 检查NAT规则是否正确配置
sudo iptables -t nat -L POSTROUTING -n --line-numbers

# 检查FORWARD规则
sudo iptables -L FORWARD -n --line-numbers

# 检查IP转发状态
cat /proc/sys/net/ipv4/ip_forward
```

### 测试内网连通性

```bash
# 从内网设备测试外网连通性
ping -I eth0 *******
ping -I eth1 *******

# 测试DNS解析
nslookup www.baidu.com
```

## 运行时配置修改

### 临时修改参数

```bash
# 禁用NAT功能
ros2 param set /gen3_network_manager enable_nat false

# 修改内网卡列表
ros2 param set /gen3_network_manager lan_interfaces "['eth0']"

# 重新启用NAT功能
ros2 param set /gen3_network_manager enable_nat true
```

### 永久修改配置

修改配置文件后重启服务：

```bash
# 停止服务
ros2 lifecycle set /gen3_network_manager shutdown

# 重新启动
ros2 launch gen3_network_manager_core network_manager.launch.py
```

## 故障排除

### 常见配置错误

1. **YAML语法错误**
   ```
   错误：缩进不正确
   解决：使用空格而不是制表符，保持一致的缩进
   ```

2. **参数类型错误**
   ```
   错误：lan_interfaces: "eth0,eth1"
   正确：lan_interfaces: ["eth0", "eth1"]
   ```

3. **接口名称错误**
   ```
   错误：配置了不存在的网络接口
   解决：使用 ip addr show 检查实际接口名称
   ```

### 配置验证脚本

```bash
#!/bin/bash
# config_check.sh - 配置验证脚本

echo "=== 网络管理器配置检查 ==="

# 检查配置文件是否存在
CONFIG_FILES=(
    "src/gen3_network_manager_core/config/network_config.yaml"
    "src/gen3_network_manager_core/config/development_config.yaml"
    "src/gen3_network_manager_core/config/production_config.yaml"
)

for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo "✓ $config_file 存在"
        
        # 检查NAT配置
        if grep -q "enable_nat:" "$config_file"; then
            echo "✓ $config_file 包含NAT配置"
        else
            echo "✗ $config_file 缺少NAT配置"
        fi
        
        if grep -q "lan_interfaces:" "$config_file"; then
            echo "✓ $config_file 包含内网卡配置"
        else
            echo "✗ $config_file 缺少内网卡配置"
        fi
    else
        echo "✗ $config_file 不存在"
    fi
    echo
done

# 检查网络接口
echo "=== 当前网络接口 ==="
ip addr show | grep "^[0-9]" | awk '{print $2}' | sed 's/://'

echo
echo "=== 配置检查完成 ==="
```

## 最佳实践

### 配置管理

1. **版本控制**：将配置文件纳入版本控制系统
2. **环境分离**：为不同环境维护独立的配置文件
3. **参数验证**：在启动时验证配置参数的有效性
4. **文档同步**：保持配置文档与实际配置的同步

### 部署建议

1. **渐进式部署**：先在测试环境验证配置，再部署到生产环境
2. **回滚准备**：保留配置文件备份，便于快速回滚
3. **监控告警**：监控NAT功能的运行状态，设置异常告警
4. **定期检查**：定期检查NAT规则的正确性和有效性

### 安全考虑

1. **权限控制**：确保配置文件的访问权限正确设置
2. **网络隔离**：合理配置内网卡和外网卡的网络隔离
3. **防火墙规则**：配置适当的防火墙规则保护系统安全
4. **日志审计**：启用详细的日志记录，便于安全审计

## 总结

通过本指南，您可以：

1. **了解新增配置**：掌握NAT功能相关的配置参数
2. **正确更新配置**：按照步骤安全地更新现有配置文件
3. **验证配置效果**：使用提供的方法验证配置是否生效
4. **解决常见问题**：参考故障排除部分解决配置问题

配置更新完成后，网络管理器将具备完整的NAT转发功能，为内网设备提供稳定的外网访问能力。

---

**文档版本**: v1.0  
**创建时间**: 2025年1月  
**维护者**: 网络管理器开发团队
