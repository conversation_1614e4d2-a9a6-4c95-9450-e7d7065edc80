---
type: "manual"
---

# 架构自适应调试配置

本项目支持自动检测和配置不同的系统架构（x86_64、aarch64等），确保VSCode调试配置能够正确指向对应架构的可执行文件，并解决共享库加载问题。

## 问题背景

在多架构环境中，ROS2 项目会遇到以下问题：
1. **路径问题**: 构建输出在不同架构目录下（`install/x86_64/` vs `install/aarch64/`）
2. **共享库加载错误**: 如 `librclcpp_action.so: cannot open shared object file`
3. **环境变量设置**: ROS 环境需要正确配置

## 功能特性

- 自动检测当前系统架构
- 动态更新VSCode launch.json配置文件
- 自动设置 ROS 环境变量
- 解决共享库加载问题
- 支持多架构构建和调试
- 自动备份配置文件

## 使用方法

### 1. 自动更新（推荐）

构建项目时会自动更新架构配置：

```bash
./build.sh
```

构建成功后，脚本会自动：
- 检测当前系统架构
- 更新`.vscode/launch.json`中的所有架构路径
- 创建配置文件备份

### 2. 手动更新

如果需要单独更新架构配置：

```bash
./scripts/update_launch_config.sh
```

### 3. 通过VSCode任务

在VSCode中：
1. 按 `Ctrl+Shift+P`
2. 输入 "Tasks: Run Task"
3. 选择 "update launch config"

## 支持的架构

- x86_64 (Intel/AMD 64位)
- aarch64 (ARM 64位)
- 其他通过 `uname -m` 检测到的架构

## 文件说明

- `scripts/update_launch_config.sh` - 架构配置更新脚本
- `scripts/get_arch.sh` - 架构检测脚本
- `.vscode/launch.json.backup.*` - 自动生成的备份文件

## 注意事项

1. 每次运行更新脚本都会创建带时间戳的备份文件
2. 确保对应架构的构建文件存在于 `install/{arch}/Debug/` 目录下
3. 如果切换到不同架构的机器，需要重新构建项目并更新配置

## VSCode 调试配置

更新后的 `.vscode/launch.json` 包含以下调试配置：

### 当前调试配置
- **ROS2 C++ Debug (Auto-Arch)**: 基础调试配置，支持架构选择
- **ROS2 C++ Debug with Config (Auto-Arch)**: 带网络配置文件的调试
- **ROS2 C++ Debug with Dev Config (Auto-Arch)**: 开发环境调试配置
- **ROS2 C++ Debug (x86_64 Fixed)**: 固定 x86_64 架构基础调试
- **ROS2 C++ Debug with Config (x86_64 Fixed)**: 固定 x86_64 架构带配置调试
- **ROS2 C++ Release Debug (Auto-Arch)**: Release 版本调试
- **ROS2 C++ Attach to Process (Auto-Arch)**: 附加到进程调试
- **ROS2 Launch File Debug**: Launch 文件调试
- **ROS2 Launch with Custom Config**: 自定义配置 Launch 调试

### 配置特点
- **Auto-Arch 配置**: 启动时可选择目标架构，灵活性更高
- **Fixed 配置**: 固定使用当前系统架构，启动更快

### 环境变量设置
所有调试配置自动设置以下环境变量：
- `LD_LIBRARY_PATH`: 包含 ROS Humble 和工作空间库路径
- `AMENT_PREFIX_PATH`: ROS2 包路径（包含 ROS Humble 路径）
- `ROS_DOMAIN_ID`: ROS 域 ID (默认: 0)
- `ROS_DISTRO`: ROS 发行版 (humble)
- `ROS_VERSION`: ROS 版本 (2)

### 使用调试配置
1. 在 VSCode 中按 `F5` 或点击 "Run and Debug"
2. 选择合适的调试配置：
   - 开发调试：选择 "Auto-Arch" 配置
   - 特定架构：选择 "Fixed" 配置
   - Launch 文件：选择 "Launch" 配置
3. 如果使用 "Auto-Arch" 配置，会提示选择架构（默认为 x86_64）

## 故障排除

### 共享库加载错误
如果遇到 `librclcpp_action.so: cannot open shared object file` 错误：
1. 确保 ROS Humble 已正确安装：`ls /opt/ros/humble/lib/`
2. 运行更新脚本：`./scripts/update_launch_config.sh`
3. 使用启动脚本：`./run_network_manager.sh`

### 程序不存在错误
如果遇到 "program does not exist" 错误：
1. 检查是否已构建项目：`./build.sh`
2. 确认架构配置是否正确：`./scripts/update_launch_config.sh`
3. 验证可执行文件是否存在：`ls install/$(uname -m)/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/`

### 架构不匹配
如果在不同架构机器上工作：
1. 重新构建项目：`./build.sh`
2. 更新配置：`./scripts/update_launch_config.sh`
3. 验证路径：检查 `install/` 目录下是否有对应架构的文件夹
