# 机器人网络相关需求整理

基于机器人的硬件架构（主模块集成 WiFi/5G 模组，多板通过交换机共享网络），结合 "自动连接 - 智能切换 - 安全绑定 - NAT转发" 的核心目标，以下从功能拆解、硬件协同、关键问题三个维度详细分析后续工作：

## 零、NAT网络转发需求（新增）

### 1. 核心功能目标

机器人作为网关设备，需要为内网设备（多个子板、传感器等）提供外网访问能力。当WiFi和5G网络切换时，确保内网设备能够无缝访问互联网。

### 2. 关键需求分析

#### （1）内网卡和外网卡管理
- **外网卡识别**：自动识别WiFi接口（wlan0）和5G接口（eth2）作为外网卡
- **内网卡配置**：支持配置多个内网接口（如eth0、eth1）连接内网设备
- **接口状态监控**：实时监控网络接口状态变化，自动更新NAT规则

#### （2）NAT规则自动管理
- **启动时配置**：系统启动时根据当前网络状态自动配置NAT规则
- **切换时更新**：WiFi和5G切换时自动更新NAT转发规则
- **状态变化响应**：网络接口变化时自动调整NAT配置
- **规则清理**：切换到非外网卡时自动清理NAT规则

#### （3）iptables规则管理
- **MASQUERADE规则**：为外网接口配置源NAT，隐藏内网设备IP
- **FORWARD规则**：允许内网到外网的流量转发和返回流量
- **IP转发启用**：自动启用系统级IP转发功能
- **规则持久化**：确保NAT规则在系统重启后保持有效

### 3. 技术实现要求

#### （1）配置管理
```yaml
# NAT和内网卡配置
enable_nat: true                    # 启用NAT功能
lan_interfaces: ["eth0", "eth1"]    # 内网卡列表
```

#### （2）自动化程度
- **零配置**：用户无需手动配置iptables规则
- **自适应**：根据网络环境自动调整NAT配置
- **容错性**：NAT配置失败不影响网络切换主流程

#### （3）企业级特性
- **多内网卡支持**：支持配置多个内网接口
- **权限管理**：需要root权限执行iptables操作
- **日志记录**：详细记录NAT规则配置过程和结果

## 零点一、网络质量检查优化需求（新增）

### 1. DNS解析时间真实测量需求

#### 核心问题
- 原有系统使用固定DNS解析时间值（50ms或1000ms）
- 无法反映实际网络环境的DNS性能差异
- 影响网络质量评分的准确性

#### 功能需求
- **多域名测试**：测试百度、QQ、网易等国内主流域名
- **精确时间测量**：使用`std::chrono::steady_clock`进行毫秒级测量
- **平均时间计算**：计算所有成功解析域名的平均时间
- **容错机制**：单个域名解析失败不影响整体测试

#### 技术接口需求
```cpp
std::tuple<bool, double> test_dns_resolution_with_time(
    const std::vector<std::string>& domains, int timeout_ms);
```

#### 预期效果
- DNS解析时间从固定值变为真实测量值（通常30-60ms）
- 网络质量评分更加准确和动态
- 支持DNS解析时间修正评分机制

### 2. 5G信号强度异常值修复需求

#### 核心问题
- 5G信号强度显示异常值（如-1004116603dBm）
- 串口通信失败时信号强度保持无效初始值
- 缺乏有效性检查和边界保护

#### 功能需求
- **多层防护机制**：数据源验证、选择逻辑验证、最终结果验证
- **智能信号强度选择**：根据网络制式选择最佳指标（RSRP/RSCP/RSSI）
- **边界检查**：信号强度必须在-150dBm到-30dBm范围内
- **异常值修复**：将异常值重置为合理默认值-100dBm

#### 技术实现需求
```cpp
// 多层验证机制
if (cell_info.rsrp != -999 && cell_info.rsrp >= -150 && cell_info.rsrp <= -30) {
    primary_signal = cell_info.rsrp;
} else if (cell_info.rscp != -999 && cell_info.rscp >= -150 && cell_info.rscp <= -30) {
    primary_signal = cell_info.rscp;
} else if (cell_info.rssi != -999 && cell_info.rssi >= -150 && cell_info.rssi <= -30) {
    primary_signal = cell_info.rssi;
}

// 最终有效性检查
if (primary_signal == -999 || primary_signal < -150 || primary_signal > -30) {
    primary_signal = -100;  // 合理的默认值
}
```

#### 预期效果
- 5G信号强度显示正常（如-85dBm）
- 通信失败时自动修复异常历史值
- 详细的调试日志便于问题排查

### 3. 网络质量检查简化需求

#### 核心问题
- 网络质量检查包含冗余的测试项目
- 网关可达性和互联网可访问性测试增加系统负载
- 评分逻辑复杂，维护成本高

#### 功能需求
- **删除冗余测试**：移除网关可达性和互联网可访问性测试
- **简化评分逻辑**：DNS解析权重从30%提升到60%
- **统一业务逻辑**：所有组件基于DNS状态判断连通性
- **早期失败检测**：信号强度为0时立即返回失败结果

#### 技术实现需求
```cpp
// 简化后的连通性评分
if (quality.dns_working) {
    score += 60.0;  // DNS解析承担更多权重
}

// 早期失败检测
if (quality.signal_strength == 0) {
    return create_failed_quality_result();
}
```

#### 预期效果
- 网络质量检查速度提升约30%
- 系统负载降低，减少并发网络连接
- 评分逻辑更清晰，专注核心指标

### 4. 系统性能优化需求

#### 核心目标
- 提升网络管理器整体性能和响应速度
- 优化日志系统，提高可读性和处理效率
- 减少系统资源消耗，提高稳定性

#### 功能需求
- **快速失败机制**：无效网络状态时跳过后续测试
- **日志简化**：删除冗余字段，专注核心信息
- **资源优化**：减少不必要的网络连接和系统调用
- **内存优化**：优化数据结构，减少内存占用

#### 预期效果
- 系统响应时间缩短
- 日志存储空间节省
- CPU和内存使用率降低

## 一、WiFi 自动连接逻辑：

### 1. 核心功能目标

机器人需在移动过程中自动连接已知 WiFi 网络，或快速适配新网络，并将连接状态同步到全系统（多板通过交换机共享），避免单板断网。

### 2. 关键任务拆解

#### （1）已知网络的自动连接策略设计

**网络列表管理：**

- 在主模块（WiFi/5G 模组所在板）本地存储 "已信任 WiFi 列表"，包含：
  - SSID
  - 密码
  - 加密方式（WPA2/WPA3）
  - **绑定时间**：记录网络添加时间，用于优先级排序
  - **绑定来源**：标记是否通过绑定流程添加（绑定来源的网络优先级最高）
  - 连接历史（连接次数、最近连接时间、成功率）
  - 信号阈值（该网络的最低可接受信号强度）
- 支持列表持久化，防止断电丢失
- 通过交换机同步到其他子板（需设计 "网络状态广播协议"，如通过 UDP 定期发送列表摘要，确保子板感知可用网络）

**连接优先级排序：**

- 定义多维度优先级规则（可配置），按优先级从高到低：
  1. **高优先级**：最近绑定的 WiFi 网络（通过绑定流程新添加的网络）
  2. **中优先级**：最近成功连接的网络（按连接时间排序）
  3. **低优先级**：历史连接过的网络（按连接次数和信号强度排序）
- **优先级动态更新**：
  - 每次通过绑定流程添加新网络时，自动成为最高优先级
  - 多个绑定网络按绑定时间排序，最新绑定的优先级最高
  - 非绑定网络的优先级不会超过任何绑定网络

**连接流程优化：**

- **启动连接**：主模块启动/唤醒时，扫描周围 WiFi 并按优先级排序：
  1. 优先尝试最近绑定的 WiFi（绑定时间最新的网络）
  2. 其次尝试用户标记的重要网络
  3. 最后按历史连接记录排序尝试
- **连接超时控制**：单次连接超时 10-20 秒，失败后跳至下一优先级网络
- **智能重连**：若当前 WiFi 信号弱（<-75dBm），优先扫描最近绑定的网络，如信号更强则自动切换
- **绑定网络保护**：最近绑定的网络即使连接失败也会保持最高优先级，避免因临时故障被降级

#### （2）新网络的快速适配机制

- **触发方式**：支持通过 "绑定设备（如手机）远程配置"（蓝牙/BLE、二维码）或 "本地物理交互"（ssh登陆配置）添加新 WiFi
- **配置同步**：新网络添加后，主模块立即：
  1. 将新网络标记为 "最近绑定"，设置最高优先级
  2. 更新 "已信任列表"，记录绑定时间和来源
  3. 通过交换机向子板推送网络状态更新
- **认证容错**：若输入密码错误，主模块通过绑定设备反馈 "wifi连接失败"（如、语音播报），支持重试 3 次后退出配置流程

#### （3）多板网络共享架构

**核心设计原则：**

- **主模块（网络板）**：唯一具备 WiFi/5G 模组的板子，负责所有网络连接和切换
- **其他子板**：通过交换机共享网络，无需独立网络配置，只负责业务逻辑

**网络共享实现：**

- **统一网关**：主模块作为唯一网络出口，所有子板的网络请求通过交换机路由到主模块
- **状态同步**：主模块定期向子板广播网络状态（连接状态、网络类型、信号强度）
- **切换通知**：网络切换时主模块及时通知子板，确保长连接能够快速重连
- **透明代理**：子板无需感知具体使用 WiFi 还是 5G，但需要响应切换通知进行重连
- **故障处理**：网络异常时，子板启用本地缓存策略，待网络恢复后重发请求并重建长连接

## 二、5G 与 WiFi 的自动切换逻辑：

### 1. 切换核心目标

在保证机器人业务连续性（如远程控制、数据回传）的前提下，根据 "网络质量、场景需求、用户配置" 自动选择最优网络（WiFi 优先还是 5G 优先），且切换过程对多板共享网络无感知（无断连）。

### 2. 关键任务拆解

#### （1）网络质量检测与切换触发条件

**检测机制：**
- **检测频率**：每5秒进行一次网络质量评估
- **检测内容**：信号强度、网络连通性、DNS解析、延迟测试
- **自动切换**：根据检测结果自动选择最优网络

**详细检测指标：**

| 网络类型 | 评估指标（触发切换阈值） | 检测方法 | 说明 |
|---------|----------------------|----------|------|
| **WiFi** | • **信号强度**：<-80dBm（弱信号）<br>• **网络连通性**：连续3次ping网关失败<br>• **DNS解析**：连续3次DNS查询失败（如*******、***************）<br>• **延迟测试**：>200ms（持续15秒，影响远程控制）<br>• **带宽测试**：<1Mbps（持续10秒，无法满足基本需求） | • 信号强度：通过WiFi模组API获取RSSI<br>• 连通性：ping默认网关<br>• DNS：nslookup测试多个DNS服务器<br>• 延迟：ping云端服务器<br>• 带宽：小文件下载测试 | 若满足任意1项，触发"切向5G" |
| **5G** | • **信号强度**：<=-100dBm（弱覆盖）<br>• **网络连通性**：连续3次ping网关失败<br>• **DNS解析**：连续3次DNS查询失败<br>• **流量限制**：剩余流量<10%<br>• **WiFi可用**：检测到已信任的高优先级WiFi（信号>-70dBm且DNS正常） | • 信号强度：通过5G模组API获取信号质量<br>• 连通性：ping运营商网关<br>• DNS：测试运营商DNS和公共DNS<br>• 流量：查询模组流量统计<br>• WiFi扫描：定期扫描可用WiFi | 若满足任意1项，触发"切向WiFi" |

**DNS检测详细机制：**
- **多DNS服务器测试**：
  - 主DNS：*******（Google）
  - 备DNS：***************（国内）
  - 运营商DNS：自动获取
- **检测域名**：
  - 云端服务器域名（业务相关）
  - 公共域名：baidu.com、qq.com
- **故障判定**：连续3次所有DNS服务器都无法解析时判定为DNS故障
- **自动切换DNS**：检测到DNS故障时，自动切换到备用DNS服务器

#### （2）无缝切换流程设计（主模块执行，子板协同重连）

**切换执行者：仅主模块（网络板）**

- 其他子板通过交换机共享网络，无需参与切换逻辑
- 但需要通知子板进行长连接的快速重连

**切换流程：**

- **预连接**：主模块先建立目标网络连接（如从 WiFi 切 5G），确认可用性
- **网络质量验证**：对目标网络进行完整检测：
  - 信号强度测试
  - 网关连通性测试（ping网关）
  - **DNS解析测试**：测试多个DNS服务器和域名解析
  - 延迟和带宽测试
  - 云端服务器连通性测试
- **DNS配置优化**：根据网络类型自动配置最优DNS：
  - WiFi网络：优先使用路由器DNS，备用*******
  - 5G网络：优先使用运营商DNS，备用***************
  - 自动检测DNS响应速度，选择最快的DNS服务器
- **切换预告**：向子板发送 "网络即将切换" 通知，包含：
  - 切换类型（WiFi→5G 或 5G→WiFi）
  - 预计切换时间（如 2 秒后）
  - 新网络的基本信息（IP段变化、网关、DNS服务器等）
- **子板准备**：子板收到通知后：
  - 暂停新的长连接建立
  - 记录当前活跃的长连接状态（连接ID、服务器地址、认证信息）
  - 准备重连参数和策略
- **路由切换**：主模块内部切换网络出口
- **切换完成通知**：向子板广播 "网络切换完成"，包含：
  - 新网络状态（连接类型、IP地址、信号强度）
  - DNS服务器配置信息
  - 切换完成时间戳
- **长连接重建**：子板收到完成通知后：
  - 更新DNS配置（如果需要）
  - 立即重新建立之前的长连接
  - 使用新的网络路径进行连接
  - 恢复业务数据传输和心跳机制

#### （3）特殊场景适配

- **移动场景**：机器人快速移动时（如从室内到室外），5G 信号通常更稳定，可配置 "移动速度 > 0.5m/s 时，强制优先 5G"（通过机器人运动传感器数据触发）
- **静态高负载场景**：机器人在充电站充电时（静态），若需上传大量数据（如高清日志、视频），自动切换至 WiFi（速率更高、无流量成本）
- **切换失败回退**：若切换目标网络失败（如 5G 注册超时），主模块立即恢复原网络连接，向子板发送 "切换失败，维持原网络" 信号，避免长时间断网

#### （4）DNS智能管理与自动切换

**DNS服务器配置策略：**
- **WiFi网络DNS配置**：
  - 主DNS：路由器分配的DNS（通过DHCP获取）
  - 备DNS1：*******（Google公共DNS）
  - 备DNS2：***************（国内公共DNS）
  - 备DNS3：1.1.1.1（Cloudflare DNS）

- **5G网络DNS配置**：
  - 主DNS：运营商DNS（通过网络注册获取）
  - 备DNS1：***************（国内公共DNS，适合5G网络）
  - 备DNS2：*******（Google公共DNS）
  - 备DNS3：223.5.5.5（阿里公共DNS）

**DNS自动检测与切换机制：**
- **实时监控**：每30秒检测当前DNS服务器响应状态
- **性能评估**：测试DNS解析速度和成功率
- **自动切换条件**：
  - 当前DNS连续3次解析失败
  - DNS解析延迟>2秒（持续1分钟）
  - DNS解析成功率<80%（5分钟统计）
- **切换流程**：
  1. 检测到DNS异常
  2. 自动切换到备用DNS服务器
  3. 验证新DNS服务器可用性
  4. 通知子板更新DNS配置
  5. 记录切换日志和原因

**DNS故障恢复机制：**
- **定期重试**：每5分钟重试原主DNS服务器
- **性能对比**：对比主DNS和备DNS的性能
- **智能回切**：当主DNS恢复且性能更优时自动回切
- **故障记录**：记录DNS故障时间、原因、切换历史

#### （5）长连接快速重连机制

**重连触发时机：**
- 网络切换完成后（WiFi↔5G）
- 网络异常恢复后
- IP地址发生变化时
- **DNS服务器切换后**
- **DNS解析恢复后**

**子板重连策略：**
- **连接状态管理**：
  - 维护活跃长连接列表（WebSocket、TCP长连接、MQTT等）
  - 记录连接参数（服务器地址、端口、认证token、重连间隔）
  - 监控连接健康状态（心跳、数据传输）

- **快速重连流程**：
  1. **接收切换通知**：收到主模块的网络切换预告
  2. **连接状态保存**：记录当前所有长连接的状态和参数
  3. **等待切换完成**：暂停新连接，等待网络切换完成通知
  4. **DNS配置更新**：根据新网络类型更新本地DNS配置
  5. **域名重新解析**：对所有服务器地址进行DNS重新解析
  6. **立即重连**：使用新的IP地址并发重建所有长连接
  7. **状态恢复**：恢复数据传输、重新同步状态、重启心跳

- **重连优化**：
  - **并发重连**：同时重建多个长连接，减少总重连时间
  - **优先级重连**：关键业务连接优先重建（如控制指令通道）
  - **重连重试**：连接失败时采用指数退避策略重试
  - **状态同步**：重连成功后主动同步业务状态，避免数据丢失

## 三、绑定配网逻辑：基于多方式（BLE / 二维码 ）的安全认证

### 1. 核心目标

实现机器人与用户设备（手机、遥控器）的安全绑定，**主要目的是完成初始化配置流程**：

1. **WiFi网络配置**：通过绑定设备向机器人推送WiFi信息，实现联网
2. **平台设备注册**：网络连接成功后，将机器人注册到云端管理平台
3. **权限管理**：建立设备控制权限，限制未绑定设备的访问

### 2. 绑定流程总体架构

**BLE绑定流程：**

```
机器人进入绑定模式 → BLE广播 → 手机连接 → WiFi配置推送 → 网络连接 → 平台注册 → 绑定完成
```

**二维码绑定流程：**

```
手机APP配置WiFi → 生成二维码 → 机器人扫码识别 → 解析配置 → 网络连接 → 平台注册 → 绑定完成
```

**关键节点说明：**

- **绑定触发**：物理按键激活绑定模式，或手机APP主动发起绑定
- **配置传输**：
  - BLE方式：手机直接推送WiFi配置到机器人
  - 二维码方式：手机生成包含WiFi信息的二维码，机器人摄像头识别
- **网络验证**：验证WiFi连接是否成功，确保网络可用
- **平台对接**：向云端平台注册设备，获取管理权限
- **状态同步**：将绑定状态同步到所有子板

### 3. 关键任务拆解

#### （1）绑定方式适配（多方式冗余，提升易用性）

**蓝牙 BLE 绑定（主流方式）：**

- **硬件适配**：确认 BLE 模组功能（待确认）；驱动层需优化 BLE 广播频率（绑定模式下每秒广播 3 次，降低功耗）、连接稳定性（处理机器人运动中的信号波动）
- **流程设计**：
  1. **进入绑定模式**：机器人默认进入绑定模式（或者语音触发重新进行绑定模式），BLE 广播含设备唯一 ID 的信号
  2. **建立连接**：用户手机 APP 扫描 BLE 信号，建立安全连接
  3. **WiFi配置**：手机 APP 向机器人推送 WiFi 配置信息（SSID、密码、加密方式）
  4. **网络连接**：机器人主模块尝试连接指定 WiFi，成功后反馈连接状态
  5. **平台注册**：网络连接成功后，机器人自动向云端平台发起设备注册请求
  6. **绑定确认**：平台返回注册成功后，机器人通过语音/灯光反馈绑定结果，手机 APP 同步显示绑定状态

**二维码绑定：**

- **硬件依赖**：机器人需配备摄像头（用于识别手机生成的二维码）
- **流程设计**：
  1. **触发绑定**：用户在手机 APP 中选择 "添加设备" 或 "绑定机器人"
  2. **WiFi配置**：手机 APP 引导用户输入或选择当前 WiFi 网络信息（SSID、密码、加密方式）
  3. **生成二维码**：APP 将 WiFi 配置信息和绑定标识打包生成二维码显示在手机屏幕
  4. **机器人识别**：机器人进入绑定模式，摄像头扫描识别手机显示的二维码
  5. **解析配置**：机器人解析二维码获取 WiFi 配置信息和绑定请求
  6. **网络连接**：机器人尝试连接指定 WiFi 网络
  7. **平台注册**：WiFi 连接成功后，向云端平台发起设备注册
  8. **绑定确认**：注册成功后，机器人通过语音/灯光反馈绑定结果，手机 APP 同步显示绑定状态

**二维码数据格式设计参考格式：**

```json
{
  "type": "robot_binding",
  "wifi": {
    "ssid": "WiFi网络名称",
    "password": "WiFi密码",
    "security": "WPA2/WPA3",
    "hidden": false
  },
  "binding": {
    "device_id": "手机设备标识",
    "user_info": "用户信息",
    "timestamp": "生成时间戳",
    "expire": "有效期（5分钟）"
  }
}
```

**技术要点：**

- 二维码采用 JSON 格式封装数据，便于解析和扩展
- 包含完整的 WiFi 连接信息和绑定标识
- 机器人需具备 JSON 解析和 WiFi 配置能力
#### （2）解绑逻辑与权限控制

- **解绑触发**：支持 "绑定设备主动发起"（如手机 APP 操作）或 "机器人本地强制解绑"（语音操作）
- **状态同步**：解绑后，主模块立即删除绑定关系，通过交换机向所有子板广播 "解绑完成"，子板需立即终止所有来自该设备的控制指令（如停止执行远程动作）

#### （3）绑定完成后的系统状态

- **网络配置完成**：机器人已连接到指定 WiFi 网络，具备基本联网能力
- **平台注册完成**：设备已在云端平台注册，可接收远程管理指令
- **权限建立**：绑定设备获得机器人控制权限，可进行后续配置和操作
- **状态同步**：绑定信息同步到所有子板，统一权限管理
- **安全存储**：绑定关系和网络配置信息加密存储，确保安全性

#### （4）绑定失败的处理机制

- **WiFi连接失败**：提示用户检查网络信息，支持重新配置
- **平台注册失败**：记录失败原因，支持离线模式运行，网络恢复后自动重试
- **超时处理**：绑定流程设置总超时时间（如 10 分钟），超时后自动退出绑定模式

## 四、硬件协同与潜在问题解决

### 1. 网络架构设计原则

**分工明确：**

- **主模块（网络板）**：
  - 集成 WiFi/5G 模组
  - 负责网络连接、切换、绑定配网
  - 作为系统唯一网络网关
- **其他子板**：
  - 专注业务逻辑处理
  - 通过交换机共享网络
  - 无需网络配置和切换功能

**技术保障：**

- **统一路由**：所有子板网络请求通过主模块转发，避免直接访问网络模组
- **时钟同步**：主模块负责系统授时，确保多板时间一致性

### 2. 潜在问题与应对

**主模块（网络板）相关：**

- **网络切换延迟**：采用 "先连后断" 策略，确保切换过程中网络服务不中断
- **DNS解析失败**：
  - 问题：DNS服务器故障导致域名无法解析
  - 解决：自动切换到备用DNS服务器，维护多个DNS服务器列表
- **DNS劫持/污染**：
  - 问题：某些网络环境下DNS被劫持或污染
  - 解决：使用多个不同的DNS服务器进行交叉验证，检测异常结果
- **DNS解析缓存问题**：
  - 问题：DNS缓存导致IP地址更新不及时
  - 解决：网络切换时清空DNS缓存，强制重新解析

**子板相关：**

- **网络请求失败**：子板检测到网络异常时启用本地缓存，待网络恢复后重发
- **状态同步延迟**：通过心跳机制确保子板及时感知网络状态变化
- **域名解析失败**：
  - 问题：子板无法解析服务器域名
  - 解决：从主模块同步DNS配置，使用统一的DNS服务器
- **长连接重连失败**：
  - 问题：网络切换后域名解析到旧IP导致连接失败
  - 解决：重连前强制进行DNS重新解析，获取最新IP地址

---

## 文档版本历史

### v2.0 (2025-07-27)
- 新增NAT网络转发需求分析
- 新增网络质量检查优化需求
- 新增DNS解析时间真实测量需求
- 新增5G信号强度异常值修复需求
- 新增系统性能优化需求
- 完善技术实现要求和预期效果

### v1.0 (2025-07-06)
- 初始版本发布
- 完整的网络需求分析
- WiFi自动连接逻辑
- 智能切换策略
- 安全绑定机制

---

**文档版本**: v2.0
**最后更新**: 2025-07-27
**维护者**: 网络管理器开发团队
