#!/bin/bash

# 网络管理器节点启动脚本
# 此脚本会自动检测架构、设置 ROS 环境并启动网络管理器节点

echo "正在设置 ROS 环境..."

# 获取当前系统架构
CURRENT_ARCH=$(uname -m)
echo "检测到当前架构: $CURRENT_ARCH"

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

# Source ROS Humble 环境
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    echo "✓ ROS Humble 环境已加载"
else
    echo "✗ 错误：未找到 ROS Humble 安装"
    exit 1
fi

# Source 工作空间环境（使用动态架构路径）
WORKSPACE_SETUP="install/$CURRENT_ARCH/Debug/setup.bash"
if [ -f "$WORKSPACE_SETUP" ]; then
    source "$WORKSPACE_SETUP"
    echo "✓ 工作空间环境已加载 ($CURRENT_ARCH)"
else
    echo "✗ 错误：未找到工作空间安装文件: $WORKSPACE_SETUP"
    echo "请先运行 colcon build 构建项目"
    exit 1
fi

# 配置文件路径
CONFIG_FILE="$PROJECT_DIR/src/gen3_network_manager_core/config/network_config.yaml"
echo "配置文件: $CONFIG_FILE"

# 从配置文件中读取日志级别
LOG_LEVEL="debug"  # 默认值
if [ -f "$CONFIG_FILE" ]; then
    echo "尝试从配置文件读取日志级别..."
    # 使用grep和sed提取日志级别
    EXTRACTED_LOG_LEVEL=$(grep "log_level:" "$CONFIG_FILE" | head -n 1 | sed 's/.*log_level:[[:space:]]*"\([^"]*\)".*/\1/')
    if [ -n "$EXTRACTED_LOG_LEVEL" ]; then
        LOG_LEVEL="$EXTRACTED_LOG_LEVEL"
        echo "✓ 从配置文件读取到日志级别: $LOG_LEVEL"
    else
        echo "⚠ 无法从配置文件读取日志级别，使用默认值: $LOG_LEVEL"
    fi
else
    echo "⚠ 配置文件不存在，使用默认日志级别: $LOG_LEVEL"
fi

# 设置ROS2日志级别环境变量
export RCUTILS_CONSOLE_OUTPUT_FORMAT='[{severity}] [{time}] [{name}]: {message}'
export RCUTILS_COLORIZED_OUTPUT=1
export RCUTILS_LOGGING_USE_STDOUT=1
export RCUTILS_LOGGING_BUFFERED_STREAM=1

# 明确设置日志级别环境变量
echo "设置日志级别环境变量为: $LOG_LEVEL"
export RCUTILS_LOGGING_LEVEL=$LOG_LEVEL
export ROS_LOG_LEVEL=$LOG_LEVEL

# 检查可执行文件是否存在（使用动态架构路径）
EXECUTABLE="install/$CURRENT_ARCH/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node"
if [ -f "$EXECUTABLE" ]; then
    echo "✓ 找到网络管理器节点可执行文件 ($CURRENT_ARCH)"
    echo "正在启动网络管理器节点..."
    echo "----------------------------------------"

    # 显示环境信息
    echo "架构: $CURRENT_ARCH"
    echo "ROS版本: $ROS_DISTRO"
    echo "可执行文件: $EXECUTABLE"
    echo "配置文件: $CONFIG_FILE"
    echo "日志级别: $LOG_LEVEL"
    echo "----------------------------------------"

    # 运行网络管理器节点，并明确指定日志级别和配置文件
    "$EXECUTABLE" --ros-args --log-level $LOG_LEVEL --params-file "$CONFIG_FILE"
else
    echo "✗ 错误：未找到网络管理器节点可执行文件"
    echo "路径：$EXECUTABLE"
    echo "请先运行 colcon build 构建项目"
    exit 1
fi
