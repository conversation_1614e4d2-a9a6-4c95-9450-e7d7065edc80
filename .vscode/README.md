# VS Code 调试配置说明

本目录包含了为 ROS2 C++ 项目优化的 VS Code 配置文件。

## 文件说明

### launch.json - 调试配置
包含以下调试配置：

#### 基础调试配置
1. **ROS2 C++ Debug (aarch64)** - ARM64 架构的 Debug 模式调试
2. **ROS2 C++ Debug (x86_64)** - x86_64 架构的 Debug 模式调试
3. **ROS2 C++ Release Debug (aarch64)** - ARM64 架构的 Release 模式调试
4. **ROS2 C++ Attach to Process** - 附加到正在运行的进程进行调试

#### 带配置文件的调试配置
5. **ROS2 C++ Debug with Config (aarch64)** - 使用默认配置文件的 ARM64 调试
6. **ROS2 C++ Debug with Dev Config (aarch64)** - 使用开发配置文件的 ARM64 调试
7. **ROS2 C++ Debug with Config (x86_64)** - 使用默认配置文件的 x86_64 调试

#### Launch 文件调试配置
8. **ROS2 Launch File Debug** - 通过 launch 文件启动调试
9. **ROS2 Launch with Custom Config** - 通过 launch 文件使用自定义配置启动调试

### tasks.json - 构建任务
包含以下构建任务：

#### 构建任务
- `colcon build` - 默认构建任务
- `colcon build release` - Release 模式构建
- `colcon build clean` - 清理后构建
- `colcon build verbose` - 详细输出构建

#### 运行任务
- `run network_manager_node` - 运行网络管理节点（无配置文件）
- `run network_manager_node with config` - 使用默认配置文件运行
- `run network_manager_node with dev config` - 使用开发配置文件运行

#### Launch 任务
- `launch network_manager` - 启动网络管理器（默认配置）
- `launch network_manager with dev config` - 使用开发配置启动
- `launch network_manager with production config` - 使用生产配置启动

#### 调试任务
- `ros2 topic list` - 列出 ROS2 话题
- `ros2 node list` - 列出 ROS2 节点

### c_cpp_properties.json - C++ IntelliSense 配置
配置了：
- 包含路径
- 编译器路径
- C++ 标准
- compile_commands.json 支持

### settings.json - 工作区设置
包含：
- C/C++ 开发设置
- Python 路径配置
- 文件关联
- 搜索排除规则
- ROS 相关设置

### extensions.json - 推荐扩展
推荐安装的 VS Code 扩展，包括：
- C/C++ 开发工具
- Python 支持
- ROS 扩展
- Git 工具
- 调试工具

## 使用方法

### 1. 构建项目
按 `Ctrl+Shift+P` 打开命令面板，输入 "Tasks: Run Task"，选择 "colcon build"

或者按 `Ctrl+Shift+B` 运行默认构建任务

### 2. 调试程序

#### 基础调试
1. 在代码中设置断点
2. 按 `F5` 或点击调试面板的运行按钮
3. 选择合适的调试配置（根据你的架构选择 aarch64 或 x86_64）

#### 带配置文件调试
1. 选择 "ROS2 C++ Debug with Config" 或 "ROS2 C++ Debug with Dev Config"
2. 这些配置会自动加载相应的 YAML 配置文件
3. 支持的配置文件：
   - `network_config.yaml` - 默认配置
   - `development_config.yaml` - 开发配置
   - `production_config.yaml` - 生产配置

#### Launch 文件调试
1. 选择 "ROS2 Launch File Debug" 使用默认 launch 配置
2. 选择 "ROS2 Launch with Custom Config" 使用自定义配置文件

### 3. 运行节点

#### 不带配置文件
使用任务 "run network_manager_node" 或 "launch network_manager"

#### 带配置文件
- "run network_manager_node with config" - 使用默认配置
- "run network_manager_node with dev config" - 使用开发配置
- "launch network_manager with dev config" - 通过 launch 文件使用开发配置
- "launch network_manager with production config" - 通过 launch 文件使用生产配置

### 4. 查看 ROS2 信息
使用 "ros2 topic list" 或 "ros2 node list" 任务

## 配置文件说明

项目包含三个配置文件：

1. **network_config.yaml** - 默认配置文件
   - 包含基本的网络管理参数
   - 适用于一般使用场景

2. **development_config.yaml** - 开发配置文件
   - 包含开发调试相关的参数
   - 通常有更详细的日志输出
   - 适用于开发和调试

3. **production_config.yaml** - 生产配置文件
   - 包含生产环境的优化参数
   - 适用于实际部署环境

### 配置文件参数说明
- `network_check_interval`: 网络检查间隔（秒）
- `quality_check_interval`: 质量检查间隔（秒）
- `enable_auto_switch`: 是否启用自动切换
- `preferred_network_type`: 首选网络类型
- `wifi_interface`: WiFi 接口名称
- `ethernet_interface`: 以太网接口名称
- `5g_interface`: 5G 接口名称

## 注意事项

1. 确保已经安装了推荐的扩展
2. 首次使用前需要运行构建任务
3. 调试前确保程序已经成功编译
4. 如果遇到路径问题，检查 install 目录是否存在对应架构的文件
5. 配置文件会在构建时自动复制到 install 目录
6. 调试时确保选择正确的架构配置（aarch64 或 x86_64）

## 故障排除

### 找不到可执行文件
- 检查构建是否成功
- 确认架构设置正确（aarch64 或 x86_64）
- 检查 install 目录下是否有对应的可执行文件

### IntelliSense 不工作
- 确保安装了 C/C++ 扩展
- 检查 compile_commands.json 是否存在
- 重新加载 VS Code 窗口

### 调试器无法启动
- 检查 gdb 是否安装：`which gdb`
- 确认程序有调试符号（Debug 模式编译）
- 检查环境变量设置是否正确
