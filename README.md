# ROS2 网络管理器 (Network99)

一个用于管理网络连接和切换的 ROS2 项目，支持多架构部署和调试。

## 🚀 快速开始

### 构建项目
```bash
./build.sh                    # 自动设置ROS2环境并构建所有包
./build.sh --release          # Release模式构建
./build.sh package_name        # 构建指定包
./build.sh --skip-ros-setup    # 跳过ROS2环境自动设置
```

### 运行程序
```bash
./run_network_manager.sh  # 自动检测架构并设置环境
```

### VSCode 调试
1. 按 `F5` 开始调试
2. 选择 `ROS2 C++ Debug (Auto-Arch)` 配置
3. 选择目标架构（默认当前架构）

## 🔧 调试配置

本项目提供完整的 VSCode 调试支持，解决了 ROS2 共享库加载问题：

### 可用配置
- **基础调试**: `ROS2 C++ Debug (Auto-Arch)`
- **带配置调试**: `ROS2 C++ Debug with Config (Auto-Arch)`
- **开发调试**: `ROS2 C++ Debug with Dev Config (Auto-Arch)`
- **Release调试**: `ROS2 C++ Release Debug (Auto-Arch)`
- **进程附加**: `ROS2 C++ Attach to Process (Auto-Arch)`
- **Launch调试**: `ROS2 Launch File Debug`

### 架构支持
- **x86_64**: Intel/AMD 64位处理器
- **aarch64**: ARM 64位处理器（树莓派、Jetson等）
- **arm64**: ARM 64位处理器（macOS Apple Silicon）

## 🛠️ 工具脚本

| 脚本 | 功能 |
|------|------|
| `build.sh` | 构建项目（自动设置ROS2环境） |
| `run_network_manager.sh` | 启动网络管理器（架构自适应） |
| `tests/run-all-tests.sh` | 运行完整测试套件 |
| `tests/debug-config-test.sh` | 验证调试配置 |
| `scripts/get_arch.sh` | 获取当前架构 |
| `scripts/update_launch_config.sh` | 更新调试配置（自动备份） |
| `scripts/backup_launch_json.sh` | 备份 launch.json 配置 |
| `scripts/restore_launch_json.sh` | 恢复 launch.json 配置 |
| `scripts/manage_launch_backups.sh` | 管理备份文件（最多3个） |

## 🐛 故障排除

### ROS2 环境问题
如果构建时提示找不到 ROS2 环境：
```bash
# 方法1: 让脚本自动检测（推荐）
./build.sh

# 方法2: 手动设置环境
source /opt/ros/humble/setup.bash
./build.sh --skip-ros-setup

# 方法3: 检查 ROS2 安装
ls /opt/ros/
```

### 共享库错误
如果遇到 `librclcpp_action.so: cannot open shared object file` 错误：
```bash
./scripts/update_launch_config.sh  # 更新配置
./run_network_manager.sh           # 使用启动脚本
```

### 程序不存在错误
如果调试时找不到程序：
```bash
./build.sh                         # 重新构建
./scripts/update_launch_config.sh  # 更新配置
```

### 验证配置
```bash
./tests/run-all-tests.sh  # 运行完整测试套件
./tests/debug-config-test.sh  # 验证调试配置
```

## 📚 文档

### 主要文档
- [`docs/README.md`](docs/README.md) - 📖 文档索引和导航
- [`docs/调试配置指南.md`](docs/调试配置指南.md) - 🔧 完整调试配置指南
- [`docs/架构配置说明.md`](docs/架构配置说明.md) - 🏗️ 架构自适应配置说明
- [`DEBUG_CONFIG_SUMMARY.md`](DEBUG_CONFIG_SUMMARY.md) - 📋 配置更新总结

### 分类文档
- **功能特性**: [`docs/features/`](docs/features/) - 🚀 自动切换、配置参数、死锁预防
- **故障排除**: [`docs/troubleshooting/`](docs/troubleshooting/) - 🐛 日志说明、级别优化
- **API 接口**: [`docs/api/`](docs/api/) - 📡 ROS2 接口完整文档
- **需求分析**: [`docs/requirements/`](docs/requirements/) - 📋 网络需求详细分析
- **开发文档**: [`docs/development/`](docs/development/) - 💻 开发笔记和测试

## 🏗️ 项目结构

```
Network99/
├── src/                          # 源代码
│   ├── gen3_network_manager_core/    # 核心网络管理器
│   └── gen3_network_interfaces/      # 网络接口定义
├── install/                      # 构建输出
│   └── {arch}/Debug/             # 架构特定的构建文件
├── .vscode/                      # VSCode 配置
│   ├── launch.json               # 调试配置
│   └── tasks.json                # 任务配置
├── scripts/                      # 工具脚本
├── docs/                         # 文档
├── tests/                        # 测试套件
│   ├── run-all-tests.sh          # 测试主入口
│   ├── config-validation-test.sh # 配置验证测试
│   ├── debug-config-test.sh      # 调试配置测试
│   └── network-interface-test.cpp # 网络接口测试
└── *.sh                          # 便捷脚本
```

## 🔄 开发工作流

1. **修改代码**
2. **构建项目**: `./build.sh`
3. **运行测试**: `./tests/run-tests.sh`
4. **测试运行**: `./run_network_manager.sh`
5. **调试**: 在 VSCode 中按 `F5`
6. **完整验证**: `./tests/run-all-tests.sh`

## 📋 系统要求

- **操作系统**: Ubuntu 22.04 或兼容系统
- **ROS版本**: ROS2 Humble
- **编译器**: GCC 11+
- **调试器**: GDB
- **IDE**: VSCode（推荐）

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**配置完成！现在可以在任何支持的架构上无缝进行 ROS2 项目的开发和调试。** 🎉
