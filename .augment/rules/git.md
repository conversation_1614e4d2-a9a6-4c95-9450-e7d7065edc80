---
description: 
globs: 
alwaysApply: false
---
---
description:Git提交和版本控制规范
globs:[".git/*"寨接们恪焘螢傲腑乖湿.蟈ェ濉榪慷弧杬竖t"]
alwaysApply: false
---

# git规范

## 提交规范
git 提交记录样例:[type]: [description]。一个具体的例子，docs:更新 README 文件。

以下是 type 的枚举值:
- feat:新增功能
- fix:修复 bug
- docs:文档注释
- style:代码格式(不影响代码运行的变动
- refactor:重构、优化(既不增加新功能，也不是修复bug)
- perf:性能优化
- test:增加测试
- chore:构建过程或辅助工具的变动
- revert:回退
- build:打包
## 分支管理
- main/master:主分支，保持稳定可发布状态
- develop:开发分支，包含最新开发特性
- feature/*:功能分支，用于开发新功能
- bugfix/*:修复分支，用于修复bug
- release/*:发布分支，用于准备发布
## 重要原则
- **重要**:不要自动提交 qit 代码，除非有明确的提示
- 提交前确保代码通过所有测试
- 保持提交信息简洁明了，描述清楚变更内容


避免大型提交，尽量将变更分解为小的、相关的提交