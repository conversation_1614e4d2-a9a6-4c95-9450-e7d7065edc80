---
description: "globs: *.md"
globs: ["*.md","CHANGELOG.md","README.md"]
alwaysApply: false
type: "always_apply"---
# 文档规范

## README.md 规范

- 保持文档结构清晰，使用适当的Markdown标记
- **重要**:每次修改保留 README.md 中的二级目录"Cursor 历史下载链接"部分，不要进行删除
- 确保README包含以下部分:
  - 项目简介
  - 安装说明
  - 使用方法
  - 贡献指南(如适用)
  - 许可证信息
  - Cursor 历史下载链接(必须保留)

## CHANGELOG.md 规范
在要求更新CHANGELOG.md时，请按照以下格式进行更新:

```
## v1.0.0
新增功能:重置设备ID
修复bug:修复设备ID重置失败的问题
```

## 文档更新原则
- 保持文档与代码同步更新
- 使用简洁明了的语言
- 提供足够的示例和说明

- 确保文档格式一致