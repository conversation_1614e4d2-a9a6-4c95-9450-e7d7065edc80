#!/bin/bash

# launch.json 备份管理脚本
# 确保最多保留三个备份文件

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

BACKUP_DIR=".vscode"
MAX_BACKUPS=3

echo -e "${BLUE}=== launch.json 备份管理 ===${NC}"
echo

# 检查 .vscode 目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${RED}错误: .vscode 目录不存在${NC}"
    exit 1
fi

# 检查当前备份文件
echo -e "${BLUE}📋 当前备份文件:${NC}"
BACKUP_FILES=($(find "$BACKUP_DIR" -name "launch.json.backup*" -type f | sort -r))

if [ ${#BACKUP_FILES[@]} -eq 0 ]; then
    echo "  没有找到备份文件"
    exit 0
fi

# 显示当前备份文件
for i in "${!BACKUP_FILES[@]}"; do
    file="${BACKUP_FILES[$i]}"
    size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null)
    mtime=$(stat -c%Y "$file" 2>/dev/null || stat -f%m "$file" 2>/dev/null)
    date_str=$(date -d "@$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
    echo "  $((i+1)). $(basename "$file") (${size} bytes, $date_str)"
done

echo
echo -e "${BLUE}📊 备份统计:${NC}"
echo "  总备份数: ${#BACKUP_FILES[@]}"
echo "  最大保留数: $MAX_BACKUPS"

# 如果备份数量超过限制，删除多余的备份
if [ ${#BACKUP_FILES[@]} -gt $MAX_BACKUPS ]; then
    excess_count=$((${#BACKUP_FILES[@]} - $MAX_BACKUPS))
    echo -e "${YELLOW}⚠️  需要删除 $excess_count 个多余的备份文件${NC}"
    echo
    
    # 显示将要删除的文件
    echo -e "${RED}🗑️  将要删除的文件:${NC}"
    for ((i=$MAX_BACKUPS; i<${#BACKUP_FILES[@]}; i++)); do
        echo "  - $(basename "${BACKUP_FILES[$i]}")"
    done
    
    echo
    read -p "确认删除这些文件吗? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 删除多余的备份文件
        deleted_count=0
        for ((i=$MAX_BACKUPS; i<${#BACKUP_FILES[@]}; i++)); do
            if rm -f "${BACKUP_FILES[$i]}"; then
                echo -e "${GREEN}✅ 已删除: $(basename "${BACKUP_FILES[$i]}")${NC}"
                ((deleted_count++))
            else
                echo -e "${RED}❌ 删除失败: $(basename "${BACKUP_FILES[$i]}")${NC}"
            fi
        done
        
        echo
        echo -e "${GREEN}🎉 清理完成! 删除了 $deleted_count 个备份文件${NC}"
    else
        echo -e "${YELLOW}取消删除操作${NC}"
    fi
else
    echo -e "${GREEN}✅ 备份数量在限制范围内，无需清理${NC}"
fi

echo
echo -e "${BLUE}📋 最终备份文件列表:${NC}"
FINAL_BACKUPS=($(find "$BACKUP_DIR" -name "launch.json.backup*" -type f | sort -r))
for i in "${!FINAL_BACKUPS[@]}"; do
    file="${FINAL_BACKUPS[$i]}"
    echo "  $((i+1)). $(basename "$file")"
done

echo
echo -e "${BLUE}💡 使用建议:${NC}"
echo "  1. 定期运行此脚本清理备份文件"
echo "  2. 重要备份可以手动重命名保护"
echo "  3. 建议在修改 launch.json 前先备份"

echo
echo -e "${BLUE}🔧 备份命令:${NC}"
echo "  # 创建新备份"
echo "  cp .vscode/launch.json .vscode/launch.json.backup.\$(date +%Y%m%d_%H%M%S)"
echo "  # 运行清理"
echo "  ./scripts/manage_launch_backups.sh"
