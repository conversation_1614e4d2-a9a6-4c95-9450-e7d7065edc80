#!/bin/bash

# 测试VSCode调试配置
echo "=== 测试VSCode调试配置 ==="

# 检测当前架构
ARCH=$(uname -m)
echo "当前架构: $ARCH"

# 检查可执行文件是否存在
PROGRAM_PATH="./install/$ARCH/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node"
if [ -f "$PROGRAM_PATH" ]; then
    echo "✓ 可执行文件存在: $PROGRAM_PATH"
else
    echo "✗ 可执行文件不存在: $PROGRAM_PATH"
    echo "请先运行 ./build.sh 构建项目"
    exit 1
fi

# 检查库文件路径
LIB_PATHS=(
    "./install/$ARCH/Debug/lib"
    "./install/$ARCH/Debug/gen3_network_interfaces/lib"
    "./install/$ARCH/Debug/gen3_network_manager_core/lib"
)

for lib_path in "${LIB_PATHS[@]}"; do
    if [ -d "$lib_path" ]; then
        echo "✓ 库目录存在: $lib_path"
    else
        echo "✗ 库目录不存在: $lib_path"
    fi
done

# 检查ROS2日志目录
if [ -d "$HOME/.ros/log" ]; then
    echo "✓ ROS2日志目录存在: $HOME/.ros/log"
else
    echo "✗ ROS2日志目录不存在，正在创建..."
    mkdir -p "$HOME/.ros/log"
    echo "✓ 已创建ROS2日志目录"
fi

# 检查launch.json配置
if grep -q "install/$ARCH/Debug" .vscode/launch.json; then
    echo "✓ launch.json已配置正确的架构路径"
else
    echo "✗ launch.json架构路径需要更新"
    echo "运行 ./scripts/update_launch_config.sh 来更新配置"
fi

echo ""
echo "=== 配置检查完成 ==="
echo "现在可以在VSCode中使用调试功能了！"
