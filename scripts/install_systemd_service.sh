#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本目录的绝对路径
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PROJECT_DIR=$(dirname "$SCRIPT_DIR")

# 服务和安装目录配置
SERVICE_NAME="network_manager"
SERVICE_FILE="$SCRIPT_DIR/${SERVICE_NAME}.service"
SYSTEMD_SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"
INSTALL_DIR="/opt/network99"
BIN_DIR="$INSTALL_DIR/bin"
SRC_DIR="$INSTALL_DIR/src"
SHARE_DIR="$INSTALL_DIR/share"
RUN_SCRIPT="$BIN_DIR/run_systemd_network_manager.sh"
LAUNCH_SRC="$PROJECT_DIR/src/gen3_network_manager_core/launch/network_manager.launch.py"
LAUNCH_DEST_SRC="$SRC_DIR/gen3_network_manager_core/launch/network_manager.launch.py"
LAUNCH_DEST_SHARE="$SHARE_DIR/gen3_network_manager_core/launch/network_manager.launch.py"

# 确保脚本具有执行权限
function ensure_executable() {
    if [ ! -x "$1" ]; then
        echo -e "${YELLOW}设置脚本可执行权限: $1${NC}"
        chmod +x "$1"
    fi
}

# 显示帮助信息
function show_help() {
    echo -e "${BLUE}网络管理器 systemd 服务安装工具${NC}"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install      安装并启用服务"
    echo "  uninstall    停止并移除服务"
    echo "  start        启动服务"
    echo "  stop         停止服务"
    echo "  restart      重启服务"
    echo "  status       查看服务状态"
    echo "  logs         查看服务日志"
    echo "  deploy       仅部署文件到/opt目录（不安装服务）"
    echo ""
}

# 检查是否为root用户
function check_root() {
    if [ "$(id -u)" != "0" ]; then
        echo -e "${RED}错误: 此操作需要 root 权限${NC}"
        echo -e "请使用 sudo 运行此脚本: ${YELLOW}sudo $0 $1${NC}"
        exit 1
    fi
}

# 检查并杀死已存在的网络管理器进程
function check_and_kill_process() {
    echo -e "${BLUE}检查是否有网络管理器进程正在运行...${NC}"
    
    # 检查network_manager_node进程
    NODE_PIDS=$(pgrep -f "network_manager_node")
    if [ -n "$NODE_PIDS" ]; then
        echo -e "${YELLOW}发现网络管理器节点进程 (PID: $NODE_PIDS)，正在终止...${NC}"
        kill -15 $NODE_PIDS 2>/dev/null
        sleep 1
        
        # 检查进程是否仍然存在，如果存在则强制终止
        if pgrep -f "network_manager_node" >/dev/null; then
            echo -e "${YELLOW}进程未响应，强制终止...${NC}"
            kill -9 $NODE_PIDS 2>/dev/null
            sleep 1
        fi
        
        echo -e "${GREEN}✓ 网络管理器节点进程已终止${NC}"
    else
        echo -e "${GREEN}✓ 未发现网络管理器节点进程${NC}"
    fi
    
    # 检查ros2 launch进程
    LAUNCH_PIDS=$(pgrep -f "ros2 launch.*network_manager.launch.py")
    if [ -n "$LAUNCH_PIDS" ]; then
        echo -e "${YELLOW}发现网络管理器launch进程 (PID: $LAUNCH_PIDS)，正在终止...${NC}"
        kill -15 $LAUNCH_PIDS 2>/dev/null
        sleep 1
        
        # 检查进程是否仍然存在，如果存在则强制终止
        if pgrep -f "ros2 launch.*network_manager.launch.py" >/dev/null; then
            echo -e "${YELLOW}进程未响应，强制终止...${NC}"
            kill -9 $LAUNCH_PIDS 2>/dev/null
            sleep 1
        fi
        
        echo -e "${GREEN}✓ 网络管理器launch进程已终止${NC}"
    else
        echo -e "${GREEN}✓ 未发现网络管理器launch进程${NC}"
    fi
    
    # 如果存在systemd服务，尝试停止它
    if systemctl is-active --quiet ${SERVICE_NAME}.service; then
        echo -e "${YELLOW}发现网络管理器服务正在运行，正在停止...${NC}"
        systemctl stop ${SERVICE_NAME}.service
        sleep 2
        echo -e "${GREEN}✓ 网络管理器服务已停止${NC}"
    fi
}

# 部署文件到/opt目录
function deploy_files() {
    check_root "deploy"
    
    # 先检查并杀死已存在的进程
    check_and_kill_process
    
    echo -e "${BLUE}部署文件到 ${INSTALL_DIR}...${NC}"
    
    # 创建安装目录
    mkdir -p "$BIN_DIR"
    mkdir -p "$INSTALL_DIR/install"
    mkdir -p "$SRC_DIR/gen3_network_manager_core/launch"
    mkdir -p "$SHARE_DIR/gen3_network_manager_core/launch"
    
    # 复制可执行文件
    EXECUTABLE_SRC="$PROJECT_DIR/install/$(uname -m)/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node"
    if [ -f "$EXECUTABLE_SRC" ]; then
        cp "$EXECUTABLE_SRC" "$BIN_DIR/network_manager_node"
        chmod +x "$BIN_DIR/network_manager_node"
        echo -e "${GREEN}✓ 已复制网络管理器节点可执行文件${NC}"
    else
        echo -e "${RED}✗ 错误：未找到源可执行文件: $EXECUTABLE_SRC${NC}"
        echo -e "${YELLOW}请先运行 colcon build 构建项目${NC}"
        exit 1
    fi
    
    # 复制运行脚本
    cp "$SCRIPT_DIR/run_systemd_network_manager.sh" "$RUN_SCRIPT"
    chmod +x "$RUN_SCRIPT"
    echo -e "${GREEN}✓ 已复制运行脚本${NC}"
    
    # 复制launch文件
    if [ -f "$LAUNCH_SRC" ]; then
        # 复制到src目录
        cp "$LAUNCH_SRC" "$LAUNCH_DEST_SRC"
        echo -e "${GREEN}✓ 已复制launch文件到src目录${NC}"
        
        # 复制到share目录
        cp "$LAUNCH_SRC" "$LAUNCH_DEST_SHARE"
        echo -e "${GREEN}✓ 已复制launch文件到share目录${NC}"
        
        # 复制配置文件
        CONFIG_SRC="$PROJECT_DIR/src/gen3_network_manager_core/config"
        if [ -d "$CONFIG_SRC" ]; then
            mkdir -p "$SRC_DIR/gen3_network_manager_core/config"
            mkdir -p "$SHARE_DIR/gen3_network_manager_core/config"
            cp -r "$CONFIG_SRC/"* "$SRC_DIR/gen3_network_manager_core/config/"
            cp -r "$CONFIG_SRC/"* "$SHARE_DIR/gen3_network_manager_core/config/"
            echo -e "${GREEN}✓ 已复制配置文件${NC}"
        else
            echo -e "${YELLOW}警告：未找到配置目录: $CONFIG_SRC${NC}"
        fi
    else
        echo -e "${RED}✗ 错误：未找到launch文件: $LAUNCH_SRC${NC}"
        echo -e "${YELLOW}请确认launch文件路径${NC}"
        exit 1
    fi
    
    # 复制ROS2工作空间安装文件
    WORKSPACE_SETUP_SRC="$PROJECT_DIR/install/$(uname -m)/Debug/setup.bash"
    if [ -f "$WORKSPACE_SETUP_SRC" ]; then
        # 使用rsync避免"Text file busy"错误
        rsync -a --exclude="*.so*" "$PROJECT_DIR/install/$(uname -m)/Debug/" "$INSTALL_DIR/install/"
        # 对于可执行文件和库文件，使用特殊处理
        find "$PROJECT_DIR/install/$(uname -m)/Debug/" -name "*.so*" -o -type f -executable | while read file; do
            rel_path=${file#"$PROJECT_DIR/install/$(uname -m)/Debug/"}
            target_file="$INSTALL_DIR/install/$rel_path"
            target_dir=$(dirname "$target_file")
            mkdir -p "$target_dir"
            cp -f "$file" "$target_file" 2>/dev/null || {
                echo -e "${YELLOW}警告：无法直接复制 $file，尝试备用方法...${NC}"
                # 如果直接复制失败，先复制到临时文件，再移动
                temp_file="${target_file}.tmp"
                cp -f "$file" "$temp_file" && mv -f "$temp_file" "$target_file"
            }
        done
        echo -e "${GREEN}✓ 已复制ROS2工作空间安装文件${NC}"
    else
        echo -e "${RED}✗ 错误：未找到ROS2工作空间安装文件: $WORKSPACE_SETUP_SRC${NC}"
        echo -e "${YELLOW}请先运行 colcon build 构建项目${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}文件部署完成。${NC}"
}

# 安装服务
function install_service() {
    check_root "install"
    
    # 先部署文件
    deploy_files
    
    echo -e "${BLUE}安装 ${SERVICE_NAME} 服务...${NC}"
    
    # 复制服务文件到 systemd 目录
    cp "$SERVICE_FILE" "$SYSTEMD_SERVICE_PATH"
    
    # 重载 systemd 配置
    systemctl daemon-reload
    
    # 启用并启动服务
    systemctl enable ${SERVICE_NAME}.service
    systemctl start ${SERVICE_NAME}.service
    
    echo -e "${GREEN}服务安装完成并已启动。${NC}"
    echo -e "查看状态: ${YELLOW}systemctl status ${SERVICE_NAME}${NC}"
    echo -e "查看日志: ${YELLOW}journalctl -u ${SERVICE_NAME} -f${NC}"
}

# 卸载服务
function uninstall_service() {
    check_root "uninstall"
    
    echo -e "${BLUE}卸载 ${SERVICE_NAME} 服务...${NC}"
    
    # 停止并禁用服务
    systemctl stop ${SERVICE_NAME}.service 2>/dev/null || true
    systemctl disable ${SERVICE_NAME}.service 2>/dev/null || true
    
    # 删除服务文件
    if [ -f "$SYSTEMD_SERVICE_PATH" ]; then
        rm "$SYSTEMD_SERVICE_PATH"
        systemctl daemon-reload
        echo -e "${GREEN}服务已成功卸载。${NC}"
    else
        echo -e "${YELLOW}服务文件不存在，无需卸载。${NC}"
    fi
    
    # 检查并杀死可能仍在运行的进程
    check_and_kill_process
    
    # 询问是否删除安装目录
    read -p "是否删除安装目录 $INSTALL_DIR? [y/N] " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$INSTALL_DIR"
        echo -e "${GREEN}已删除安装目录。${NC}"
    fi
}

# 启动服务
function start_service() {
    check_root "start"
    # 检查并杀死可能存在的进程
    check_and_kill_process
    echo -e "${BLUE}启动 ${SERVICE_NAME} 服务...${NC}"
    systemctl start ${SERVICE_NAME}.service
    echo -e "${GREEN}服务已启动。${NC}"
}

# 停止服务
function stop_service() {
    check_root "stop"
    echo -e "${BLUE}停止 ${SERVICE_NAME} 服务...${NC}"
    systemctl stop ${SERVICE_NAME}.service
    # 检查并杀死可能仍在运行的进程
    check_and_kill_process
    echo -e "${GREEN}服务已停止。${NC}"
}

# 重启服务
function restart_service() {
    check_root "restart"
    echo -e "${BLUE}重启 ${SERVICE_NAME} 服务...${NC}"
    systemctl stop ${SERVICE_NAME}.service
    # 检查并杀死可能仍在运行的进程
    check_and_kill_process
    systemctl start ${SERVICE_NAME}.service
    echo -e "${GREEN}服务已重启。${NC}"
}

# 查看服务状态
function status_service() {
    systemctl status ${SERVICE_NAME}.service
}

# 查看服务日志
function view_logs() {
    echo -e "${BLUE}显示 ${SERVICE_NAME} 服务日志...${NC}"
    journalctl -u ${SERVICE_NAME} -f
}

# 主函数
case "$1" in
    install)
        install_service
        ;;
    uninstall)
        uninstall_service
        ;;
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        view_logs
        ;;
    deploy)
        deploy_files
        ;;
    *)
        show_help
        exit 1
        ;;
esac

exit 0 