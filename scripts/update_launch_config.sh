#!/bin/bash

# launch.json 架构配置更新脚本
# 自动检测架构并更新配置，同时管理备份

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== launch.json 架构配置更新 ===${NC}"

# 获取当前架构
ARCH=$(uname -m)
echo -e "${BLUE}🔍 检测到架构: ${GREEN}$ARCH${NC}"

# 检查 launch.json 是否存在
if [ ! -f ".vscode/launch.json" ]; then
    echo -e "${YELLOW}⚠️  .vscode/launch.json 文件不存在${NC}"
    exit 1
fi

echo

# 使用备份脚本创建备份
echo -e "${BLUE}📋 创建备份...${NC}"
if [ -f "scripts/backup_launch_json.sh" ]; then
    ./scripts/backup_launch_json.sh
else
    # 如果备份脚本不存在，使用简单备份
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    cp .vscode/launch.json ".vscode/launch.json.backup.$TIMESTAMP"
    echo -e "${GREEN}✅ 备份完成: launch.json.backup.$TIMESTAMP${NC}"
fi

echo

# 更新launch.json中的架构路径
echo -e "${BLUE}🔧 更新架构路径...${NC}"
sed -i "s|/install/[^/]*/Debug|/install/$ARCH/Debug|g" .vscode/launch.json
sed -i "s|/install/[^/]*/Release|/install/$ARCH/Release|g" .vscode/launch.json

echo -e "${GREEN}✅ 已更新 launch.json 中的架构路径为: $ARCH${NC}"

echo

# 验证更新结果
echo -e "${BLUE}🔍 验证更新结果:${NC}"
updated_paths=$(grep -n "install/$ARCH" .vscode/launch.json | head -3)
if [ -n "$updated_paths" ]; then
    echo "$updated_paths"
    echo -e "${GREEN}✅ 架构路径更新成功${NC}"
else
    echo -e "${YELLOW}⚠️  未找到更新的架构路径，可能配置文件格式已变更${NC}"
fi

echo
echo -e "${BLUE}💡 提示:${NC}"
echo "  1. 请重新加载 VSCode 以应用新配置"
echo "  2. 可以运行 ./scripts/restore_launch_json.sh 恢复备份"
echo "  3. 运行 ./scripts/manage_launch_backups.sh 管理备份文件"
