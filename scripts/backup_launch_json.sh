#!/bin/bash

# launch.json 自动备份脚本
# 在修改前自动创建备份并管理备份数量

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

LAUNCH_FILE=".vscode/launch.json"
BACKUP_DIR=".vscode"
MAX_BACKUPS=3

echo -e "${BLUE}=== launch.json 自动备份 ===${NC}"

# 检查 launch.json 是否存在
if [ ! -f "$LAUNCH_FILE" ]; then
    echo -e "${YELLOW}⚠️  launch.json 文件不存在，无需备份${NC}"
    exit 0
fi

# 生成备份文件名（带时间戳）
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/launch.json.backup.${TIMESTAMP}"

# 创建备份
echo -e "${BLUE}📋 创建备份:${NC}"
if cp "$LAUNCH_FILE" "$BACKUP_FILE"; then
    echo -e "${GREEN}✅ 备份成功: $(basename "$BACKUP_FILE")${NC}"
    
    # 显示备份文件信息
    size=$(stat -c%s "$BACKUP_FILE" 2>/dev/null || stat -f%z "$BACKUP_FILE" 2>/dev/null)
    echo "   文件大小: ${size} bytes"
    echo "   备份时间: $(date)"
else
    echo -e "${RED}❌ 备份失败${NC}"
    exit 1
fi

echo

# 自动清理多余的备份
echo -e "${BLUE}🧹 自动清理多余备份:${NC}"
BACKUP_FILES=($(find "$BACKUP_DIR" -name "launch.json.backup.*" -type f | sort -r))

if [ ${#BACKUP_FILES[@]} -gt $MAX_BACKUPS ]; then
    excess_count=$((${#BACKUP_FILES[@]} - $MAX_BACKUPS))
    echo "   发现 ${#BACKUP_FILES[@]} 个备份文件，需要删除 $excess_count 个"
    
    # 删除多余的备份文件（保留最新的3个）
    deleted_count=0
    for ((i=$MAX_BACKUPS; i<${#BACKUP_FILES[@]}; i++)); do
        if rm -f "${BACKUP_FILES[$i]}"; then
            echo -e "${GREEN}   ✅ 已删除: $(basename "${BACKUP_FILES[$i]}")${NC}"
            ((deleted_count++))
        fi
    done
    
    echo -e "${GREEN}   🎉 清理完成! 删除了 $deleted_count 个旧备份${NC}"
else
    echo -e "${GREEN}   ✅ 备份数量正常，无需清理${NC}"
fi

echo

# 显示当前备份列表
echo -e "${BLUE}📋 当前备份文件:${NC}"
CURRENT_BACKUPS=($(find "$BACKUP_DIR" -name "launch.json.backup.*" -type f | sort -r))
for i in "${!CURRENT_BACKUPS[@]}"; do
    file="${CURRENT_BACKUPS[$i]}"
    mtime=$(stat -c%Y "$file" 2>/dev/null || stat -f%m "$file" 2>/dev/null)
    date_str=$(date -d "@$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
    echo "   $((i+1)). $(basename "$file") ($date_str)"
done

echo
echo -e "${GREEN}✅ 备份完成! 现在可以安全地修改 launch.json${NC}"

# 提供恢复命令提示
echo
echo -e "${BLUE}💡 恢复命令:${NC}"
echo "   # 恢复最新备份"
echo "   cp \"${CURRENT_BACKUPS[0]}\" \"$LAUNCH_FILE\""
echo "   # 查看备份差异"
echo "   diff \"$LAUNCH_FILE\" \"${CURRENT_BACKUPS[0]}\""
