# ROS2 网络管理器 Systemd 服务

本目录包含将ROS2网络管理器设置为systemd服务的必要脚本，允许系统在启动时自动运行网络管理器，并提供标准的服务管理接口。

## 文件说明

- `network_manager.service`: systemd服务定义文件
- `run_systemd_network_manager.sh`: 服务运行脚本，负责设置ROS2环境并启动网络管理器节点
- `install_systemd_service.sh`: 服务安装和管理脚本

## 安装和使用说明

### 前提条件

- 需要sudo/root权限
- 已成功构建ROS2网络管理器项目（使用`colcon build`）
- 系统使用systemd作为服务管理器

### 安装服务

安装脚本会执行以下操作：
1. 将必要文件部署到 `/opt/network99` 目录
2. 安装systemd服务
3. 启用服务使其在系统启动时自动运行

```bash
sudo ./scripts/install_systemd_service.sh install
```

### 仅部署文件

如果您只想部署文件到 `/opt/network99` 目录而不安装服务：

```bash
sudo ./scripts/install_systemd_service.sh deploy
```

### 文件部署结构

安装后的文件结构如下：

```
/opt/network99/
├── bin/
│   ├── network_manager_node         # 可执行文件
│   └── run_systemd_network_manager.sh  # 运行脚本
├── src/
│   └── gen3_network_manager_core/
│       ├── launch/
│       │   └── network_manager.launch.py  # launch文件
│       └── config/
│           └── network_config.yaml  # 配置文件
├── share/
│   └── gen3_network_manager_core/
│       ├── launch/
│       │   └── network_manager.launch.py  # launch文件（ROS2标准位置）
│       └── config/
│           └── network_config.yaml  # 配置文件
└── install/                         # ROS2工作空间安装文件
    ├── setup.bash
    └── ...
```

### 启动方式

服务使用ROS2 launch文件启动网络管理器，而不是直接运行可执行文件。这允许：
- 配置环境变量
- 设置日志格式
- 加载参数
- 配置节点映射

具体使用的launch文件是：`/mine/note/Code/ROS/Network99/src/gen3_network_manager_core/launch/network_manager.launch.py`

### 管理服务

脚本提供了多种管理选项：

```bash
# 启动服务
sudo ./scripts/install_systemd_service.sh start

# 停止服务
sudo ./scripts/install_systemd_service.sh stop

# 重启服务
sudo ./scripts/install_systemd_service.sh restart

# 查看服务状态
./scripts/install_systemd_service.sh status

# 查看服务日志
./scripts/install_systemd_service.sh logs
```

### 卸载服务

要完全卸载服务：

```bash
sudo ./scripts/install_systemd_service.sh uninstall
```

此命令将停止并禁用服务，删除服务文件，并询问是否删除 `/opt/network99` 安装目录。

## 日志

服务日志同时写入：
- systemd日志系统（可通过`journalctl -u network_manager`查看）
- `/var/log/network_manager.log`文件

## 故障排除

如果服务启动失败，请检查：

1. ROS2环境是否正确安装
2. 网络管理器是否已成功构建
3. launch文件是否正确复制
4. 配置文件是否存在
5. 权限问题（日志文件写入权限等）

检查服务状态和日志可以帮助诊断问题：

```bash
sudo systemctl status network_manager
sudo journalctl -u network_manager -f
``` 