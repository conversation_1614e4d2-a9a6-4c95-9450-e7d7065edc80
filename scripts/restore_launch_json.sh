#!/bin/bash

# launch.json 恢复脚本
# 从备份文件恢复 launch.json

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

LAUNCH_FILE=".vscode/launch.json"
BACKUP_DIR=".vscode"

echo -e "${BLUE}=== launch.json 恢复工具 ===${NC}"
echo

# 查找所有备份文件
BACKUP_FILES=($(find "$BACKUP_DIR" -name "launch.json.backup.*" -type f | sort -r))

if [ ${#BACKUP_FILES[@]} -eq 0 ]; then
    echo -e "${RED}❌ 没有找到备份文件${NC}"
    exit 1
fi

# 显示可用的备份文件
echo -e "${BLUE}📋 可用的备份文件:${NC}"
for i in "${!BACKUP_FILES[@]}"; do
    file="${BACKUP_FILES[$i]}"
    size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null)
    mtime=$(stat -c%Y "$file" 2>/dev/null || stat -f%m "$file" 2>/dev/null)
    date_str=$(date -d "@$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "$mtime" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
    echo "  $((i+1)). $(basename "$file")"
    echo "     时间: $date_str"
    echo "     大小: ${size} bytes"
    echo
done

# 如果没有参数，提示用户选择
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}请选择要恢复的备份文件 (1-${#BACKUP_FILES[@]}), 或按 Ctrl+C 取消:${NC}"
    read -p "选择 [1]: " choice
    
    # 默认选择第一个（最新的）
    if [ -z "$choice" ]; then
        choice=1
    fi
    
    # 验证选择
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 1 ] || [ "$choice" -gt ${#BACKUP_FILES[@]} ]; then
        echo -e "${RED}❌ 无效的选择${NC}"
        exit 1
    fi
    
    selected_backup="${BACKUP_FILES[$((choice-1))]}"
else
    # 使用命令行参数指定的备份文件
    backup_name="$1"
    selected_backup="$BACKUP_DIR/launch.json.backup.$backup_name"
    
    if [ ! -f "$selected_backup" ]; then
        echo -e "${RED}❌ 备份文件不存在: $selected_backup${NC}"
        exit 1
    fi
fi

echo -e "${BLUE}📋 选择的备份文件:${NC}"
echo "  文件: $(basename "$selected_backup")"

# 显示当前文件和备份文件的差异
if [ -f "$LAUNCH_FILE" ]; then
    echo
    echo -e "${BLUE}🔍 文件差异预览:${NC}"
    if diff -u "$LAUNCH_FILE" "$selected_backup" > /dev/null; then
        echo -e "${GREEN}  ✅ 文件内容相同，无需恢复${NC}"
        exit 0
    else
        echo "  当前文件与备份文件存在差异:"
        diff -u "$LAUNCH_FILE" "$selected_backup" | head -20
        if [ $(diff -u "$LAUNCH_FILE" "$selected_backup" | wc -l) -gt 20 ]; then
            echo "  ... (差异较多，仅显示前20行)"
        fi
    fi
fi

echo
echo -e "${YELLOW}⚠️  这将覆盖当前的 launch.json 文件${NC}"
read -p "确认恢复吗? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    # 在恢复前先备份当前文件
    if [ -f "$LAUNCH_FILE" ]; then
        current_backup="$BACKUP_DIR/launch.json.backup.before_restore_$(date +%Y%m%d_%H%M%S)"
        cp "$LAUNCH_FILE" "$current_backup"
        echo -e "${GREEN}✅ 当前文件已备份为: $(basename "$current_backup")${NC}"
    fi
    
    # 执行恢复
    if cp "$selected_backup" "$LAUNCH_FILE"; then
        echo -e "${GREEN}🎉 恢复成功!${NC}"
        echo "  已从 $(basename "$selected_backup") 恢复 launch.json"
        
        # 显示恢复后的文件信息
        size=$(stat -c%s "$LAUNCH_FILE" 2>/dev/null || stat -f%z "$LAUNCH_FILE" 2>/dev/null)
        echo "  文件大小: ${size} bytes"
        echo "  恢复时间: $(date)"
    else
        echo -e "${RED}❌ 恢复失败${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}取消恢复操作${NC}"
fi

echo
echo -e "${BLUE}💡 提示:${NC}"
echo "  1. 恢复后请重新加载 VSCode 配置"
echo "  2. 可以运行 ./scripts/manage_launch_backups.sh 清理备份"
echo "  3. 建议测试调试配置是否正常工作"
