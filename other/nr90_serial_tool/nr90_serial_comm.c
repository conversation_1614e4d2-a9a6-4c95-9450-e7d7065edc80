#include "nr90_serial_comm.h"
#include <stdarg.h>
#include <strings.h>

// 兼容性定义
#ifndef CRTSCTS
#define CRTSCTS 020000000000
#endif

// 全局变量定义
volatile sig_atomic_t g_interrupted = 0;
log_level_t g_log_level = LOG_INFO;

// 颜色定义用于日志输出
#define COLOR_RED     "\033[0;31m"
#define COLOR_YELLOW  "\033[1;33m"
#define COLOR_GREEN   "\033[0;32m"
#define COLOR_BLUE    "\033[0;34m"
#define COLOR_RESET   "\033[0m"

// 内部函数声明
static char* nr90_strcasestr(const char* haystack, const char* needle);

// 预定义的AT命令列表
static const nr90_at_command_t nr90_at_commands[] = {
    {"AT", "基本AT命令测试", 3, 1},
    {"ATI", "查询设备信息", 3, 1},
    {"AT+CIMI", "查询IMSI", 3, 0},
    {"AT+COPS?", "查询当前网络运营商", 5, 1},
    {"AT+CREG?", "查询网络注册状态", 3, 1},
    {"AT+CGREG?", "查询GPRS网络注册状态", 3, 0},
    {"AT+CEREG?", "查询EPS网络注册状态", 3, 1},
    {"AT+C5GREG?", "查询5G网络注册状态", 5, 1},
    {"AT+CPSI?", "查询系统信息", 5, 1},
    {"AT+QNWINFO", "查询网络信息", 5, 0},
    {"AT+QENG=\"servingcell\"", "查询服务小区信息", 8, 0},
    {NULL, NULL, 0, 0} // 结束标记
};

// 日志输出函数
void nr90_log(log_level_t level, const char* format, ...) {
    if (level > g_log_level) return;
    
    const char* level_str;
    const char* color;
    
    switch (level) {
        case LOG_ERROR:
            level_str = "ERROR";
            color = COLOR_RED;
            break;
        case LOG_WARNING:
            level_str = "WARNING";
            color = COLOR_YELLOW;
            break;
        case LOG_INFO:
            level_str = "INFO";
            color = COLOR_BLUE;
            break;
        case LOG_DEBUG:
            level_str = "DEBUG";
            color = COLOR_GREEN;
            break;
        default:
            level_str = "UNKNOWN";
            color = COLOR_RESET;
    }
    
    // 获取当前时间
    time_t now;
    struct tm* timeinfo;
    char timestamp[64];
    
    time(&now);
    timeinfo = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", timeinfo);
    
    // 输出日志
    fprintf(stderr, "%s[%s]%s [%s] ", color, level_str, COLOR_RESET, timestamp);
    
    va_list args;
    va_start(args, format);
    vfprintf(stderr, format, args);
    va_end(args);
    
    fprintf(stderr, "\n");
    fflush(stderr);
}

// 设置日志级别
void nr90_set_log_level(log_level_t level) {
    g_log_level = level;
}

// 错误码转字符串
const char* nr90_error_string(nr90_error_t error) {
    switch (error) {
        case NR90_SUCCESS:
            return "Success";
        case NR90_ERROR_DEVICE_OPEN:
            return "Device open failed";
        case NR90_ERROR_DEVICE_CONFIG:
            return "Device configuration failed";
        case NR90_ERROR_COMMAND_SEND:
            return "Command send failed";
        case NR90_ERROR_RESPONSE_TIMEOUT:
            return "Response timeout";
        case NR90_ERROR_RESPONSE_INVALID:
            return "Invalid response";
        case NR90_ERROR_DEVICE_NOT_READY:
            return "Device not ready";
        case NR90_ERROR_MEMORY_ALLOC:
            return "Memory allocation failed";
        case NR90_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        default:
            return "Unknown error";
    }
}

// 信号处理函数
void nr90_signal_handler(int sig) {
    g_interrupted = 1;
    nr90_log(LOG_WARNING, "收到信号 %d，准备安全退出...", sig);
}

// 设置信号处理
void nr90_setup_signal_handlers(void) {
    signal(SIGINT, nr90_signal_handler);
    signal(SIGTERM, nr90_signal_handler);
    signal(SIGHUP, nr90_signal_handler);
    signal(SIGQUIT, nr90_signal_handler);
}

// 设备初始化
nr90_error_t nr90_device_init(nr90_device_t* device, const char* device_path) {
    if (!device || !device_path) {
        return NR90_ERROR_INVALID_PARAM;
    }
    
    memset(device, 0, sizeof(nr90_device_t));
    strncpy(device->device_path, device_path, MAX_DEVICE_PATH - 1);
    device->device_path[MAX_DEVICE_PATH - 1] = '\0';
    device->fd = -1;
    device->is_configured = 0;
    device->baud_rate = DEFAULT_BAUD_RATE;
    device->log_level = LOG_INFO;
    
    nr90_log(LOG_INFO, "设备初始化: %s", device_path);
    return NR90_SUCCESS;
}

// 设备打开
nr90_error_t nr90_device_open(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }
    
    nr90_log(LOG_INFO, "正在打开设备: %s", device->device_path);
    
    // 打开串口设备
    device->fd = open(device->device_path, O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (device->fd < 0) {
        nr90_log(LOG_ERROR, "无法打开设备 %s: %s", device->device_path, strerror(errno));
        return NR90_ERROR_DEVICE_OPEN;
    }
    
    // 保存原始串口设置
    if (tcgetattr(device->fd, &device->original_termios) != 0) {
        nr90_log(LOG_WARNING, "无法获取原始串口设置: %s", strerror(errno));
    }
    
    nr90_log(LOG_INFO, "设备打开成功，文件描述符: %d", device->fd);
    return NR90_SUCCESS;
}

// 设备配置
nr90_error_t nr90_device_configure(nr90_device_t* device) {
    if (!device || device->fd < 0) {
        return NR90_ERROR_INVALID_PARAM;
    }
    
    nr90_log(LOG_INFO, "正在配置串口参数...");
    
    struct termios tty;
    memset(&tty, 0, sizeof(tty));
    
    // 获取当前设置
    if (tcgetattr(device->fd, &tty) != 0) {
        nr90_log(LOG_ERROR, "获取串口属性失败: %s", strerror(errno));
        return NR90_ERROR_DEVICE_CONFIG;
    }
    
    // 设置波特率
    cfsetospeed(&tty, device->baud_rate);
    cfsetispeed(&tty, device->baud_rate);
    
    // 设置数据位、停止位、校验位
    tty.c_cflag &= ~PARENB;        // 无校验位
    tty.c_cflag &= ~CSTOPB;        // 1个停止位
    tty.c_cflag &= ~CSIZE;         // 清除数据位设置
    tty.c_cflag |= CS8;            // 8个数据位
    tty.c_cflag &= ~CRTSCTS;       // 禁用硬件流控制
    tty.c_cflag |= CREAD | CLOCAL; // 启用接收，忽略调制解调器控制线
    
    // 设置输入模式
    tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 禁用软件流控制
    tty.c_iflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始输入
    
    // 设置输出模式
    tty.c_oflag &= ~OPOST; // 原始输出
    
    // 设置本地模式
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始模式
    
    // 设置超时
    tty.c_cc[VMIN] = 0;   // 最小字符数
    tty.c_cc[VTIME] = 10; // 超时时间(0.1秒单位)
    
    // 应用设置
    if (tcsetattr(device->fd, TCSANOW, &tty) != 0) {
        nr90_log(LOG_ERROR, "设置串口属性失败: %s", strerror(errno));
        return NR90_ERROR_DEVICE_CONFIG;
    }
    
    // 清空缓冲区
    tcflush(device->fd, TCIOFLUSH);
    
    device->is_configured = 1;
    nr90_log(LOG_INFO, "串口配置完成 (115200-8-N-1)");

    return NR90_SUCCESS;
}

// 发送命令
nr90_error_t nr90_send_command(nr90_device_t* device, const char* command) {
    if (!device || !command || device->fd < 0) {
        return NR90_ERROR_INVALID_PARAM;
    }

    char cmd_buffer[MAX_COMMAND_LEN + 4];
    int cmd_len = snprintf(cmd_buffer, sizeof(cmd_buffer), "%s\r\n", command);

    if (cmd_len >= sizeof(cmd_buffer)) {
        nr90_log(LOG_ERROR, "命令太长: %s", command);
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_DEBUG, "发送命令: %s", command);

    // 清空输入缓冲区
    tcflush(device->fd, TCIFLUSH);

    // 发送命令
    ssize_t bytes_written = write(device->fd, cmd_buffer, cmd_len);
    if (bytes_written != cmd_len) {
        nr90_log(LOG_ERROR, "命令发送失败: %s (写入 %zd/%d 字节)",
                 strerror(errno), bytes_written, cmd_len);
        return NR90_ERROR_COMMAND_SEND;
    }

    // 确保数据发送完成
    tcdrain(device->fd);

    nr90_log(LOG_DEBUG, "命令发送成功: %d 字节", cmd_len);
    return NR90_SUCCESS;
}

// 读取响应
nr90_error_t nr90_read_response(nr90_device_t* device, nr90_response_t* response, int timeout) {
    if (!device || !response || device->fd < 0) {
        return NR90_ERROR_INVALID_PARAM;
    }

    memset(response, 0, sizeof(nr90_response_t));
    time(&response->timestamp);

    fd_set read_fds;
    struct timeval tv;
    char buffer[256];
    int total_bytes = 0;
    time_t start_time = time(NULL);

    nr90_log(LOG_DEBUG, "等待响应 (超时: %d秒)...", timeout);

    while (total_bytes < MAX_RESPONSE_LEN - 1) {
        // 检查是否被中断
        if (g_interrupted) {
            nr90_log(LOG_WARNING, "读取响应被中断");
            return NR90_ERROR_RESPONSE_TIMEOUT;
        }

        // 检查超时
        if (time(NULL) - start_time >= timeout) {
            nr90_log(LOG_WARNING, "读取响应超时");
            return NR90_ERROR_RESPONSE_TIMEOUT;
        }

        FD_ZERO(&read_fds);
        FD_SET(device->fd, &read_fds);

        tv.tv_sec = 1;
        tv.tv_usec = 0;

        int select_result = select(device->fd + 1, &read_fds, NULL, NULL, &tv);

        if (select_result < 0) {
            if (errno == EINTR) continue; // 被信号中断，继续
            nr90_log(LOG_ERROR, "select失败: %s", strerror(errno));
            return NR90_ERROR_RESPONSE_TIMEOUT;
        }

        if (select_result == 0) {
            continue; // 超时，继续等待
        }

        if (FD_ISSET(device->fd, &read_fds)) {
            ssize_t bytes_read = read(device->fd, buffer, sizeof(buffer) - 1);

            if (bytes_read < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    continue; // 非阻塞读取，没有数据
                }
                nr90_log(LOG_ERROR, "读取失败: %s", strerror(errno));
                return NR90_ERROR_RESPONSE_TIMEOUT;
            }

            if (bytes_read == 0) {
                continue; // 没有数据
            }

            buffer[bytes_read] = '\0';

            // 将读取的数据添加到响应中
            if (total_bytes + bytes_read < MAX_RESPONSE_LEN - 1) {
                memcpy(response->response + total_bytes, buffer, bytes_read);
                total_bytes += bytes_read;
                response->response[total_bytes] = '\0';
                response->length = total_bytes;
            }

            // 检查是否收到完整响应
            if (strstr(response->response, "OK") ||
                strstr(response->response, "ERROR") ||
                strstr(response->response, "+CME ERROR") ||
                strstr(response->response, "+CMS ERROR")) {
                break;
            }
        }
    }

    // 分析响应
    response->is_ok = nr90_is_response_ok(response->response);
    response->is_error = nr90_is_response_error(response->response);

    if (response->length > 0) {
        nr90_log(LOG_DEBUG, "收到响应: %d 字节", response->length);
        return NR90_SUCCESS;
    } else {
        nr90_log(LOG_WARNING, "未收到有效响应");
        return NR90_ERROR_RESPONSE_INVALID;
    }
}

// 发送AT命令并获取响应
nr90_error_t nr90_send_at_command(nr90_device_t* device, const char* command,
                                  nr90_response_t* response, int timeout) {
    if (!device || !command || !response) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "执行AT命令: %s", command);

    // 发送命令
    nr90_error_t result = nr90_send_command(device, command);
    if (result != NR90_SUCCESS) {
        return result;
    }

    // 等待一小段时间让设备处理命令
    usleep(100000); // 100ms

    // 读取响应
    result = nr90_read_response(device, response, timeout);
    if (result != NR90_SUCCESS) {
        return result;
    }

    // 输出响应信息
    if (response->is_ok) {
        nr90_log(LOG_INFO, "命令执行成功");
    } else if (response->is_error) {
        nr90_log(LOG_WARNING, "命令执行失败");
    } else {
        nr90_log(LOG_INFO, "收到响应");
    }

    return NR90_SUCCESS;
}

// 检查响应是否为OK
int nr90_is_response_ok(const char* response) {
    if (!response) return 0;
    return (strstr(response, "OK") != NULL);
}

// 检查响应是否为错误
int nr90_is_response_error(const char* response) {
    if (!response) return 0;
    return (strstr(response, "ERROR") != NULL ||
            strstr(response, "+CME ERROR") != NULL ||
            strstr(response, "+CMS ERROR") != NULL);
}

// 打印响应内容
void nr90_print_response(const nr90_response_t* response) {
    if (!response || response->length == 0) {
        printf("(空响应)\n");
        return;
    }

    printf("响应内容 (%d 字节):\n", response->length);
    printf("----------------------------------------\n");

    // 逐行打印响应，过滤控制字符
    const char* line_start = response->response;
    const char* line_end;

    while ((line_end = strchr(line_start, '\n')) != NULL) {
        int line_len = line_end - line_start;
        if (line_len > 0) {
            // 移除行末的\r
            if (line_len > 0 && line_start[line_len - 1] == '\r') {
                line_len--;
            }
            if (line_len > 0) {
                printf("  %.*s\n", line_len, line_start);
            }
        }
        line_start = line_end + 1;
    }

    // 处理最后一行（如果没有换行符结尾）
    if (*line_start != '\0') {
        int line_len = strlen(line_start);
        if (line_len > 0 && line_start[line_len - 1] == '\r') {
            line_len--;
        }
        if (line_len > 0) {
            printf("  %.*s\n", line_len, line_start);
        }
    }

    printf("----------------------------------------\n");
    printf("状态: %s\n", response->is_ok ? "成功" :
                        (response->is_error ? "错误" : "未知"));
}

// 设备关闭
nr90_error_t nr90_device_close(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    if (device->fd >= 0) {
        nr90_log(LOG_INFO, "正在关闭设备...");

        // 恢复原始串口设置
        if (device->is_configured) {
            tcsetattr(device->fd, TCSANOW, &device->original_termios);
        }

        close(device->fd);
        device->fd = -1;
        device->is_configured = 0;

        nr90_log(LOG_INFO, "设备已关闭");
    }

    return NR90_SUCCESS;
}

// 设备清理
void nr90_device_cleanup(nr90_device_t* device) {
    if (device) {
        nr90_device_close(device);
        memset(device, 0, sizeof(nr90_device_t));
        device->fd = -1;
    }
}

// 基本通信测试
nr90_error_t nr90_test_basic_communication(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "=== 开始基本通信测试 ===");

    nr90_response_t response;
    nr90_error_t result;

    // 测试基本AT命令
    result = nr90_send_at_command(device, "AT", &response, 3);
    if (result != NR90_SUCCESS) {
        nr90_log(LOG_ERROR, "基本AT命令测试失败: %s", nr90_error_string(result));
        return result;
    }

    if (!response.is_ok) {
        nr90_log(LOG_ERROR, "设备未响应OK");
        nr90_print_response(&response);
        return NR90_ERROR_DEVICE_NOT_READY;
    }

    nr90_log(LOG_INFO, "✓ 基本通信测试通过");
    return NR90_SUCCESS;
}

// 获取设备信息
nr90_error_t nr90_get_device_info(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "=== 获取设备信息 ===");

    nr90_response_t response;
    nr90_error_t result;

    // 获取设备信息
    result = nr90_send_at_command(device, "ATI", &response, 3);
    if (result == NR90_SUCCESS && response.length > 0) {
        printf("\n设备信息:\n");
        nr90_print_response(&response);
    }

    // 获取IMSI（如果可用）
    result = nr90_send_at_command(device, "AT+CIMI", &response, 3);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\nIMSI信息:\n");
        nr90_print_response(&response);
    }

    return NR90_SUCCESS;
}

// 检查网络状态
nr90_error_t nr90_check_network_status(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "=== 检查网络状态 ===");

    nr90_response_t response;
    nr90_error_t result;
    int network_registered = 0;

    // 检查网络运营商
    result = nr90_send_at_command(device, "AT+COPS?", &response, 5);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\n网络运营商信息:\n");
        nr90_print_response(&response);
    }

    // 检查网络注册状态
    result = nr90_send_at_command(device, "AT+CREG?", &response, 3);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\n网络注册状态:\n");
        nr90_print_response(&response);
        // 简单检查是否包含注册成功的状态码
        if (strstr(response.response, ",1") || strstr(response.response, ",5")) {
            network_registered = 1;
        }
    }

    // 检查EPS网络注册状态
    result = nr90_send_at_command(device, "AT+CEREG?", &response, 3);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\nEPS网络注册状态:\n");
        nr90_print_response(&response);
    }

    // 检查系统信息
    result = nr90_send_at_command(device, "AT+CPSI?", &response, 5);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\n系统信息:\n");
        nr90_print_response(&response);
    }

    if (network_registered) {
        nr90_log(LOG_INFO, "✓ 设备已注册到网络");
    } else {
        nr90_log(LOG_WARNING, "⚠ 设备可能未注册到网络");
    }

    return NR90_SUCCESS;
}

// 验证5G能力
nr90_error_t nr90_verify_5g_capability(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "=== 验证5G能力 ===");

    nr90_response_t response;
    nr90_error_t result;
    int has_5g_indicators = 0;

    // 检查5G网络注册状态
    result = nr90_send_at_command(device, "AT+C5GREG?", &response, 5);
    if (result == NR90_SUCCESS) {
        printf("\n5G网络注册状态:\n");
        nr90_print_response(&response);

        // 检查5G指示符
        const char* indicators[] = {"5G", "NR", "SA", "NSA", "n78", "n79", "n1", "n3", "n28", "n41", NULL};
        for (int i = 0; indicators[i] != NULL; i++) {
            if (nr90_strcasestr(response.response, indicators[i])) {
                nr90_log(LOG_INFO, "发现5G指示符: %s", indicators[i]);
                has_5g_indicators = 1;
            }
        }
    }

    // 查询网络信息（Quectel特定命令）
    result = nr90_send_at_command(device, "AT+QNWINFO", &response, 5);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\n网络信息 (QNWINFO):\n");
        nr90_print_response(&response);

        // 检查5G指示符
        const char* indicators[] = {"5G", "NR", "SA", "NSA", NULL};
        for (int i = 0; indicators[i] != NULL; i++) {
            if (nr90_strcasestr(response.response, indicators[i])) {
                nr90_log(LOG_INFO, "在QNWINFO中发现5G指示符: %s", indicators[i]);
                has_5g_indicators = 1;
            }
        }
    }

    // 查询服务小区信息
    result = nr90_send_at_command(device, "AT+QENG=\"servingcell\"", &response, 8);
    if (result == NR90_SUCCESS && response.is_ok) {
        printf("\n服务小区信息:\n");
        nr90_print_response(&response);

        // 检查5G指示符
        const char* indicators[] = {"5G", "NR", "SA", "NSA", NULL};
        for (int i = 0; indicators[i] != NULL; i++) {
            if (nr90_strcasestr(response.response, indicators[i])) {
                nr90_log(LOG_INFO, "在服务小区信息中发现5G指示符: %s", indicators[i]);
                has_5g_indicators = 1;
            }
        }
    }

    if (has_5g_indicators) {
        nr90_log(LOG_INFO, "✓ 检测到5G网络能力");
        return NR90_SUCCESS;
    } else {
        nr90_log(LOG_WARNING, "⚠ 未检测到明确的5G网络指示");
        return NR90_ERROR_RESPONSE_INVALID;
    }
}

// strcasestr函数实现（某些系统可能没有）
static char* nr90_strcasestr(const char* haystack, const char* needle) {
    if (!haystack || !needle) return NULL;

    size_t needle_len = strlen(needle);
    if (needle_len == 0) return (char*)haystack;

    for (const char* p = haystack; *p; p++) {
        if (strncasecmp(p, needle, needle_len) == 0) {
            return (char*)p;
        }
    }
    return NULL;
}

// 运行完整测试
nr90_error_t nr90_run_full_test(nr90_device_t* device) {
    if (!device) {
        return NR90_ERROR_INVALID_PARAM;
    }

    nr90_log(LOG_INFO, "=== 开始NR90设备完整测试 ===");
    nr90_log(LOG_INFO, "设备路径: %s", device->device_path);

    time_t start_time = time(NULL);
    int test_passed = 0;
    int test_total = 0;

    // 1. 基本通信测试
    test_total++;
    if (nr90_test_basic_communication(device) == NR90_SUCCESS) {
        test_passed++;
    }

    if (g_interrupted) {
        nr90_log(LOG_WARNING, "测试被中断");
        return NR90_ERROR_RESPONSE_TIMEOUT;
    }

    // 2. 获取设备信息
    test_total++;
    if (nr90_get_device_info(device) == NR90_SUCCESS) {
        test_passed++;
    }

    if (g_interrupted) {
        nr90_log(LOG_WARNING, "测试被中断");
        return NR90_ERROR_RESPONSE_TIMEOUT;
    }

    // 3. 检查网络状态
    test_total++;
    if (nr90_check_network_status(device) == NR90_SUCCESS) {
        test_passed++;
    }

    if (g_interrupted) {
        nr90_log(LOG_WARNING, "测试被中断");
        return NR90_ERROR_RESPONSE_TIMEOUT;
    }

    // 4. 验证5G能力
    test_total++;
    if (nr90_verify_5g_capability(device) == NR90_SUCCESS) {
        test_passed++;
    }

    // 执行预定义的AT命令列表
    printf("\n=== 执行扩展AT命令测试 ===\n");
    int cmd_passed = 0;
    int cmd_total = 0;

    for (int i = 0; nr90_at_commands[i].command != NULL; i++) {
        if (g_interrupted) break;

        const nr90_at_command_t* cmd = &nr90_at_commands[i];
        cmd_total++;

        printf("\n执行命令 %d/%d: %s (%s)\n",
               i + 1, cmd_total, cmd->command, cmd->description);

        nr90_response_t response;
        nr90_error_t result = nr90_send_at_command(device, cmd->command,
                                                   &response, cmd->timeout);

        if (result == NR90_SUCCESS && (response.is_ok || response.length > 0)) {
            cmd_passed++;
            printf("✓ 成功\n");
            if (response.length > 0) {
                nr90_print_response(&response);
            }
        } else {
            printf("✗ 失败: %s\n", nr90_error_string(result));
            if (cmd->is_critical) {
                nr90_log(LOG_WARNING, "关键命令失败: %s", cmd->command);
            }
        }

        // 命令间延时
        usleep(500000); // 500ms
    }

    // 输出测试结果
    time_t end_time = time(NULL);
    int duration = (int)(end_time - start_time);

    printf("\n");
    nr90_log(LOG_INFO, "=== NR90设备测试完成 ===");
    nr90_log(LOG_INFO, "测试耗时: %d 秒", duration);
    nr90_log(LOG_INFO, "基本测试: %d/%d 通过", test_passed, test_total);
    nr90_log(LOG_INFO, "AT命令测试: %d/%d 成功", cmd_passed, cmd_total);

    if (test_passed == test_total && cmd_passed > 0) {
        nr90_log(LOG_INFO, "✓ 设备通信正常，NR90设备工作正常");
        return NR90_SUCCESS;
    } else if (test_passed > 0) {
        nr90_log(LOG_WARNING, "⚠ 设备部分功能正常，可能存在问题");
        return NR90_ERROR_DEVICE_NOT_READY;
    } else {
        nr90_log(LOG_ERROR, "✗ 设备通信失败，请检查连接和配置");
        return NR90_ERROR_DEVICE_NOT_READY;
    }
}
