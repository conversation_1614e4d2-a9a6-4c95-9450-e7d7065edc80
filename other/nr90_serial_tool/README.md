# NR90 5G设备串口通信工具

这是一个专门用于NR90 5G模组的串口通信测试工具，通过AT命令验证设备通信是否正常。

## 功能特性

- **完整的串口通信支持**: 自动配置ttyUSB设备参数（115200-8-N-1）
- **AT命令通信**: 支持标准AT命令集和5G特定命令
- **设备状态检测**: 全面检测网络注册状态、5G能力等
- **错误处理**: 完善的错误处理和超时机制
- **信号处理**: 支持Ctrl+C安全中断
- **详细日志**: 多级别日志输出，便于调试
- **灵活配置**: 支持自定义设备路径、波特率等参数

## 编译要求

- GCC编译器
- Linux系统（支持termios）
- 标准C库

## 编译安装

### 快速编译
```bash
make
```

### 编译选项
```bash
make all        # 编译程序
make clean      # 清理编译文件
make install    # 安装到系统 (/usr/local/bin)
make uninstall  # 从系统卸载
```

### 代码检查（可选）
```bash
make check      # 需要安装cppcheck
make format     # 需要安装clang-format
```

## 使用方法

### 基本用法
```bash
# 使用默认设备进行完整测试
./nr90_test

# 指定设备路径
./nr90_test -d /dev/ttyUSB1

# 启用详细输出
./nr90_test -v

# 静默模式（仅显示错误）
./nr90_test -q
```

### 高级用法
```bash
# 执行单个AT命令
./nr90_test -c "AT+COPS?"

# 自定义波特率和超时
./nr90_test -b 9600 -t 10

# 列出可用的ttyUSB设备
./nr90_test -l
```

### 命令行选项

| 选项 | 长选项 | 参数 | 说明 |
|------|--------|------|------|
| -d | --device | PATH | 指定ttyUSB设备路径 |
| -b | --baud | RATE | 指定波特率 |
| -t | --timeout | SEC | 指定命令超时时间 |
| -v | --verbose | - | 启用详细输出 |
| -q | --quiet | - | 静默模式 |
| -c | --command | CMD | 执行单个AT命令 |
| -l | --list | - | 列出可用设备 |
| -h | --help | - | 显示帮助信息 |

## 测试内容

程序会执行以下测试：

### 1. 基本通信测试
- AT命令响应测试
- 设备连接验证

### 2. 设备信息获取
- 设备型号和版本信息
- IMSI信息（如果可用）

### 3. 网络状态检查
- 网络运营商信息
- 网络注册状态
- EPS网络状态
- 系统信息

### 4. 5G能力验证
- 5G网络注册状态
- 5G指示符检测
- 服务小区信息

### 5. 扩展AT命令测试
执行预定义的AT命令列表，包括：
- `AT` - 基本测试
- `ATI` - 设备信息
- `AT+COPS?` - 网络运营商
- `AT+CREG?` - 网络注册
- `AT+CEREG?` - EPS注册
- `AT+C5GREG?` - 5G注册
- `AT+CPSI?` - 系统信息
- `AT+QNWINFO` - 网络信息
- `AT+QENG="servingcell"` - 小区信息

## 设备权限

如果遇到设备权限问题，可以：

```bash
# 将用户添加到dialout组
sudo usermod -a -G dialout $USER

# 重新登录或使用
newgrp dialout

# 或者临时修改设备权限
sudo chmod 666 /dev/ttyUSB0
```

## 返回值

| 返回值 | 说明 |
|--------|------|
| 0 | 测试成功 |
| 1 | 测试失败或设备错误 |
| 2 | 参数错误 |
| 130 | 被信号中断 |

## 故障排除

### 常见问题

1. **设备打开失败**
   - 检查设备是否存在：`ls -la /dev/ttyUSB*`
   - 检查设备权限
   - 确认设备未被其他程序占用

2. **AT命令无响应**
   - 检查波特率设置是否正确
   - 确认设备已正确连接
   - 尝试不同的ttyUSB设备

3. **5G指示符未检测到**
   - 设备可能未连接到5G网络
   - 检查SIM卡和网络覆盖
   - 某些命令可能不被设备支持

### 调试模式

使用 `-v` 选项启用详细输出，查看详细的通信过程：

```bash
./nr90_test -v -d /dev/ttyUSB0
```

## 开发信息

### 文件结构
```
├── main.c              # 主程序入口
├── nr90_serial_comm.h  # 头文件定义
├── nr90_serial_comm.c  # 核心通信实现
├── Makefile           # 编译配置
└── README.md          # 说明文档
```

### API接口
主要的API函数包括：
- `nr90_device_init()` - 设备初始化
- `nr90_device_open()` - 打开设备
- `nr90_device_configure()` - 配置串口
- `nr90_send_at_command()` - 发送AT命令
- `nr90_run_full_test()` - 运行完整测试

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交问题报告和改进建议。
