import yaml
import os

config_path = "src/gen3_network_manager_core/config/network_config.yaml"
print(f"尝试读取配置文件: {config_path}")

if os.path.exists(config_path):
    print(f"配置文件存在，正在读取...")
    with open(config_path, 'r') as f:
        config_data = yaml.safe_load(f)
        print(f"YAML内容: {config_data}")
        if config_data and 'gen3_network_manager' in config_data and 'ros__parameters' in config_data['gen3_network_manager']:
            params = config_data['gen3_network_manager']['ros__parameters']
            if 'log_level' in params:
                log_level = params['log_level']
                print(f"日志级别: {log_level}")
            else:
                print("未找到log_level参数")
        else:
            print("配置文件格式无效")
else:
    print("配置文件不存在")
